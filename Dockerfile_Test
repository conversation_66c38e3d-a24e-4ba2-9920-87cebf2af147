FROM python:3.9


RUN apt-get update && \ 
    apt-get install -y --no-install-recommends curl ca-certificates lsb-release gnupg apt-utils wget unzip git make perl vim && \
    rm -rf /var/lib/apt/lists/*
    
RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash

RUN git clone --depth 1 https://github.com/darold/pgFormatter.git /pgFormatter && rm -rf /pgFormatter/.git
RUN cd /pgFormatter \ 
    && perl Makefile.PL \
    && make \
    && make install
  
    
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

COPY . /app
WORKDIR /app

ARG MIG
ARG MIG_ID
ARG STG_KEY
ARG STG_ACC
ARG STG_SRC
ARG STG_PATH_CONVERSIONFILES
ARG STG_PATH_DYNAMICRULES

ARG GID
ARG UID

ENV UID=${UID} \
    GID=${GID}

RUN echo "MIG Name: $MIG" && \ 
    echo "MIG ID: $MIG_ID" && \
    echo "GID: $GID" && \
    echo "UID: $UID"

RUN echo 'az storage blob download-batch -d . --pattern "$STG_PATH_CONVERSIONFILES" -s "$STG_SRC" --account-name "$STG_ACC" --account-key "$STG_KEY"' > command.sh

RUN sh command.sh
RUN echo 'az storage blob download-batch -d . --pattern "$STG_PATH_DYNAMICRULES" -s "$STG_SRC" --account-name "$STG_ACC" --account-key "$STG_KEY"' > command.sh

RUN sh command.sh

RUN echo "MIG Name: $MIG" && \ 
    echo "MIG ID: $MIG_ID"

RUN if [ "$MIG" = "Oracle_Postgres14" ] || [ "$MIG" = "Oracle_Oracle" ] || [ "$MIG" = "Oracle_Fabrics" ] || [ "$MIG" = "Oracle_MongoDB" ] || [ "$MIG" = "DAL_DOTNET_Oracle_Postgres" ] || [ "$MIG" = "Oracle_SQL" ] || [ "$MIG" = "DynamoDB_CosmosNosql" ] || [ "$MIG" = "DynamoDB_Postgres" ] || [ "$MIG" = "Sybase_SQL" ]; then \ 
        mkdir -p /opt/oracle && \
        cd /opt/oracle && \
        apt-get update && \
        apt-get install -y libaio1 wget unzip && \
        wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip && \
        unzip instantclient-basiclite-linuxx64.zip && \
        rm -f instantclient-basiclite-linuxx64.zip && \
        cd /opt/oracle/instantclient* && \
        rm -f *jdbc* *occi* *mysql* *README *jar uidrvci genezi adrci && \
        echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && \
        ldconfig; \
    fi

ENV LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:"/usr/lib/x86_64-linux-gnu"

RUN if [ "$MIG" = "Oracle_Postgres14" ] || [ "$MIG" = "DB2_Postgres" ] || [ "$MIG" = "DynamoDB_Postgres" ] || [ "$MIG" = "MSSQL_Postgres" ] || [ "$MIG" = "Postgres_Postgres" ]; then \
        apt-get update && \
        apt-get install -y libaio1 wget gnupg2 lsb-release && \
        echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -c | awk '{print $2}')-pgdg main" | tee /etc/apt/sources.list.d/pgdg.list && \
        wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - && \
        apt-get update && \
        apt-get install -y postgresql-client libpq-dev libpq5 && \
        ldconfig; \
    fi

RUN if [ "$MIG" = "Oracle_SQL" ]; then \ 
        curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \ 
        && curl https://packages.microsoft.com/config/debian/10/prod.list > /etc/apt/sources.list.d/mssql-release.list \
        && apt-get update \
        && ACCEPT_EULA=Y apt-get install -y mssql-tools unixodbc-dev \
        && echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc \
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/* && \
        export PATH="$PATH:/opt/mssql-tools/bin" ; \
    elif [ "$MIG" = "MSSQL_Oracle" ]; then \
        apt-get update && apt-get install -y libaio1 wget unzip \
        && wget https://download.oracle.com/otn_software/linux/instantclient/2114000/instantclient-basiclite-linux.x64-*********.0dbru.zip \
        && wget https://download.oracle.com/otn_software/linux/instantclient/2114000/instantclient-sqlplus-linux.x64-*********.0dbru.zip \
        && unzip instantclient-basiclite-linux.x64-*********.0dbru.zip -d /opt/oracle \
        && unzip instantclient-sqlplus-linux.x64-*********.0dbru.zip -d /opt/oracle \
        && rm -f instantclient-basiclite-linux.x64-*********.0dbru.zip instantclient-sqlplus-linux.x64-*********.0dbru.zip \
        && cd /opt/oracle/instantclient_21_14 \
        && rm -f *jdbc* *occi* *mysql* *README *jar uidrvci genezi adrci \
        && echo /opt/oracle/instantclient_21_14 > /etc/ld.so.conf.d/oracle-instantclient.conf \
        && ldconfig && \
        export PATH="/opt/oracle/instantclient_21_14:$PATH" ; \
    elif [ "$MIG" = "DB2_Mysql" ]; then \
        apt-get install -y default-mysql-client && \
        wget -O v11.5fp11_linuxx64_dsdriver.tar.gz https://public.dhe.ibm.com/ibmdl/export/pub/software/data/db2/drivers/odbc_cli/linuxx64_odbc_cli.tar.gz && \
        tar -xzvf v11.5fp11_linuxx64_dsdriver.tar.gz && \
        mkdir -p /opt/ibm && \
        mv ./clidriver /opt/ibm && \
        rm v11.5fp11_linuxx64_dsdriver.tar.gz && \
        export IBM_DB_HOME=/opt/ibm && \
        export LD_LIBRARY_PATH=/opt/ibm/lib64:$LD_LIBRARY_PATH && \
        ldconfig && \
        export PATH=/opt/ibm/clidriver/bin:$PATH; \
    elif [ "$MIG" = "DB2_Postgres" ]; then \
        wget -O v11.5fp11_linuxx64_dsdriver.tar.gz https://public.dhe.ibm.com/ibmdl/export/pub/software/data/db2/drivers/odbc_cli/linuxx64_odbc_cli.tar.gz && \
        tar -xzvf v11.5fp11_linuxx64_dsdriver.tar.gz && \
        mkdir -p /opt/ibm && \
        mv ./clidriver /opt/ibm && \
        rm v11.5fp11_linuxx64_dsdriver.tar.gz && \
        export IBM_DB_HOME=/opt/ibm && \
        export LD_LIBRARY_PATH=/opt/ibm/lib64:$LD_LIBRARY_PATH && \
        export PATH=/opt/ibm/clidriver/bin:$PATH; \
    elif [ "$MIG" = "Mariadb_Mysql" ]; then \
        apt-get update && \
        apt-get install -y software-properties-common dirmngr curl gnupg && \
        curl -LsS https://mariadb.org/mariadb_release_signing_key.asc | apt-key add - && \ 
        add-apt-repository 'deb [arch=amd64] https://mirror.23media.com/mariadb/repo/10.6/ubuntu focal main' && \ 
        apt-get update && \
        apt-get install -y mariadb-client mydumper default-mysql-client libmariadb-dev && \
        apt-get clean; \
    elif [ "$MIG" = "Synapse_Fabrics" ]; then \
        echo "Installing ODBC Driver 18 for SQL Server for $MIG..." && \
        apt-get update && \
        apt-get install -y wget gnupg && \
        wget -qO- https://packages.microsoft.com/keys/microsoft.asc | apt-key add - && \
        wget -q https://packages.microsoft.com/config/debian/11/prod.list -O /etc/apt/sources.list.d/mssql-release.list && \
        apt-get update && \
        ACCEPT_EULA=Y apt-get install -y msodbcsql18 unixodbc-dev && \
        echo "ODBC Driver 18 for SQL Server installed."; \
    elif [ "$MIG" = "Sybase_SQL" ]; then \
        apt-get update && apt-get install -y unixodbc-dev && \
        curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \ 
        && curl https://packages.microsoft.com/config/debian/10/prod.list > /etc/apt/sources.list.d/mssql-release.list \
        && apt-get update \
        && ACCEPT_EULA=Y apt-get install -y mssql-tools unixodbc-dev \
        && echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc \
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/* && \
        export PATH="$PATH:/opt/mssql-tools/bin" && \
        echo "[Sybase]" > /etc/odbcinst.ini && \
        echo "Driver=/opt/sap/DataAccess64/ODBC/lib/libsybdrvodb.so" >> /etc/odbcinst.ini && \
        echo "#!/bin/bash" > /app/sybase.sh && \
        echo "echo \"Installing Sybase Drivers ...\"" >> /app/sybase.sh && \
        echo "cd /app/Sybase_Driver_Liunx/" >> /app/sybase.sh && \
        echo "tar -xvzf linuxamd64164006.TGZ" >> /app/sybase.sh && \
        echo "./ebf30912/setup.bin -i silent -DRUN_SILENT=TRUE -DAGREE_TO_SAP_LICENSE=TRUE -DPRODUCTION_INSTALL=TRUE -DCHOSEN_INSTALL_FEATURE_LIST='fopen_client, fdblib, fodbcl' -DCHOSEN_INSTALL_SET=Custom" >> /app/sybase.sh && \
        echo "cd /opt/sap" >> /app/sybase.sh && \
        echo "source /opt/sap/SYBASE.sh" >> /app/sybase.sh && \
        chmod +x /app/sybase.sh && \
        bash /app/sybase.sh && \
        rm -rf /app/Sybase_Driver_Liunx/ ; \
    fi

WORKDIR /app
RUN pip install --upgrade setuptools
RUN pip install -r /app/Migrations/"$MIG"/requirements.txt

RUN find /app/Migrations -mindepth 1 -maxdepth 1 -not -name "$MIG" -exec rm -rf {} \;
RUN python3 pipeline_script.py -migid "$MIG_ID" -mig_name "$MIG"
RUN rm -rf Migrations_Pipeline_Prod Dockerfile_Prod command.sh command.txt pipeline_script.py

RUN if [ "${GID}" != "0" ]; then \ 
    groupadd --system --gid "${GID}" "app"; \
fi

RUN adduser --gid "${GID}" --disabled-password --gecos "" --uid "${UID}" appuser && chown -R appuser /app
USER appuser
CMD ["python", "redis_conversion.py"]