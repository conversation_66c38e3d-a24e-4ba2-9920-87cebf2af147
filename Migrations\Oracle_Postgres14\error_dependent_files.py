import os, sys
from import_file import import_file
import xml.etree.ElementTree as ET
from common_modules.api import api_authentication, decrypt_database_details

def error_dependent_files_trigger(task_name, project_id, migration_name, iteration_id, source_connection_id,
                                      schema_name, object_type, object_name, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
    print(f'Performing {task_name} process')
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        if not os.path.exists(working_directory_path):
            os.makedirs(working_directory_path)

        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'Error_ddl.xml'

        tree = ET.parse(xml_path)
        root = tree.getroot()
        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)
            project_folder = 'PRJ' + project_id + 'SRC'

            token_data = api_authentication()
            DDL_file_path = root_path + '/' + project_folder + '/' + str(
                iteration_id) + '/' + 'Error_DDL' + '/' + object_type.capitalize()

            if not os.path.exists(DDL_file_path):
                os.makedirs(DDL_file_path)

            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', source_connection_id)
            source_function_call = getattr(import_object, 'DB_connection')

            execute_function_call_list = getattr(import_object, 'execute_query')

            try:
                extraction_query_tag = root.find('Extraction_Tables_List')
                extraction_query = extraction_query_tag.text.strip() if extraction_query_tag is not None else ''
                source_connection, error = source_function_call(source_DB_details)
                extraction_query = extraction_query.replace('@schemaname', schema_name).replace('@name', object_name)
                source_object_output = execute_function_call_list(source_connection, extraction_query)
                if source_object_output:
                    Tables_list = [i[0] for i in source_object_output]
                    definition_query_tag = root.find('Definition')
                    tables_condition = str(Tables_list).strip().replace('[', '').replace(']', '')
                    try:
                        definition_query = definition_query_tag.text.strip() if definition_query_tag is not None else ''
                        definition_query = definition_query.replace('@name', tables_condition)
                        try:
                            definition_object_output = execute_function_call_list(source_connection, definition_query)
                            if definition_object_output:
                                definition_output = [i[0] for i in definition_object_output]

                                file_name = os.path.join(DDL_file_path, schema_name + '-' + object_name + '.sql')
                                with open(file_name, 'w', encoding='utf-8') as ddl_data:
                                    joining_all_statements = '\n\n'.join(definition_output)
                                    ddl_data.write(joining_all_statements)
                        except Exception as e:
                            print(f"Error occurred at DDl fetching of {schema_name}.{object_name} is {str(e)}")
                    except Exception as e:
                        print(f"Error occurred at List fetching of {schema_name}.{object_name} is {str(e)}")
            except Exception as e:
                print(f"Error occurred at Error_DDl of {schema_name}.{object_name} is {str(e)}")
        else:
                print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
    print(f'{task_name} Task Completed')
