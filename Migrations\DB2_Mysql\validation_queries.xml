<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
                <!--                <Type>-->
                <!--                    SELECT TYPE_NAME from SYSIBM.SQLUDTS-->
                <!--                    where TYPE_SCHEM = Upper('@schemaname')-->
                <!--                    order by TYPE_NAME-->
                <!--                </Type>-->
<!--                <Sequence>-->
<!--                    SELECT SEQSCHEMA ,SEQNAME,SEQID AS sequence_value-->
<!--                    FROM SYSCAT.SEQUENCES-->
<!--                    WHERE SEQSCHEMA = upper('quadrant')-->
<!--                    and SEQSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SY<PERSON>BMADM')-->
<!--                    ORDER BY SEQNAME;-->
<!--                </Sequence>new query commented-->
                <Table>
                    SELECT TABSCHEMA,TABNAME,TYPE FROM SYSCAT.tables A
                    WHERE NOT EXISTS (
                    SELECT 1
                    FROM SYSCAT.TABLES MV
                    WHERE MV.TABNAME = A.TABNAME
                    AND MV.TABSCHEMA = upper('@schemaname')
                    AND MV.TYPE = 'M'
                    )
                    AND A.TABSCHEMA = upper('@schemaname')
                    AND A.TABNAME NOT LIKE '%$%'
                    AND A.TYPE = 'T'
                    AND A.TABNAME NOT LIKE 'SYS%'
                    ORDER BY 1 ;
                </Table>
                <Primary_Key>
                    SELECT  OWNER,TABNAME,OWNER ||'-'|| TABNAME || '-' || col_list AS CONSTRAINT_DETAILS,
                    col_list||'-'||CONSTNAME AS col_list,'ENABLED' AS STATUS
                    FROM (
                    SELECT C.TABSCHEMA AS OWNER,
                    C.TABNAME,
                    C.CONSTNAME,
                    LISTAGG(CC.COLNAME, ', ') WITHIN GROUP (ORDER BY CC.COLSEQ) AS COL_LIST
                    FROM SYSCAT.TABCONST C
                    JOIN SYSCAT.KEYCOLUSE CC ON C.CONSTNAME = CC.CONSTNAME
                            AND C.TABSCHEMA = CC.TABSCHEMA
                            AND C.TABNAME = CC.TABNAME
                    WHERE C.TYPE = 'P' -- Primary Key constraints
                    AND C.TABSCHEMA = upper('@schemaname')
                    AND C.CONSTNAME NOT LIKE '%$%'
                    AND C.TABNAME NOT LIKE '%$%'
                    GROUP BY C.TABSCHEMA, C.TABNAME, C.CONSTNAME, C.ENFORCED
                    ) AS PRIMARY_KEY_CONSTRAINTS
                    ORDER BY OWNER, TABNAME;
                </Primary_Key>
                <Unique_Constraint>
                    SELECT c.TABSCHEMA,c.TABNAME,c.CONSTNAME AS "CONSTRAINT_INFO",
                    LISTAGG(cc.COLNAME, ', ') WITHIN GROUP (ORDER BY cc.COLSEQ) "COL_LIST",
                    CASE WHEN c.ENFORCED = 'Y' THEN 'ENABLED' ELSE 'DISABLED' END AS "STATUS"

                    FROM SYSCAT.TABCONST c
                    JOIN SYSCAT.KEYCOLUSE cc ON c.CONSTNAME = cc.CONSTNAME
                    AND c.TABSCHEMA = cc.TABSCHEMA
                    AND c.TABNAME = cc.TABNAME
                    WHERE c.TYPE = 'U'
                    AND c.TABSCHEMA = UPPER('@schemaname')
                    AND c.CONSTNAME NOT LIKE '%$%'
                    AND c.TABNAME NOT LIKE '%$%'
                    GROUP BY c.TABSCHEMA, c.TABNAME, c.CONSTNAME, c.ENFORCED
                </Unique_Constraint>
                <Foreign_Key>
                    WITH CTE AS (
                    SELECT
                    tc.tabname AS table_name,
                    tc.tabschema AS schema_name,
                    tc.constname AS constraint_name,
                    tc.enforced AS status,
                    CAST('ALTER TABLE ' || tc.tabschema || '.' || tc.tabname || ' ADD CONSTRAINT ' || tc.constname ||
                    ' FOREIGN KEY (' AS VARCHAR(500)) ||
                    CAST((SELECT LISTAGG(kc.colname, ',') WITHIN GROUP (ORDER BY kc.colseq)
                    FROM syscat.keycoluse kc
                    WHERE kc.constname = tc.constname
                    AND kc.tabschema = tc.tabschema) AS VARCHAR(500)) ||
                    CAST(') REFERENCES ' AS VARCHAR(50)) ||
                    CAST(ref.tabschema AS VARCHAR(128)) || '.' || CAST(ref.reftabname AS VARCHAR(128)) ||
                    '(' ||
                    CAST((SELECT LISTAGG(kc2.colname, ',') WITHIN GROUP (ORDER BY kc2.colseq)
                    FROM syscat.keycoluse kc2
                    WHERE kc2.constname = ref.refkeyname
                    AND kc2.tabschema = ref.reftabschema) AS VARCHAR(500)) || ')' ||
                    ';' AS alter_statement
                    FROM
                    syscat.tabconst tc
                    JOIN
                    syscat.references ref ON tc.constname = ref.constname AND tc.tabschema = ref.tabschema
                    WHERE
                    tc.tabschema = UPPER('@schemaname')
                    AND tc.constname NOT LIKE '%$%'
                    AND tc.tabname NOT LIKE '%$%'
                    AND tc.type = 'F'
                    )
                    SELECT DISTINCT
                    CTE.schema_name,
                    CTE.table_name , CTE.constraint_name AS constraint_identifier,
                    CTE.alter_statement,
                    CTE.status
                    FROM
                    CTE;
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT  C.TABSCHEMA,
                    upper(C.TABNAME) || '-' || upper(C.COLNAME) AS CONSTRAINT_NAME,
                    'ENABLED' AS STATUS
                    FROM
                    SYSCAT.COLUMNS C
                    JOIN
                    SYSCAT.TABLES T
                    ON
                    C.TABSCHEMA = T.TABSCHEMA
                    AND C.TABNAME = T.TABNAME
                    WHERE
                    C.NULLS = 'N'
                    AND C.TABSCHEMA = upper('@schemaname')
                    AND T.TYPE = 'T'
                    ORDER BY
                    C.TABSCHEMA,
                    C.TABNAME,
                    C.COLNAME;

                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT
                    TABSCHEMA,TABNAME , COLNAME AS table_column,DEFAULT
                    FROM
                    SYSCAT.COLUMNS
                    WHERE
                    DEFAULT IS NOT NULL
                    AND TABSCHEMA = upper('@schemaname')
                    AND TABNAME NOT LIKE '%$%'
                </Default_Constraint>
                <Check_Constraint>
                                SELECT
                        ac.tabschema,ac.tabname, ac.constname AS constraint_name_status,text AS search_condition,'ENABLED' AS status

                        FROM
                        syscat.checks ac
                        JOIN
                        syscat.colchecks acc
                        ON
                        ac.tabschema = acc.tabschema AND
                        ac.tabname = acc.tabname AND
                        ac.constname = acc.constname
                        WHERE
                        ac.type = 'C' AND
                        ac.tabschema = UPPER('@schemaname') AND
                        (ac.tabname || '.' || acc.colname) NOT IN (
                        SELECT DISTINCT
                        tabname || '.' || colname
                        FROM
                        syscat.columns
                        WHERE
                        nulls = 'N'
                        ) AND
                        ac.constname NOT LIKE '%$%' AND
                        ac.tabname NOT LIKE '%$%'
                        ORDER BY
                        ac.tabschema;
                </Check_Constraint>
                <!--                <Index>-->
                <!--                     SELECT INDSCHEMA,INDNAME FROM SYSCAT.indexes-->
                <!--						WHERE OWNER NOT IN ('SYSIBM')-->
                <!--						AND INDSCHEMA=nvl($INDSCHEMA,INDSCHEMA);-->
                <!--                </Index>-->

<!--                <View>-->
<!--                    SELECT VIEWSCHEMA,VIEWNAME FROM SYSCAT.VIEWS-->
<!--                    WHERE VIEWSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM')-->
<!--                    and VIEWSCHEMA = upper('@schemaname') ;-->
<!--                </View>-->
                <Datatype>
                    SELECT
                    T.TABSCHEMA AS "OWNER",
                    T.TABNAME AS "TABLE_NAME",
                    C.COLNAME AS "COLUMN_NAME",
                    C.TYPENAME AS "DATA_TYPE",
                    CASE
                    WHEN C.TYPENAME IN ('CHAR', 'VARCHAR', 'GRAPHIC', 'VARGRAPHIC','CHARACTER', 'SMALLINT', 'BIGINT', 'REAL') THEN '(' || C.LENGTH || ')'
                    WHEN C.TYPENAME IN ('DECIMAL', 'NUMERIC','INTEGER','DOUBLE') THEN '(' || C.LENGTH || ',' || C.SCALE || ')'
                    WHEN C.TYPENAME IN ('DATE', 'TIME', 'TIMESTAMP') THEN NULL
                    WHEN C.TYPENAME IN ('CLOB', 'BLOB', 'DBCLOB') THEN NULL
                    WHEN C.TYPENAME IN ('XML', 'ROWID', 'UUID') THEN NULL
                    ELSE NULL
                    END AS "COLUMN_SIZE",
                    C.COLNO + 1 AS "ORDINAL_POSITION",
                    T.TYPE AS "TABLE_TYPE"
                    FROM
                    SYSCAT.TABLES T
                    JOIN
                    SYSCAT.COLUMNS C
                    ON T.TABSCHEMA = C.TABSCHEMA
                    AND T.TABNAME = C.TABNAME
                    WHERE
                    upper(T.TABSCHEMA) IN upper('@schemaname')
                    AND T.TYPE = 'T'
                    ORDER BY
                    T.TABSCHEMA,
                    T.TABNAME,
                    C.COLNO;
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    SELECT 'Function' AS object_type, ROUTINENAME AS Code_Object_Name
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'F'
                    AND VALID = 'Y'
                    UNION
                    SELECT 'Procedure' AS object_type,ROUTINENAME AS Code_Object_Name
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'P'
                    AND VALID = 'Y';
                </Code_Objects>
                <Trigger>
                    SELECT
                    TRIGSCHEMA AS trigger_schema,
                    TRIGNAME AS trigger_name,
                    TABNAME as Table_Name
                    FROM SYSCAT.TRIGGERS
                    WHERE TRIGSCHEMA = upper('@schemaname')
                    AND VALID = 'Y'
                    GROUP BY TRIGSCHEMA, TRIGNAME, TABNAME,TRIGTIME,GRANULARITY
                    ORDER BY TABNAME, TRIGNAME;
                </Trigger>
            </Code>
        </Source>
        <Target>
            <Storage>
                <!--<Type>
                    select user_defined_type_name from information_schema.user_defined_types
                    where user_defined_type_schema = lower('@schemaname') order by user_defined_type_name
                </Type>
                <Sequence>
                    select distinct schemaname as schema,
                    sequencename as sequence,
                    last_value - 1 as sequence_value
                    from pg_sequences where schemaname ='@schemaname'
                </Sequence>-->
                <Table>
                    SELECT DISTINCT TABLE_SCHEMA ,TABLE_NAME AS OBJECT_NAME,TABLE_TYPE
                    FROM INFORMATION_SCHEMA.TABLES A
                    WHERE NOT EXISTS (
                    SELECT 1
                    FROM INFORMATION_SCHEMA.TABLES MV
                    WHERE MV.TABLE_NAME = A.TABLE_NAME
                    AND MV.TABLE_SCHEMA in ('information_schema','mysql','information_schema','performance_schema',
                    'sys','sakila','world')
                    AND MV.TABLE_TYPE = 'VIEW'
                    )
                    AND A.TABLE_SCHEMA = upper('@schemaname')
                    AND A.TABLE_NAME NOT LIKE '%$%'
                    AND A.TABLE_TYPE = 'BASE TABLE'
                    AND A.TABLE_NAME NOT LIKE 'SYS%'
                    ORDER BY OBJECT_NAME;
                </Table>
                <Primary_Key>
                                 select
                        OWNER,
                        TABNAME,
                        CONCAT(OWNER, '-', TABNAME, '-', col_list) as CONSTRAINT_DETAILS,
                        CONCAT(col_list,'-',CONSTNAME) as col_list,
                        'ENABLED' as STATUS
                        from
                        (
                        select
                        C.TABLE_SCHEMA as OWNER,
                        C.TABLE_NAME as TABNAME,
                        C.CONSTRAINT_NAME as CONSTNAME,
                        GROUP_CONCAT(CC.COLUMN_NAME order by CC.ORDINAL_POSITION separator ', ') as COL_LIST
                        from
                        INFORMATION_SCHEMA.TABLE_CONSTRAINTS C
                        join
                        INFORMATION_SCHEMA.KEY_COLUMN_USAGE CC
                        on
                        C.CONSTRAINT_NAME = CC.CONSTRAINT_NAME
                        and C.TABLE_SCHEMA = CC.TABLE_SCHEMA
                        and C.TABLE_NAME = CC.TABLE_NAME
                        where
                        C.CONSTRAINT_TYPE = 'PRIMARY KEY'
                        -- Primary Key constraints
                        and C.TABLE_SCHEMA = UPPER('@schemaname')
                        and C.CONSTRAINT_NAME not like '%$%'
                        and C.TABLE_NAME not like '%$%'
                        group by
                        C.TABLE_SCHEMA,
                        C.TABLE_NAME,
                        C.CONSTRAINT_NAME
                        ) as PRIMARY_KEY_CONSTRAINTS
                        order by
                        OWNER,
                        TABNAME;


                </Primary_Key>
                <Unique_Constraint>
                        SELECT
                    TC.TABLE_SCHEMA AS TABSCHEMA,
                    TC.TABLE_NAME AS TABNAME,
                    TC.CONSTRAINT_NAME AS CONSTRAINT_INFO,
                    GROUP_CONCAT(KCU.COLUMN_NAME ORDER BY KCU.ORDINAL_POSITION SEPARATOR ', ') AS COL_LIST,
                    CASE
                    WHEN TC.ENFORCED = 'YES' THEN 'ENABLED'
                    ELSE 'DISABLED'
                    END AS STATUS
                    FROM
                    information_schema.TABLE_CONSTRAINTS AS TC
                    JOIN
                    information_schema.KEY_COLUMN_USAGE AS KCU
                    ON
                    TC.CONSTRAINT_NAME = KCU.CONSTRAINT_NAME
                    AND TC.TABLE_SCHEMA = KCU.TABLE_SCHEMA
                    AND TC.TABLE_NAME = KCU.TABLE_NAME
                    WHERE
                    TC.CONSTRAINT_TYPE = 'UNIQUE'
                    and TC.TABLE_SCHEMA = upper('@schemaname')
                    AND TC.CONSTRAINT_NAME NOT LIKE '%$%'
                    AND TC.TABLE_NAME NOT LIKE '%$%'
                    GROUP BY
                    TC.TABLE_SCHEMA,
                    TC.TABLE_NAME,
                    TC.CONSTRAINT_NAME,
                    TC.ENFORCED;

                </Unique_Constraint>
                <Foreign_Key>
                    SELECT
                    kc.TABLE_SCHEMA AS schema_name,
                    kc.TABLE_NAME AS table_name,
                    rc.CONSTRAINT_NAME AS constraint_name,
                    CONCAT(
                    'ALTER TABLE ',
                    kc.TABLE_SCHEMA, '.', kc.TABLE_NAME,
                    ' ADD CONSTRAINT ', rc.CONSTRAINT_NAME,
                    ' FOREIGN KEY (', kc.COLUMN_NAME, ') ',
                    'REFERENCES ', kcu.TABLE_SCHEMA, '.', kcu.TABLE_NAME,
                    '(', kcu.COLUMN_NAME, ');'
                    ) AS alter_statement,'YES' AS status
                    FROM
                    INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                    JOIN
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE kc
                    ON rc.CONSTRAINT_NAME = kc.CONSTRAINT_NAME
                    AND rc.CONSTRAINT_SCHEMA = kc.TABLE_SCHEMA
                    AND rc.TABLE_NAME = kc.TABLE_NAME -- Ensures proper referencing table
                    JOIN
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                    ON kcu.CONSTRAINT_NAME = rc.UNIQUE_CONSTRAINT_NAME
                    AND kcu.TABLE_SCHEMA = rc.UNIQUE_CONSTRAINT_SCHEMA
                    AND kcu.TABLE_NAME = rc.REFERENCED_TABLE_NAME -- Ensures proper referenced table
                    WHERE
                    kc.TABLE_SCHEMA = upper('@schemaname') -- Replace with your schema name
                    GROUP BY
                    kc.TABLE_SCHEMA,
                    kc.TABLE_NAME,
                    rc.CONSTRAINT_NAME,
                    kc.COLUMN_NAME,
                    kcu.TABLE_SCHEMA,
                    kcu.TABLE_NAME,
                    kcu.COLUMN_NAME;

                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT t.TABLE_SCHEMA,CONCAT(t.TABLE_NAME,'-',C.COLUMN_NAME )as CONSTRAINT_NAME,'ENABLED' as STATUS
                    FROM INFORMATION_SCHEMA.COLUMNS C
                    JOIN INFORMATION_SCHEMA.TABLES T
                    ON C.TABLE_SCHEMA = T.TABLE_SCHEMA
                    AND C.TABLE_NAME = T.TABLE_NAME
                    WHERE C.IS_NULLABLE = 'NO'
                    AND T.TABLE_SCHEMA = '@schemaname'
                    AND T.TABLE_NAME NOT LIKE '%$%'
                    ORDER BY T.TABLE_SCHEMA, T.TABLE_NAME, C.COLUMN_NAME;

                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT
                    TABLE_SCHEMA AS TABSCHEMA,
                    TABLE_NAME AS TABNAME,
                    COLUMN_NAME AS table_column,
                    COLUMN_DEFAULT AS 'DEFAULT'
                    FROM
                    information_schema.COLUMNS
                    WHERE
                    COLUMN_DEFAULT IS NOT NULL
                    AND TABLE_SCHEMA = UPPER('@schemaname')
                    AND TABLE_NAME NOT LIKE '%$%';
                </Default_Constraint>
                <Check_Constraint>
                    select
                    tc.TABLE_SCHEMA as schema_name,
                    tc.TABLE_NAME as table_name,
                    tc.CONSTRAINT_NAME as constraint_name,
                    'ENABLED' as status,
                    cc.CHECK_CLAUSE as check_clause
                    from
                    INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                    join
                    INFORMATION_SCHEMA.CHECK_CONSTRAINTS cc
                    on
                    tc.CONSTRAINT_NAME = cc.CONSTRAINT_NAME
                    and tc.TABLE_SCHEMA = cc.CONSTRAINT_SCHEMA
                    where
                    tc.TABLE_SCHEMA = '@schemaname'
                    and tc.CONSTRAINT_TYPE = 'CHECK'
                    order by
                    tc.TABLE_NAME,
                    tc.CONSTRAINT_NAME;
                </Check_Constraint>
<!--                <Index>-->
<!--                    WITH cols AS (-->
<!--                    SELECT-->
<!--                    idx.table_schema AS schema_name,-->
<!--                    idx.table_name AS table_name,-->
<!--                    idx.index_name AS index_name,-->
<!--                    idx.column_name AS column_name,-->
<!--                    idx.seq_in_index AS column_position,-->
<!--                    CASE-->
<!--                    WHEN idx.non_unique = 0 THEN 'UNIQUE'-->
<!--                    ELSE 'NONUNIQUE'-->
<!--                    END AS uniqueness-->
<!--                    FROM-->
<!--                    information_schema.statistics idx-->
<!--                    WHERE-->
<!--                    idx.table_schema = '@schema_name'-->
<!--                    ),-->
<!--                    index_details AS (-->
<!--                    SELECT-->
<!--                    cols.schema_name,-->
<!--                    cols.table_name,-->
<!--                    GROUP_CONCAT(-->
<!--                    CASE-->
<!--                    WHEN cols.column_name LIKE 'SYS_N%' THEN cols.column_name-->
<!--                    ELSE cols.column_name-->
<!--                    END ORDER BY cols.column_position SEPARATOR ', '-->
<!--                    ) AS index_cols,-->
<!--                    cols.index_name,-->
<!--                    cols.uniqueness,-->
<!--                    CASE-->
<!--                    WHEN cons.constraint_type = 'PRIMARY KEY' THEN 'Primary Key'-->
<!--                    ELSE 'Non Primary Key'-->
<!--                    END AS constraint_type-->
<!--                    FROM-->
<!--                    cols-->
<!--                    LEFT JOIN information_schema.table_constraints cons ON cols.schema_name = cons.table_schema-->
<!--                    AND cols.table_name = cons.table_name-->
<!--                    AND cols.index_name = cons.constraint_name-->
<!--                    AND cons.constraint_type IN ('PRIMARY KEY', 'UNIQUE')-->
<!--                    GROUP BY-->
<!--                    cols.schema_name,-->
<!--                    cols.table_name,-->
<!--                    cols.index_name,-->
<!--                    cols.uniqueness,-->
<!--                    cons.constraint_type-->
<!--                    )-->
<!--                    SELECT-->
<!--                    schema_name,-->
<!--                    table_name,-->
<!--                    index_name,-->
<!--                    index_cols,-->
<!--                    constraint_type,-->
<!--                    LOWER(CONCAT(schema_name, '.', table_name, '-', index_name)) AS mysql_concat,-->

<!--                    LOWER(-->
<!--                    CONCAT(-->
<!--                    'create ',-->
<!--                    CASE WHEN uniqueness = 'NONUNIQUE' THEN 'index ' ELSE 'UNIQUE index ' END,-->
<!--                    index_name,-->
<!--                    ' on ', schema_name, '.', table_name, '(', index_cols, ');'-->
<!--                    )-->
<!--                    ) AS idx_def-->
<!--                    FROM-->
<!--                    index_details-->
<!--                    WHERE-->
<!--                    UPPER(index_name) NOT IN (-->
<!--                    SELECT UPPER(constraint_name)-->
<!--                    FROM information_schema.table_constraints-->
<!--                    WHERE table_schema = '@schemaname'-->
<!--                    AND constraint_type IN ('PRIMARY KEY', 'UNIQUE')-->
<!--                    )-->
<!--                    ORDER BY-->
<!--                    schema_name,-->
<!--                    table_name,-->
<!--                    index_name;-->

<!--                </Index>-->
                <!--                <Synonym>-->
                <!--                    SELECT viewname-->
                <!--                    FROM pg_views-->
                <!--                    WHERE schemaname = lower('@schemaname')-->
                <!--                    order by viewname-->
                <!--                </Synonym>-->
                <!--                <View>-->
                <!--                    SELECT schemaname,viewname-->
                <!--                    FROM pg_views-->
                <!--                    WHERE schemaname = lower('@schemaname')-->
                <!--                    order by viewname-->
                <!--                </View>-->
                <Datatype>
                    SELECT
                    T.TABLE_SCHEMA AS "OWNER",
                    T.TABLE_NAME AS "TABLE_NAME",
                    C.COLUMN_NAME AS "COLUMN_NAME",
                    C.DATA_TYPE AS "DATA_TYPE",
                    CASE
                    WHEN C.DATA_TYPE IN ('char', 'varchar') THEN  concat('(' ,  C.CHARACTER_MAXIMUM_LENGTH, ')')
                    WHEN C.DATA_TYPE IN ('decimal', 'numeric','int', 'smallint', 'bigint', 'float', 'double') THEN  concat('(' ,  C.NUMERIC_PRECISION ,',', C.NUMERIC_SCALE, ')')
                    WHEN C.DATA_TYPE IN ('date', 'time', 'datetime', 'timestamp') THEN NULL
                    WHEN C.DATA_TYPE IN ('blob', 'text', 'longtext', 'mediumblob') THEN NULL
                    WHEN C.DATA_TYPE IN ('json', 'geometry') THEN NULL
                    ELSE NULL
                    END AS "COLUMN_SIZE",
                    C.ORDINAL_POSITION AS "ORDINAL_POSITION",
                    T.TABLE_TYPE AS "TABLE_TYPE"
                    FROM
                    INFORMATION_SCHEMA.TABLES T
                    JOIN
                    INFORMATION_SCHEMA.COLUMNS C
                    ON T.TABLE_SCHEMA = C.TABLE_SCHEMA
                    AND T.TABLE_NAME = C.TABLE_NAME
                    WHERE
                    UPPER(T.TABLE_SCHEMA) = UPPER('@schemaname')
                    AND T.TABLE_TYPE = 'BASE TABLE'

                    ORDER BY
                    T.TABLE_SCHEMA,
                    T.TABLE_NAME,
                    C.ORDINAL_POSITION;


                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    Select res.* from (select routine_type, routine_name as Code_Object_Name from
                    information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    except
                    select routine_type, routine_name as Code_Object_Name from information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')
                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name;
                </Trigger>
            </Code>
        </Target>
    </Validation_Queries>
    <Table_Validation_Queries>
        <Source>
            <Storage>
                <!--                <Type>-->
                <!--                    SELECT TYPE_NAME from SYSIBM.SQLUDTS-->
                <!--                    where TYPE_SCHEM = Upper('@schemaname')-->
                <!--                    order by TYPE_NAME-->
                <!--                </Type>-->
<!--                <Sequence>-->
<!--                    SELECT SEQSCHEMA ,SEQNAME,SEQID AS sequence_value-->
<!--                    FROM SYSCAT.SEQUENCES-->
<!--                    WHERE SEQSCHEMA = upper('quadrant')-->
<!--                    and SEQSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM')-->
<!--                    ORDER BY SEQNAME;-->
<!--                </Sequence>new query commented-->
                <Table>
                    SELECT TABSCHEMA,TABNAME,TYPE FROM SYSCAT.tables A
                    WHERE NOT EXISTS (
                    SELECT 1
                    FROM SYSCAT.TABLES MV
                    WHERE MV.TABNAME = A.TABNAME
                    AND MV.TABSCHEMA = upper('@schemaname')
                    AND MV.TYPE = 'M'
                    )
                    AND A.TABSCHEMA = upper('@schemaname')
                    AND A.TABNAME NOT LIKE '%$%'
                    AND A.TYPE = 'T'
                    AND A.TABNAME NOT LIKE 'SYS%'
                    ORDER BY 1 ;
                </Table>
                <Primary_Key>
                    SELECT  OWNER,TABNAME,OWNER ||'-'|| TABNAME || '-' || col_list AS CONSTRAINT_DETAILS,
                    col_list||'-'||CONSTNAME AS col_list,'ENABLED' AS STATUS
                    FROM (
                    SELECT C.TABSCHEMA AS OWNER,
                    C.TABNAME,
                    C.CONSTNAME,
                    LISTAGG(CC.COLNAME, ', ') WITHIN GROUP (ORDER BY CC.COLSEQ) AS COL_LIST
                    FROM SYSCAT.TABCONST C
                    JOIN SYSCAT.KEYCOLUSE CC ON C.CONSTNAME = CC.CONSTNAME
                            AND C.TABSCHEMA = CC.TABSCHEMA
                            AND C.TABNAME = CC.TABNAME
                    WHERE C.TYPE = 'P' -- Primary Key constraints
                    AND C.TABSCHEMA = upper('@schemaname')
                    AND C.TABNAME = UPPER('@tablename')
                    AND C.CONSTNAME NOT LIKE '%$%'
                    AND C.TABNAME NOT LIKE '%$%'
                    GROUP BY C.TABSCHEMA, C.TABNAME, C.CONSTNAME, C.ENFORCED
                    ) AS PRIMARY_KEY_CONSTRAINTS
                    ORDER BY OWNER, TABNAME;
                </Primary_Key>
                <Unique_Constraint>
                    SELECT c.TABSCHEMA,c.TABNAME,c.CONSTNAME AS "CONSTRAINT_INFO",
                    LISTAGG(cc.COLNAME, ', ') WITHIN GROUP (ORDER BY cc.COLSEQ) "COL_LIST",
                    CASE WHEN c.ENFORCED = 'Y' THEN 'ENABLED' ELSE 'DISABLED' END AS "STATUS"
                    FROM SYSCAT.TABCONST c
                    JOIN SYSCAT.KEYCOLUSE cc ON c.CONSTNAME = cc.CONSTNAME
                    AND c.TABSCHEMA = cc.TABSCHEMA
                    AND c.TABNAME = cc.TABNAME
                    WHERE c.TYPE = 'U'
                    AND c.TABSCHEMA = UPPER('@schemaname')
                    AND c.TABNAME = UPPER('@tablename')
                    AND c.CONSTNAME NOT LIKE '%$%'
                    AND c.TABNAME NOT LIKE '%$%'
                    GROUP BY c.TABSCHEMA, c.TABNAME, c.CONSTNAME, c.ENFORCED
                </Unique_Constraint>
                <Foreign_Key>
                    SELECT
                    kc.TABLE_NAME AS table_name,
                    kc.TABLE_SCHEMA AS schema_name,
                    rc.CONSTRAINT_NAME AS constraint_name,
                    'YES' AS status,
                    CONCAT(
                    'ALTER TABLE ',
                    kc.TABLE_SCHEMA, '.', kc.TABLE_NAME,
                    ' ADD CONSTRAINT ', rc.CONSTRAINT_NAME,
                    ' FOREIGN KEY (', kc.COLUMN_NAME, ') ',
                    'REFERENCES ', kcu.TABLE_SCHEMA, '.', kcu.TABLE_NAME,
                    '(', kcu.COLUMN_NAME, ');'
                    ) AS alter_statement
                    FROM
                    INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                    JOIN
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE kc
                    ON rc.CONSTRAINT_NAME = kc.CONSTRAINT_NAME
                    AND rc.CONSTRAINT_SCHEMA = kc.TABLE_SCHEMA
                    AND rc.TABLE_NAME = kc.TABLE_NAME -- Ensures proper referencing table
                    JOIN
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                    ON kcu.CONSTRAINT_NAME = rc.UNIQUE_CONSTRAINT_NAME
                    AND kcu.TABLE_SCHEMA = rc.UNIQUE_CONSTRAINT_SCHEMA
                    AND kcu.TABLE_NAME = rc.REFERENCED_TABLE_NAME -- Ensures proper referenced table
                    WHERE
                    kc.TABLE_SCHEMA = upper('@schemaname') -- Replace with your schema name
                    and kc.TABLE_NAME = upper('@tablename')
                    GROUP BY
                    kc.TABLE_SCHEMA,
                    kc.TABLE_NAME,
                    rc.CONSTRAINT_NAME,
                    kc.COLUMN_NAME,
                    kcu.TABLE_SCHEMA,
                    kcu.TABLE_NAME,
                    kcu.COLUMN_NAME;
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT  C.TABSCHEMA,
                    upper(C.TABNAME) || '-' || upper(C.COLNAME) AS CONSTRAINT_NAME,
                    'ENABLED' AS STATUS
                    FROM
                    SYSCAT.COLUMNS C
                    JOIN
                    SYSCAT.TABLES T
                    ON
                    C.TABSCHEMA = T.TABSCHEMA
                    AND C.TABNAME = T.TABNAME
                    WHERE
                    C.NULLS = 'N'
                    AND C.TABSCHEMA = upper('@schemaname')
                    AND C.TABNAME = upper('@tablename')
                    AND T.TYPE = 'T'
                    ORDER BY
                    C.TABSCHEMA,
                    C.TABNAME,
                    C.COLNAME;

                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT
                    TABSCHEMA,TABNAME , COLNAME AS table_column,DEFAULT
                    FROM
                    SYSCAT.COLUMNS
                    WHERE
                    DEFAULT IS NOT NULL
                    AND TABSCHEMA = upper('@schemaname')
                    AND TABNAME = upper('@tablename')
                    AND TABNAME NOT LIKE '%$%'
                </Default_Constraint>
                <Check_Constraint>
                                SELECT
                        ac.tabschema,ac.tabname, ac.constname AS constraint_name_status,text AS search_condition,'ENABLED' AS status

                        FROM
                        syscat.checks ac
                        JOIN
                        syscat.colchecks acc
                        ON
                        ac.tabschema = acc.tabschema AND
                        ac.tabname = acc.tabname AND
                        ac.constname = acc.constname
                        WHERE
                        ac.type = 'C' AND
                        ac.tabschema = UPPER('@schemaname')
                        AND ac.tabname  = upper('@tablename')
                        AND
                        (ac.tabname || '.' || acc.colname) NOT IN (
                        SELECT DISTINCT
                        tabname || '.' || colname
                        FROM
                        syscat.columns
                        WHERE
                        nulls = 'N'
                        ) AND
                        ac.constname NOT LIKE '%$%' AND
                        ac.tabname NOT LIKE '%$%'
                        ORDER BY
                        ac.tabschema;
                </Check_Constraint>
                <!--                <Index>-->
                <!--                     SELECT INDSCHEMA,INDNAME FROM SYSCAT.indexes-->
                <!--						WHERE OWNER NOT IN ('SYSIBM')-->
                <!--						AND INDSCHEMA=nvl($INDSCHEMA,INDSCHEMA);-->
                <!--                </Index>-->

<!--                <View>-->
<!--                    SELECT VIEWSCHEMA,VIEWNAME FROM SYSCAT.VIEWS-->
<!--                    WHERE VIEWSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM')-->
<!--                    and VIEWSCHEMA = upper('@schemaname') ;-->
<!--                </View>-->
                <Datatype>
                     SELECT
                    T.TABSCHEMA AS "OWNER",
                    T.TABNAME AS "TABLE_NAME",
                    C.COLNAME AS "COLUMN_NAME",
                    C.TYPENAME AS "DATA_TYPE",
                    CASE
                    WHEN C.TYPENAME IN ('CHAR', 'VARCHAR', 'GRAPHIC', 'VARGRAPHIC','CHARACTER', 'SMALLINT', 'BIGINT', 'REAL') THEN '(' || C.LENGTH || ')'
                    WHEN C.TYPENAME IN ('DECIMAL', 'NUMERIC','INTEGER','DOUBLE') THEN '(' || C.LENGTH || ',' || C.SCALE || ')'
                    WHEN C.TYPENAME IN ('DATE', 'TIME', 'TIMESTAMP') THEN NULL
                    WHEN C.TYPENAME IN ('CLOB', 'BLOB', 'DBCLOB') THEN NULL
                    WHEN C.TYPENAME IN ('XML', 'ROWID', 'UUID') THEN NULL
                    ELSE NULL
                    END AS "COLUMN_SIZE",
                    C.COLNO + 1 AS "ORDINAL_POSITION",
                    T.TYPE AS "TABLE_TYPE"
                    FROM
                    SYSCAT.TABLES T
                    JOIN
                    SYSCAT.COLUMNS C
                    ON T.TABSCHEMA = C.TABSCHEMA
                    AND T.TABNAME = C.TABNAME
                    WHERE
                    upper(T.TABSCHEMA) IN upper('@schemaname')
                    AND upper(T.TABNAME) IN upper('@tablename')
                    AND T.TYPE = 'T'
                    ORDER BY
                    T.TABSCHEMA,
                    T.TABNAME,
                    C.COLNO;
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    SELECT 'Function' AS object_type, ROUTINENAME AS Code_Object_Name
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'F'
                    AND VALID = 'Y'
                    UNION
                    SELECT 'Procedure' AS object_type,ROUTINENAME AS Code_Object_Name
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'P'
                    AND VALID = 'Y';
                </Code_Objects>
                <Trigger>
                    SELECT
                    TRIGSCHEMA AS trigger_schema,
                    TRIGNAME AS trigger_name,
                    TABNAME as Table_Name
                    FROM SYSCAT.TRIGGERS
                    WHERE TRIGSCHEMA = upper('@schemaname')
                    AND VALID = 'Y'
                    GROUP BY TRIGSCHEMA, TRIGNAME, TABNAME,TRIGTIME,GRANULARITY
                    ORDER BY TABNAME, TRIGNAME;
                </Trigger>
            </Code>
        </Source>
        <Target>
            <Storage>
                <!--<Type>
                    select user_defined_type_name from information_schema.user_defined_types
                    where user_defined_type_schema = lower('@schemaname') order by user_defined_type_name
                </Type>
                <Sequence>
                    select distinct schemaname as schema,
                    sequencename as sequence,
                    last_value - 1 as sequence_value
                    from pg_sequences where schemaname ='@schemaname'
                </Sequence>-->
                <Table>
                    SELECT DISTINCT TABLE_SCHEMA ,TABLE_NAME AS OBJECT_NAME,TABLE_TYPE
                    FROM INFORMATION_SCHEMA.TABLES A
                    WHERE NOT EXISTS (
                    SELECT 1
                    FROM INFORMATION_SCHEMA.TABLES MV
                    WHERE MV.TABLE_NAME = A.TABLE_NAME
                    AND MV.TABLE_SCHEMA in ('information_schema','mysql','information_schema','performance_schema',
                    'sys','sakila','world')
                    AND MV.TABLE_TYPE = 'VIEW'
                    )
                    AND A.TABLE_SCHEMA = upper('@schemaname')
                    AND A.TABLE_NAME NOT LIKE '%$%'
                    AND A.TABLE_TYPE = 'BASE TABLE'
                    AND A.TABLE_NAME NOT LIKE 'SYS%'
                    ORDER BY OBJECT_NAME;
                </Table>
                <Primary_Key>
                     select
                        OWNER,
                        TABNAME,
                        CONCAT(OWNER, '-', TABNAME, '-', col_list) as CONSTRAINT_DETAILS,
                        CONCAT(col_list,'-',CONSTNAME) as col_list,
                        'ENABLED' as STATUS
                        from
                        (
                        select
                        C.TABLE_SCHEMA as OWNER,
                        C.TABLE_NAME as TABNAME,
                        C.CONSTRAINT_NAME as CONSTNAME,
                        GROUP_CONCAT(CC.COLUMN_NAME order by CC.ORDINAL_POSITION separator ', ') as COL_LIST
                        from
                        INFORMATION_SCHEMA.TABLE_CONSTRAINTS C
                        join
                        INFORMATION_SCHEMA.KEY_COLUMN_USAGE CC
                        on
                        C.CONSTRAINT_NAME = CC.CONSTRAINT_NAME
                        and C.TABLE_SCHEMA = CC.TABLE_SCHEMA
                        and C.TABLE_NAME = CC.TABLE_NAME
                        where
                        C.CONSTRAINT_TYPE = 'PRIMARY KEY'
                        -- Primary Key constraints
                        and C.TABLE_SCHEMA = UPPER('@schemaname')
                        and C.CONSTRAINT_NAME not like '%$%'
                        and C.TABLE_NAME not like '%$%'
                        group by
                        C.TABLE_SCHEMA,
                        C.TABLE_NAME,
                        C.CONSTRAINT_NAME
                        ) as PRIMARY_KEY_CONSTRAINTS
                        order by
                        OWNER,
                        TABNAME;
                </Primary_Key>
                <Unique_Constraint>
                        SELECT
                    TC.TABLE_SCHEMA AS TABSCHEMA,
                    TC.TABLE_NAME AS TABNAME,
                    TC.CONSTRAINT_NAME AS CONSTRAINT_INFO,
                    GROUP_CONCAT(KCU.COLUMN_NAME ORDER BY KCU.ORDINAL_POSITION SEPARATOR ', ') AS COL_LIST,
                    CASE
                    WHEN TC.ENFORCED = 'YES' THEN 'ENABLED'
                    ELSE 'DISABLED'
                    END AS STATUS
                    FROM
                    information_schema.TABLE_CONSTRAINTS AS TC
                    JOIN
                    information_schema.KEY_COLUMN_USAGE AS KCU
                    ON
                    TC.CONSTRAINT_NAME = KCU.CONSTRAINT_NAME
                    AND TC.TABLE_SCHEMA = KCU.TABLE_SCHEMA
                    AND TC.TABLE_NAME = KCU.TABLE_NAME
                    WHERE
                    TC.CONSTRAINT_TYPE = 'UNIQUE'
                    and TC.TABLE_SCHEMA = upper('@schemaname')
                    AND TC.TABLE_NAME = UPPER('@tablename')
                    AND TC.CONSTRAINT_NAME NOT LIKE '%$%'
                    AND TC.TABLE_NAME NOT LIKE '%$%'
                    GROUP BY
                    TC.TABLE_SCHEMA,
                    TC.TABLE_NAME,
                    TC.CONSTRAINT_NAME,
                    TC.ENFORCED;

                </Unique_Constraint>
                <Foreign_Key>
                    SELECT
                    kc.TABLE_SCHEMA AS schema_name,
                    kc.TABLE_NAME AS table_name,
                    rc.CONSTRAINT_NAME AS constraint_name,
                    CONCAT(
                    'ALTER TABLE ',
                    kc.TABLE_SCHEMA, '.', kc.TABLE_NAME,
                    ' ADD CONSTRAINT ', rc.CONSTRAINT_NAME,
                    ' FOREIGN KEY (', kc.COLUMN_NAME, ') ',
                    'REFERENCES ', kcu.TABLE_SCHEMA, '.', kcu.TABLE_NAME,
                    '(', kcu.COLUMN_NAME, ');'
                    ) AS alter_statement,'YES' AS status
                    FROM
                    INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                    JOIN
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE kc
                    ON rc.CONSTRAINT_NAME = kc.CONSTRAINT_NAME
                    AND rc.CONSTRAINT_SCHEMA = kc.TABLE_SCHEMA
                    AND rc.TABLE_NAME = kc.TABLE_NAME -- Ensures proper referencing table
                    JOIN
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                    ON kcu.CONSTRAINT_NAME = rc.UNIQUE_CONSTRAINT_NAME
                    AND kcu.TABLE_SCHEMA = rc.UNIQUE_CONSTRAINT_SCHEMA
                    AND kcu.TABLE_NAME = rc.REFERENCED_TABLE_NAME -- Ensures proper referenced table
                    WHERE
                    kc.TABLE_SCHEMA = upper('@schemaname') -- Replace with your schema name
                    and kc.TABLE_NAME = upper('@tablename')
                    GROUP BY
                    kc.TABLE_SCHEMA,
                    kc.TABLE_NAME,
                    rc.CONSTRAINT_NAME,
                    kc.COLUMN_NAME,
                    kcu.TABLE_SCHEMA,
                    kcu.TABLE_NAME,
                    kcu.COLUMN_NAME;

                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT
                    t.TABLE_SCHEMA,
                    CONCAT(t.TABLE_NAME, '-', C.COLUMN_NAME )AS CONSTRAINT_NAME,
                    'ENABLED' AS STATUS
                    FROM
                    INFORMATION_SCHEMA.COLUMNS C
                    JOIN INFORMATION_SCHEMA.TABLES T
                    ON
                    C.TABLE_SCHEMA = T.TABLE_SCHEMA
                    AND C.TABLE_NAME = T.TABLE_NAME
                    WHERE
                    C.IS_NULLABLE = 'NO'
                    AND T.TABLE_SCHEMA = '@schemaname'
                    AND T.TABLE_NAME = UPPER('@tablename')
                    AND T.TABLE_NAME NOT LIKE '%$%'
                    ORDER BY
                    T.TABLE_SCHEMA,
                    T.TABLE_NAME,
                    C.COLUMN_NAME;

                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT
                    TABLE_SCHEMA AS TABSCHEMA,
                    TABLE_NAME AS TABNAME,
                    COLUMN_NAME AS table_column,
                    COLUMN_DEFAULT AS 'DEFAULT'
                    FROM
                    information_schema.COLUMNS
                    WHERE
                    COLUMN_DEFAULT IS NOT NULL
                    AND TABLE_SCHEMA = UPPER('@schemaname')
                    AND TABLE_NAME = UPPER('@tablename')
                    AND TABLE_NAME NOT LIKE '%$%';
                </Default_Constraint>
                <Check_Constraint>
                    select
                    tc.TABLE_SCHEMA as schema_name,
                    tc.TABLE_NAME as table_name,
                    tc.CONSTRAINT_NAME as constraint_name,
                    'ENABLED' as status,
                    cc.CHECK_CLAUSE as check_clause
                    from
                    INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                    join
                    INFORMATION_SCHEMA.CHECK_CONSTRAINTS cc
                    on
                    tc.CONSTRAINT_NAME = cc.CONSTRAINT_NAME
                    and tc.TABLE_SCHEMA = cc.CONSTRAINT_SCHEMA
                    where
                    tc.TABLE_SCHEMA = '@schemaname'
                    AND tc.TABLE_NAME = UPPER('@tablename')
                    and tc.CONSTRAINT_TYPE = 'CHECK'
                    order by
                    tc.TABLE_NAME,
                    tc.CONSTRAINT_NAME;
                </Check_Constraint>
<!--                <Index>-->
<!--                    WITH cols AS (-->
<!--                    SELECT-->
<!--                    idx.table_schema AS schema_name,-->
<!--                    idx.table_name AS table_name,-->
<!--                    idx.index_name AS index_name,-->
<!--                    idx.column_name AS column_name,-->
<!--                    idx.seq_in_index AS column_position,-->
<!--                    CASE-->
<!--                    WHEN idx.non_unique = 0 THEN 'UNIQUE'-->
<!--                    ELSE 'NONUNIQUE'-->
<!--                    END AS uniqueness-->
<!--                    FROM-->
<!--                    information_schema.statistics idx-->
<!--                    WHERE-->
<!--                    idx.table_schema = '@schema_name'-->
<!--                    ),-->
<!--                    index_details AS (-->
<!--                    SELECT-->
<!--                    cols.schema_name,-->
<!--                    cols.table_name,-->
<!--                    GROUP_CONCAT(-->
<!--                    CASE-->
<!--                    WHEN cols.column_name LIKE 'SYS_N%' THEN cols.column_name-->
<!--                    ELSE cols.column_name-->
<!--                    END ORDER BY cols.column_position SEPARATOR ', '-->
<!--                    ) AS index_cols,-->
<!--                    cols.index_name,-->
<!--                    cols.uniqueness,-->
<!--                    CASE-->
<!--                    WHEN cons.constraint_type = 'PRIMARY KEY' THEN 'Primary Key'-->
<!--                    ELSE 'Non Primary Key'-->
<!--                    END AS constraint_type-->
<!--                    FROM-->
<!--                    cols-->
<!--                    LEFT JOIN information_schema.table_constraints cons ON cols.schema_name = cons.table_schema-->
<!--                    AND cols.table_name = cons.table_name-->
<!--                    AND cols.index_name = cons.constraint_name-->
<!--                    AND cons.constraint_type IN ('PRIMARY KEY', 'UNIQUE')-->
<!--                    GROUP BY-->
<!--                    cols.schema_name,-->
<!--                    cols.table_name,-->
<!--                    cols.index_name,-->
<!--                    cols.uniqueness,-->
<!--                    cons.constraint_type-->
<!--                    )-->
<!--                    SELECT-->
<!--                    schema_name,-->
<!--                    table_name,-->
<!--                    index_name,-->
<!--                    index_cols,-->
<!--                    constraint_type,-->
<!--                    LOWER(CONCAT(schema_name, '.', table_name, '-', index_name)) AS mysql_concat,-->

<!--                    LOWER(-->
<!--                    CONCAT(-->
<!--                    'create ',-->
<!--                    CASE WHEN uniqueness = 'NONUNIQUE' THEN 'index ' ELSE 'UNIQUE index ' END,-->
<!--                    index_name,-->
<!--                    ' on ', schema_name, '.', table_name, '(', index_cols, ');'-->
<!--                    )-->
<!--                    ) AS idx_def-->
<!--                    FROM-->
<!--                    index_details-->
<!--                    WHERE-->
<!--                    UPPER(index_name) NOT IN (-->
<!--                    SELECT UPPER(constraint_name)-->
<!--                    FROM information_schema.table_constraints-->
<!--                    WHERE table_schema = '@schemaname'-->
<!--                    AND constraint_type IN ('PRIMARY KEY', 'UNIQUE')-->
<!--                    )-->
<!--                    ORDER BY-->
<!--                    schema_name,-->
<!--                    table_name,-->
<!--                    index_name;-->

<!--                </Index>-->
                <!--                <Synonym>-->
                <!--                    SELECT viewname-->
                <!--                    FROM pg_views-->
                <!--                    WHERE schemaname = lower('@schemaname')-->
                <!--                    order by viewname-->
                <!--                </Synonym>-->
                <!--                <View>-->
                <!--                    SELECT schemaname,viewname-->
                <!--                    FROM pg_views-->
                <!--                    WHERE schemaname = lower('@schemaname')-->
                <!--                    order by viewname-->
                <!--                </View>-->
                <Datatype>
                    SELECT
                    T.TABLE_SCHEMA AS "OWNER",
                    T.TABLE_NAME AS "TABLE_NAME",
                    C.COLUMN_NAME AS "COLUMN_NAME",
                    C.DATA_TYPE AS "DATA_TYPE",
                    CASE
                    WHEN C.DATA_TYPE IN ('char', 'varchar') THEN  concat('(' ,  C.CHARACTER_MAXIMUM_LENGTH, ')')
                    WHEN C.DATA_TYPE IN ('decimal', 'numeric','int', 'smallint', 'bigint', 'float', 'double') THEN  concat('(' ,  C.NUMERIC_PRECISION ,',', C.NUMERIC_SCALE, ')')
                    WHEN C.DATA_TYPE IN ('date', 'time', 'datetime', 'timestamp') THEN NULL
                    WHEN C.DATA_TYPE IN ('blob', 'text', 'longtext', 'mediumblob') THEN NULL
                    WHEN C.DATA_TYPE IN ('json', 'geometry') THEN NULL
                    ELSE NULL
                    END AS "COLUMN_SIZE",
                    C.ORDINAL_POSITION AS "ORDINAL_POSITION",
                    T.TABLE_TYPE AS "TABLE_TYPE"
                    FROM
                    INFORMATION_SCHEMA.TABLES T
                    JOIN
                    INFORMATION_SCHEMA.COLUMNS C
                    ON T.TABLE_SCHEMA = C.TABLE_SCHEMA
                    AND T.TABLE_NAME = C.TABLE_NAME
                    WHERE
                    UPPER(T.TABLE_SCHEMA) = UPPER('@schemaname')
                    AND UPPER(T.TABLE_NAME) = UPPER('@tablename')
                    AND T.TABLE_TYPE = 'BASE TABLE'

                    ORDER BY
                    T.TABLE_SCHEMA,
                    T.TABLE_NAME,
                    C.ORDINAL_POSITION;


                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    Select res.* from (select routine_type, routine_name as Code_Object_Name from
                    information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    except
                    select routine_type, routine_name as Code_Object_Name from information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')
                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name;
                </Trigger>
            </Code>
        </Target>

    </Table_Validation_Queries>


</Queries>