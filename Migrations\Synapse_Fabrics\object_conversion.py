import re, os, sys, warnings, csv, multiprocessing
import pandas as pd
import numpy as np
from datetime import datetime
import xml.etree.ElementTree as ET
from import_file import import_file
from joblib import Parallel, delayed
from cryptography.fernet import Fernet
from common_modules.api import decrypt_database_details, api_authentication, get_decryption_key, \
    delete_files_in_directory
from common_modules.common_functions import DualStream, create_sub_objects, pg_formatter, deploy_object
from common_modules.comments_handling import retrieve_comments_with_rules, retrieve_singlequote_data, \
    replace_comment_markers, replace_single_quote_markers
from common_modules.stored_procedures import request_insert, request_update, target_objects_insert, target_deployment_insert

warnings.filterwarnings('ignore')


def decrypt_conversion_file(file_path, file_encryption_key, working_directory_path):
    f = Fernet(file_encryption_key)
    with open(file_path, 'rb') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = f.decrypt(encrypted)

    if not os.path.exists(working_directory_path):
        os.makedirs(working_directory_path)
    file_name = file_path.split('/')[-1]
    with open(working_directory_path + '/' + file_name, 'wb') as decrypted_file:
        decrypted_file.write(decrypted)


def feature_execution_and(source_data, schema, folder_path, value, working_directory_path, token_data):
    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    if '&' in str(value[2]):
        excel_list_all = str(value[2]).split('&')
        list_all = []
        for key in excel_list_all:
            if '~' in key:
                est = re.findall(r'~\S+', key)[0]
                key = key.replace(est, '').strip()
                list_all.append(key)
            else:
                key = key.strip()
                list_all.append(key)
        list_all = [i for i in list_all if i != '']
        status_list = []
        for key in list_all:
            if '*' in key or '%' in key or '.' in key:
                key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace(
                    '..',
                    '.').replace(
                    '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
                key = key.replace('(', '\(').replace('%', '.*?').replace(')', '\)').replace('.', '\.').replace('\.*?',
                                                                                                               '.*?')
                key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
            else:
                ignore_list = [':=', ':']
                check_ignore_item = ['True' for i in ignore_list if i in key]
                if check_ignore_item:
                    key_pattern = r'\s*' + key + r'\s*'
                else:
                    key_pattern = r'\b' + key + r'\b'
                key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
        if 0 not in status_list:
            try:
                encrypt_decrypt_path = folder_path + value[6] + '/' + str(value[1]).lower() + '.py'
                decrypt_key = get_decryption_key(token_data)
                decrypt_conversion_file(encrypt_decrypt_path, decrypt_key, working_directory_path)
                import_object = import_file(working_directory_path + '/' + str(value[1]).lower() + '.py')
                delete_files_in_directory(working_directory_path)
                function_name = [i for i in dir(import_object) if i.lower() == str(value[1]).lower()][0]
                function_call = getattr(import_object, function_name.strip())
                output = function_call(source_data, schema)
                source_data = output
            except Exception as e:
                print('Error in conversion ' + str(e) + ' at module ' + str(value[1]).lower())
                source_data = source_data
    return source_data


def feature_execution_pipe(source_data, schema, folder_path, value, working_directory_path, token_data):
    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    excel_list_any = str(value[2]).split('|')
    list_any = []
    for key in excel_list_any:
        if '~' in key:
            est = re.findall(r'~\S+', key)[0]
            key = key.replace(est, '').strip()
            list_any.append(key)
        else:
            key = key.strip()
            list_any.append(key)
    list_any = [i for i in list_any if i != '']
    status_list = []
    for key in list_any:
        key = key.strip()
        if '*' in key or '%' in key or '.' in key:
            key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace('..',
                                                                                                                  '.').replace(
                '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
            key = key.replace('(', '\(').replace('%', '.*?').replace(')', '\)').replace('.', '\.').replace('\.*?',
                                                                                                           '.*?')
            key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
        else:
            ignore_list = [':=', ':']
            check_ignore_item = ['True' for i in ignore_list if i in key]
            if check_ignore_item:
                key_pattern = r'\s*' + key + r'\s*'
            else:
                key_pattern = r'\b' + key + r'\b'
            key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
    if any(i > 0 for i in status_list):
        try:
            encrypt_decrypt_path = folder_path + value[6] + '/' + str(value[1]).lower() + '.py'
            decrypt_key = get_decryption_key(token_data)
            decrypt_conversion_file(encrypt_decrypt_path, decrypt_key, working_directory_path)
            import_object = import_file(working_directory_path + '/' + str(value[1]).lower() + '.py')
            delete_files_in_directory(working_directory_path)
            function_name = [i for i in dir(import_object) if i.lower() == str(value[1]).lower()][0]
            function_call = getattr(import_object, function_name.strip())
            output = function_call(source_data, schema)
            source_data = output
        except Exception as e:
            print('Error in conversion ' + str(e) + ' at module ' + str(value[1]).lower())
    return source_data


def feature_execution(source_data, schema, folder_path, excel_data, complete_excel_data, working_directory_path,
                      token_data):
    feature_execution_list = []
    for index, value in excel_data.iterrows():
        if value[3] == 'No Predecessor':
            if value[1] not in feature_execution_list:
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, working_directory_path,
                                                        token_data)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value,
                                                         working_directory_path, token_data)
                feature_execution_list.append(value[1])
            else:
                source_data = source_data
                feature_execution_list = feature_execution_list
        else:
            if value[3] in feature_execution_list:
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, working_directory_path,
                                                        token_data)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value,
                                                         working_directory_path, token_data)
                feature_execution_list.append(value[1])
            else:
                parent_feature_name = value[3]
                pred_excel_data = complete_excel_data[complete_excel_data['Feature_Name'] == parent_feature_name]
                source_data = feature_execution(source_data, schema, folder_path, pred_excel_data, complete_excel_data,
                                                working_directory_path, token_data)
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, working_directory_path,
                                                        token_data)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value,
                                                         working_directory_path, token_data)
                feature_execution_list.append(value[3])
                feature_execution_list.append(value[1])
    return source_data


def build_common_object_list(objects_excel_data, object_path):
    object_path_list = []
    object_excel_data = objects_excel_data[objects_excel_data['Object_Id'] == object_path]
    object_path_list.append(object_path)
    if not object_excel_data.empty:
        if type(object_excel_data.iloc[0]['Linked_Objects']) is not float and not isinstance(
                object_excel_data.iloc[0]['Linked_Objects'], np.float64):
            linked_objects_split = object_excel_data.iloc[0]['Linked_Objects'].split(',')
            if linked_objects_split:
                for path in linked_objects_split:
                    temp_list = build_common_object_list(objects_excel_data, path)
                    if temp_list:
                        object_path_list.extend(temp_list)
    return object_path_list


def tuple_execution(object_tuple, schema_name, object_path, modules_data, rules_data, objects_data, scripts_path,
                    working_directory_path, token_data):
    object_path_list = build_common_object_list(objects_data, str(object_path + '/' + 'Pre'))
    pre_module_data = modules_data[modules_data['Object_Path'].isin(object_path_list)]

    pre_output = object_tuple[1]
    if not pre_module_data.empty:
        pre_output = feature_execution(object_tuple[1], schema_name, scripts_path, pre_module_data, pre_module_data,
                                       working_directory_path,
                                       token_data)
    pre_output = '\n'.join([re.sub(r' +', ' ', i).strip() for i in pre_output.split('\n') if i.strip()])

    current_object_data = objects_data[(objects_data['Object_Id'] == object_path)]
    if current_object_data.iloc[0]['Object_Process_Style'] == 'Sequential':
        sub_object_path = object_path + '/'
        sub_objects_data = objects_data[
            objects_data['Object_Id'].str.contains(object_path) & ~objects_data['Object_Id'].str.contains(
                'Pre|Post')].sort_values("Object_Execution_Order")

        sub_object_names_list = []
        object_path_length = len([i for i in sub_object_path.split('/') if i])
        sub_object_names_list.extend(row['Object_Id'].rsplit('/', 1)[-1] for _, row in sub_objects_data.iterrows() if
                                     len(row['Object_Id'].split('/')) == object_path_length + 1)

        for sub_object in sub_object_names_list:
            current_object_path = object_path + '/' + sub_object
            sub_objects_list = create_sub_objects(pre_output, current_object_path, rules_data, objects_data)

            counter = 0
            for sub_tuple in sub_objects_list:
                sub_tuple_data = '\n'.join(
                    [re.sub(r' +', ' ', i).strip() for i in sub_tuple[1].split('\n') if i.strip()])

                if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                    pre_output = re.sub(rf'{re.escape(sub_tuple_data.strip())}',
                                        f' qbook_replace_{sub_tuple[0]}_{counter}_us ', pre_output, 1,
                                        flags=re.DOTALL | re.I)
                    # pre_output = pre_output.replace(sub_tuple_data,
                    #                                 ' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ',
                    #                                 1)
                    sub_object_output = tuple_execution(sub_tuple, schema_name, current_object_path, modules_data,
                                                        rules_data, objects_data, scripts_path, working_directory_path,
                                                        token_data)
                    pre_output = str(pre_output).replace(
                        ' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ',
                        '\n' + str(sub_object_output))
                else:
                    pre_output = pre_output
                counter = counter + 1

    elif current_object_data.iloc[0]['Object_Process_Style'] == 'Mutually Exclusive':
        sub_object_path = object_path + '/'
        sub_objects_list = create_sub_objects(pre_output, sub_object_path, rules_data, objects_data)

        counter = 0
        for sub_tuple in sub_objects_list:
            sub_tuple_data = '\n'.join([re.sub(r' +', ' ', i).strip() for i in sub_tuple[1].split('\n') if i.strip()])

            if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                pre_output = re.sub(rf'{re.escape(sub_tuple_data.strip())}',
                                    f' qbook_replace_{sub_tuple[0]}_{counter}_us ', pre_output, 1,
                                    flags=re.DOTALL | re.I)
                # pre_output = pre_output.replace(sub_tuple_data,
                #                                 ' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ', 1)
                current_object_path = object_path + '/' + str(sub_tuple[0])
                sub_object_output = tuple_execution(sub_tuple, schema_name, current_object_path, modules_data,
                                                    rules_data, objects_data, scripts_path, working_directory_path,
                                                    token_data)
                pre_output = pre_output.replace(' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ',
                                                '\n' + str(sub_object_output))
            else:
                pre_output = pre_output
            counter = counter + 1
    else:
        pre_output = pre_output

    object_path_list = build_common_object_list(objects_data, str(object_path + '/' + 'Post'))
    post_module_data = modules_data[modules_data['Object_Path'].isin(object_path_list)]

    post_output = feature_execution(pre_output, schema_name, scripts_path, post_module_data, post_module_data,
                                    working_directory_path,
                                    token_data)
    post_output = '\n'.join([re.sub(r' +', ' ', i).strip() for i in post_output.split('\n') if i.strip()])

    return post_output


def object_conversion_trigger(schema_name, object_type, object_name, modules_data, rules_data, objects_data,
                              scripts_path, working_directory_path, conversion_file_path, token_data):
    comment_identifiers = rules_data[rules_data['Object_Path'] == object_type]['Comment_Identifiers'].values.tolist()[0]

    f = open(object_name, 'r', encoding='utf-8', errors='replace')
    source_data = f.read()

    source_data = "".join([s for s in source_data.strip().splitlines(True) if s.strip()])
    source_data = re.sub(' +', ' ', source_data)

    comments_data_list = re.findall(r'\/\*[\s\S]*?\*\/', source_data)
    for comment_data in comments_data_list:
        modified_comment_data = comment_data.replace('/*', '/*\n', 1).replace('*/', '\n*/', 1)
        source_data = source_data.replace(comment_data, modified_comment_data)

    # source_data, single_quote_comment_dict = retrieve_singlequote_data(source_data)
    source_data, comment_dict = retrieve_comments_with_rules(source_data, comment_identifiers)
    source_data = re.sub(r' +', ' ', source_data)

    source_data = re.sub(r'CREATE\sProc\b', 'CREATE PROCEDURE', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = source_data.replace('&lt ;', '&lt;').replace('&lt;', '<')
    source_data = source_data.replace('&gt ;', '&gt;').replace('&gt;', '>')
    source_data = re.sub(r':\s*=\s*', ':= ', source_data, flags=re.DOTALL | re.IGNORECASE)
    source_data = re.sub(r' +', ' ', source_data)

    # if object_type in ['Procedure', 'Function', 'Package_Procedure', 'Package_Function']:
    #     source_data = schema_handling(source_data, schema_name, conversion_file_path)
    #     source_data = call_statement_handling(source_data, conversion_file_path)
    #     source_data = cast_handling(source_data, schema_name, object_name.split('/')[-1].replace('.sql', ''),
    #                                 conversion_file_path)
        # print(source_data)


    object_tuple = (object_type, source_data)
    object_converted_output = tuple_execution(object_tuple, schema_name, object_type, modules_data, rules_data,
                                              objects_data, scripts_path, working_directory_path, token_data)

    object_converted_output = replace_comment_markers(object_converted_output, comment_dict)
    # # object_converted_output = replace_single_quote_markers(object_converted_output, single_quote_comment_dict)
    # object_converted_output=add_execute_keyword(object_converted_output)
    # if re.search(r'RRRR',object_converted_output,flags=re.DOTALL|re.I):
    #     object_converted_output=date_format(object_converted_output)

    return object_converted_output


def schema_execution(task_name, migration_name, root_path, project_folder, working_directory_path, iteration_id,
                     source_connection_id, dr_connection_id, target_connection_id, schema, target_schema, object_type,
                     objects_list, object_category, object_category_folder,
                     restart_flag, project_DB_details, dr_DB_details, target_DB_details, db_module_path, token_data,
                     modules_data, scripts_path, rules_data, objects_data):
    import_object = import_file(db_module_path)
    function_call = getattr(import_object, 'connect_database')

    request_id = ''
    try:
        print("schema name ", schema)
        target_function_call = getattr(import_object, 'target_DB_connection')

        project_connection = function_call(project_DB_details)
        request_id = request_insert(project_connection, iteration_id, source_connection_id, task_name, object_category,
                                    schema, object_type)[0]

        source_path = root_path + '/' + project_folder + '/' + str(
            iteration_id) + '/' + 'Source' + '/' + schema.capitalize()

        conversion_path = root_path + '/' + project_folder + '/' + str(
            iteration_id) + '/' + 'Conversion' + '/' + schema.capitalize()
        if not os.path.exists(conversion_path):
            os.makedirs(conversion_path)

        single_file_output_path = root_path + '/' + project_folder + '/' + 'Single_File_Output'
        if not os.path.exists(single_file_output_path):
            os.makedirs(single_file_output_path)

        output_deployment_path = root_path + '/' + project_folder + '/' + str(
            iteration_id) + '/' + 'Deployment_Logs'
        if not os.path.exists(output_deployment_path):
            os.makedirs(output_deployment_path)

        execution_log_path = root_path + '/' + project_folder + '/' + str(
            iteration_id) + '/' + 'Execution_Logs' + '/' + 'Conversion'
        if not os.path.exists(execution_log_path):
            os.makedirs(execution_log_path)

        execution_file_path = execution_log_path + '/' + str(
            iteration_id) + '_' + object_category_folder + '_' + schema.capitalize() + '_execution_{}.log'.format(
            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

        conversion_file_path = source_path + '/' + 'Conversion_Files.xlsx'

        original_stdout = sys.stdout
        original_stderr = sys.stderr
        log_file = open(execution_file_path, "w")
        sys.stdout = DualStream(log_file, original_stdout)
        sys.stderr = DualStream(log_file, original_stderr)

        for object_type in objects_list:
            try:
                if object_type == 'Package_Procedure':
                    rules_object_name = 'Procedure'
                elif object_type == 'Package_Function':
                    rules_object_name = 'Function'
                else:
                    rules_object_name = object_type

                object_rules_data = rules_data[(rules_data['Migration_Name'] == migration_name) & (
                        rules_data['Object_Path'] == rules_object_name)]

                if not object_rules_data.empty:
                    object_conversion_path = conversion_path + '/' + object_type
                    if not os.path.exists(object_conversion_path):
                        os.makedirs(object_conversion_path)

                    object_source_path = source_path + '/' + object_type
                    if os.path.exists(source_path):
                        object_files_list = os.listdir(object_source_path)

                        if object_type == 'Table':
                            partition_source_path = source_path + '/' + 'Partition'
                            if os.path.exists(partition_source_path):
                                partition_names_list = os.listdir(partition_source_path)
                                object_files_list = [i for i in object_files_list if i not in partition_names_list]
                        object_files_list = [object_source_path + '/' + file_i for file_i in object_files_list]

                        object_status_file = source_path + '/' + str(
                            iteration_id) + '_' + schema.capitalize() + '_' + object_type + '_Conversion.csv'

                        converted_objects_list = []
                        if not os.path.isfile(object_status_file) or restart_flag == 'True':
                            csv_file = open(object_status_file, mode='w', newline='')
                            writer = csv.writer(csv_file)
                            writer.writerow(['Object_Name', 'Conversion_Status'])
                            csv_file.flush()
                        else:
                            if os.path.isfile(object_status_file):
                                try:
                                    converted_df = pd.read_csv(object_status_file)
                                    # converted_objects_list = converted_df['Object_Name'].values.tolist()
                                    converted_objects_list = \
                                        converted_df[converted_df['Conversion_Status'] == 'Converted and Deployed'][
                                            'Object_Name'].values.tolist()
                                    os.remove(object_status_file)
                                    csv_file = open(object_status_file, mode='w', newline='')
                                    writer = csv.writer(csv_file)
                                    writer.writerow(['Object_Name', 'Conversion_Status'])
                                    csv_file.flush()
                                except Exception as restart_error:
                                    print(
                                        f'Error occurred at reading {object_type} sheet from excel path {object_status_file} : {str(restart_error)}')

                        if not os.path.exists(single_file_output_path + '/' + object_type):
                            os.makedirs(single_file_output_path + '/' + object_type)
                        single_file_output_file_path = single_file_output_path + '/' + object_type + '/' + str(
                            iteration_id) + '_' + str(
                            dr_DB_details[
                                'db_name']).capitalize() + '_~_' + target_schema.capitalize() + '_~_' + object_type + '_Single_File_Output_{}.sql'.format(
                            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                        if not os.path.exists(output_deployment_path + '/' + object_type):
                            os.makedirs(output_deployment_path + '/' + object_type)
                        output_deployment_file_path = output_deployment_path + '/' + object_type + '/' + str(
                            iteration_id) + '_' + target_schema.capitalize() + '_' + object_type + '_Deploy_Log_{}.log'.format(
                            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                        for index, object_name in enumerate(object_files_list):
                            object_converted_output = ''
                            try:
                                if restart_flag == 'False':
                                    if object_name.split('/')[-1].strip() not in converted_objects_list:
                                        pass
                                    else:
                                        object_status_df = pd.DataFrame(
                                            [(object_name.split('/')[-1], 'Already Converted and Deployed')],
                                            columns=['Object_Name', 'Conversion_Status'])
                                        object_status_df.to_csv(csv_file, header=False, index=False)
                                        csv_file.flush()

                                        print(f"Skipping {object_name.split('/')[-1]} as it is already Converted ")
                                        continue

                                object_converted_output = object_conversion_trigger(schema, rules_object_name,
                                                                                    object_name,
                                                                                    modules_data, rules_data,
                                                                                    objects_data, scripts_path,
                                                                                    working_directory_path,
                                                                                    conversion_file_path, token_data)

                                object_converted_output = re.sub(rf'{schema}\.', target_schema + '.',
                                                                 object_converted_output,
                                                                 flags=re.IGNORECASE | re.DOTALL)
                                # object_converted_output = pg_formatter(object_converted_output)

                                with open(object_conversion_path + '/' + object_name.split('/')[-1], 'w') as f:
                                    f.write("{}\n\n\n".format(object_converted_output))

                                print(f"Conversion completed for {object_type}.{object_name.split('/')[-1]}")

                                dr_connection, error = target_function_call(dr_DB_details)
                                deploy_error = deploy_object(dr_connection, object_converted_output)

                                status_msgs = ['table can have only one primary key', 'existing object',
                                               'already NOT NULL',
                                               'already indexed', 'already exists', 'column default value expression',
                                               'Duplicate key name ', 'Multiple primary key defined']

                                file_name = object_name.split('/')[-1]
                                filename_without_ext = file_name.split('.')[0]
                                if deploy_error != '' and all(
                                        str(i).lower() not in str(deploy_error).lower() for i in status_msgs):
                                    deploy_message = f"Error occurred at deployment of object {file_name} : {str(deploy_error)}"

                                    target_objects_insert(project_connection, iteration_id, source_connection_id,
                                                          schema,
                                                          dr_connection_id, target_schema, object_type,
                                                          filename_without_ext,
                                                          object_converted_output, False)

                                    target_deployment_insert(project_connection, iteration_id, dr_connection_id,
                                                             schema,
                                                             object_type, filename_without_ext, None, None,
                                                             False, deploy_error)

                                    object_status_df = pd.DataFrame(
                                        [(object_name.split('/')[-1], 'Converted and Undeployed')],
                                        columns=['Object_Name', 'Conversion_Status'])
                                    object_status_df.to_csv(csv_file, header=False, index=False)
                                    csv_file.flush()

                                else:
                                    deploy_message = f"Deployment completed successfully for object {file_name} : {str(deploy_error)}"

                                    with open(single_file_output_file_path, 'a') as f:
                                        f.write("\n\n\n{}".format(object_converted_output))

                                    target_objects_insert(project_connection, iteration_id, source_connection_id,
                                                          schema,
                                                          dr_connection_id, target_schema, object_type,
                                                          filename_without_ext,
                                                          object_converted_output, True)

                                    object_status_df = pd.DataFrame(
                                        [(object_name.split('/')[-1], 'Converted and Deployed')],
                                        columns=['Object_Name', 'Conversion_Status'])
                                    object_status_df.to_csv(csv_file, header=False, index=False)
                                    csv_file.flush()

                                    if target_connection_id != '' and dr_connection_id != target_connection_id and object_type in [
                                        'Table', 'Partition']:
                                        target_connection, error = target_function_call(target_DB_details)
                                        target_deploy_error = deploy_object(target_connection, object_converted_output)

                                        if deploy_error != '' and all(
                                                str(i).lower() not in str(target_deploy_error).lower() for i in
                                                status_msgs):
                                            print(
                                                f"Error occurred at deployment of object {file_name} in target DB: {str(target_deploy_error)}")
                                        else:
                                            print(
                                                f"Deployment completed successfully for object {file_name} in target DB: {str(target_deploy_error)}")

                                with open(output_deployment_file_path, 'a') as f:
                                    f.write("\n\n{}".format(deploy_message))
                            except Exception as object_name_error:
                                print(
                                    f"Error occurred at conversion of {object_type}.{object_name.split('/')[-1]} :{str(object_name_error)}")
            except Exception as object_error:
                print(f"Error occurred at object {object_type} is {str(object_error)}")

        project_connection = function_call(project_DB_details)
        request_update(project_connection, request_id, 'Completed', None)
    except Exception as schema_error:
        print(f"Error occurred at schema {schema} is {str(schema_error)}")

        project_connection = function_call(project_DB_details)
        request_update(project_connection, request_id, 'Error', str(schema_error))



def conversion_trigger(task_name, project_id, migration_name, iteration_id, object_category, object_type,
                       schema_name, target_schema, source_connection_id, dr_connection_id, target_connection_id,
                       restart_flag, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    print(local_root_path)

    print(f'Performing {task_name} process')
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        if not os.path.exists(working_directory_path):
            os.makedirs(working_directory_path)

        code_objects_list, storage_objects_list = [], []

        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'extraction_queries.xml'
        tree = ET.parse(xml_path)
        root = tree.getroot()
        code_objects_tags = root.find('Extraction_Queries/Code_Objects')
        for code_tag in code_objects_tags.iter():
            code_objects_list.append(code_tag.tag)
        code_objects_list = [i for i in code_objects_list if
                             i not in ['Code_Objects', 'ListQuery', 'DefinitionQuery', 'Query']]
        if 'Package' in code_objects_list:
            code_objects_list = [item for item in code_objects_list if item != 'Package'] + ['Package_Procedure',
                                                                                         'Package_Function']

        storage_objects_tags = root.find('Extraction_Queries/Storage_Objects')
        for storage_tag in storage_objects_tags.iter():
            storage_objects_list.append(storage_tag.tag)
        storage_objects_list = [i for i in storage_objects_list if
                                i not in ['Storage_Objects', 'ListQuery', 'DefinitionQuery', 'Query',
                                          'QueryUpperVersion',
                                          'QueryLowerVersion']]

        objects_list = []
        object_category_folder = ''
        if object_category == 'Code_Objects':
            objects_list = code_objects_list
            object_category_folder = object_category
        elif object_category == 'Storage_Objects':
            objects_list = storage_objects_list
            object_category_folder = object_category
        elif object_category == 'All':
            objects_list = storage_objects_list + code_objects_list
            object_category_folder = 'All_Objects'

        if object_type.capitalize().strip() == 'All':
            objects_list = objects_list
        else:
            objects_list = [i for i in objects_list if i in object_type.strip().split(',')]

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)

            token_data = api_authentication()
            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')

            dr_DB_details = decrypt_database_details(token_data, project_id, 'Target', dr_connection_id)
            target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)


            project_folder = 'PRJ' + project_id + 'SRC'

            conversion_modules_path = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '.csv'
            modules_data = pd.read_csv(conversion_modules_path)

            scripts_path = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/'
            sys.path.append(scripts_path)

            dynamic_rules_path = local_root_path + '/' + 'Dynamic_Rules' + '/' + migration_name + '/' + migration_name + '.csv'
            rules_data = pd.read_csv(dynamic_rules_path)

            objects_excel = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '_objects_data.csv'
            objects_data = pd.read_csv(objects_excel)

            if schema_name.capitalize() == 'All':
                schema_list = os.listdir(root_path + '/' + project_folder + '/' + 'Source')
            else:
                schema_list = schema_name.strip().split(',')
            print(schema_list)

            # cores = multiprocessing.cpu_count()
            #
            # Parallel(n_jobs=cores, verbose=10)(
            #     delayed(schema_execution)(task_name, migration_name, root_path, project_folder, working_directory_path,
            #                               iteration_id, source_connection_id, dr_connection_id, target_connection_id,
            #                               schema, target_schema, object_type, objects_list, object_category,
            #                               object_category_folder, restart_flag,
            #                               project_DB_details, dr_DB_details, target_DB_details, db_module_path,
            #                               token_data, modules_data, scripts_path,
            #                               rules_data, objects_data)
            #     for schema in schema_list)
            for schema in schema_list:
                schema_execution(task_name, migration_name, root_path, project_folder, working_directory_path,
                                          iteration_id, source_connection_id, dr_connection_id, target_connection_id,
                                          schema, target_schema, object_type, objects_list, object_category,
                                          object_category_folder, restart_flag,
                                          project_DB_details, dr_DB_details, target_DB_details, db_module_path,
                                          token_data, modules_data, scripts_path,
                                          rules_data, objects_data)

        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
