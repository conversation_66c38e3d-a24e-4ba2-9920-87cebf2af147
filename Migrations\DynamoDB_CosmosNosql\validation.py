import os, sys, re, shutil
from datetime import datetime
import pandas as pd
from import_file import import_file
import xml.etree.ElementTree as ET
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.common_functions import adjust_column_width


def validation_trigger(task_name, project_id, migration_name, iteration_id, object_category, schema_name, target_schema,
                       source_connection_id, target_connection_id, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    print(f'Performing {task_name} process')
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        if not os.path.exists(working_directory_path):
            os.makedirs(working_directory_path)

        code_objects_list, storage_objects_list = [], []
        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'validation_queries.xml'

        tree = ET.parse(xml_path)
        root = tree.getroot()
        storage_objects_tags = root.find('Validation_Queries/Source/Storage')
        for storage_tag in storage_objects_tags.iter():
            storage_objects_list.append(storage_tag.tag)

        storage_objects_list = [i for i in storage_objects_list if i not in ['Storage']]

        code_objects_tags = root.find('Validation_Queries/Source/Code')
        for code_tag in code_objects_tags.iter():
            code_objects_list.append(code_tag.tag)
        code_objects_list = [i for i in code_objects_list if
                             i not in ['Code']]

        objects_list = []
        if object_category == 'Code_Objects':
            objects_list = code_objects_list
        elif object_category == 'Storage_Objects':
            objects_list = storage_objects_list
        elif object_category == 'All':
            objects_list = storage_objects_list + code_objects_list

        if object_category.capitalize() == 'All':
            object_category_folder = 'All_Objects'
        else:
            object_category_folder = object_category

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            function_call = getattr(import_object, 'connect_database')
            application_conn = function_call(project_DB_details)

            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', source_connection_id)
            source_function_call = getattr(import_object, 'DB_connection')

            target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)
            target_function_call = getattr(import_object, 'target_DB_connection')

            execute_function_call_list = getattr(import_object, 'execute_query')

            source_parallel_size = source_DB_details['parallelprocess']
            target_parallel_size = target_DB_details['parallelprocess']

            # request_data = request_insert(application_conn, source_connection_id, schema_name, 'Validation', 'All',
            #                               object_category, iteration_str)
            # request_id = request_data[0][1]
            #
            # iteration_id = run_info_insert(application_conn, iteration_str, schema_name, 'All'.capitalize(),
            #                                'Validation',
            #                                object_category, source_connection_id)

            project_folder = 'PRJ' + project_id + 'SRC'

            validation_reports_folder = root_path + '/' + project_folder + '/' + str(
                iteration_id) + '/' + 'Reports' + '/' + 'Validation'
            if not os.path.exists(validation_reports_folder):
                os.makedirs(validation_reports_folder)

            conversion_path = root_path + '/' + project_folder + '/' + str(
                iteration_id) + '/' + 'Conversion' + '/' + schema_name.capitalize()
            if not os.path.exists(conversion_path):
                os.makedirs(conversion_path)

            validation_report_path = validation_reports_folder + '/' + str(
                iteration_id) + '_' + schema_name.capitalize() + '_' + object_category_folder + '_Validation_{}.xlsx'.format(
                datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

            excel_path = conversion_path + '/' + str(
                iteration_id) + '_' + schema_name.capitalize() + '_' + object_category_folder + '_Validation.xlsx'

            temp_excel_path = working_directory_path + '/' + str(
                iteration_id) + '_' + schema_name.capitalize() + '_' + object_category_folder + '_Validation.xlsx'

            source_name, target_name = 'DynamoDB', 'CosmosNosql'

            with pd.ExcelWriter(temp_excel_path, engine="openpyxl") as writer:
                final_summary_objects_list = []
                for object_type in objects_list:
                    try:
                        resource, client, error = source_function_call(source_DB_details)
                        source_object_output = [str(object.name).lower() for object in list(resource.tables.all())]

                        target_client, error = target_function_call(target_DB_details)
                        database = target_client.get_database_client(target_DB_details['db_name'])
                        target_object_output = [i['id'].lower() for i in database.list_containers()]

                        if object_type == 'Table':
                            source_data_df = pd.DataFrame(source_object_output,
                                                          columns=['Table_Name'])
                            target_data_df = pd.DataFrame(target_object_output,
                                                          columns=['Table_Name'])
                            source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                            target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                            matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                                    suffixes=('_Source', '_Target'), how='inner')
                            matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                            source_data = source_data_df[
                                ~source_data_df['Table_Name'].isin(matched_data['Table_Name'])]
                            target_data = target_data_df[
                                ~target_data_df['Table_Name'].isin(matched_data['Table_Name'])]

                            source_only_data = pd.merge(source_data, target_data,
                                                        on=['Table_Name'],
                                                        suffixes=('_Source', '_Target'),
                                                        how='left', indicator=True)
                            source_only_data = source_only_data[source_only_data['_merge'] == 'left_only'].drop(
                                columns=['_merge'])
                            source_only_data['Status'] = f"Available in {source_name} not in {target_name}"

                            target_only_data = pd.merge(source_data, target_data,
                                                        on=['Table_Name'],
                                                        suffixes=('_Source', '_Target'),
                                                        how='right', indicator=True)
                            target_only_data = target_only_data[target_only_data['_merge'] == 'right_only'].drop(
                                columns=['_merge'])
                            target_only_data['Status'] = f"Available in {target_name} not in {source_name}"

                            result_df = pd.concat([matched_data, source_only_data, target_only_data], ignore_index=True)
                            result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)
                        else:
                            result_df = pd.DataFrame()

                        if object_type not in ['Datatype', 'Synonym']:
                            object_matching_count = \
                                len(result_df[
                                        (result_df['Status'] == f"Available in both {source_name} and {target_name}")])

                            source_objects_count = \
                                len(result_df[(result_df['Status'] == f"Available in {source_name} not in {target_name}")])

                            if source_objects_count == 0:
                                status = 'Matched'
                            else:
                                status = 'Not Matched'

                            final_summary_objects_list.append(
                                (schema_name, target_schema, object_type, object_matching_count, source_objects_count,status))

                            # insert the count in metadata
                    except Exception as object_error:
                        print(f"Error occurred at validation of {schema_name}.{object_type} is {str(object_error)}")

                final_summary_objects_df = pd.DataFrame(final_summary_objects_list,
                                                        columns=['Source_Schema', 'Target_Schema',
                                                                 'Object_Name',
                                                                 'Matching_Count', 'Not_Matching_Count',
                                                                 'Matching_Status'])
                final_summary_objects_df.to_excel(writer, sheet_name='Summary', index=False)

                workbook = writer.book
                sheets = workbook.sheetnames
                sheets.remove('Summary')
                sheets.insert(0, 'Summary')
                workbook._sheets = [workbook.get_sheet_by_name(sheet) for sheet in sheets]

            shutil.copyfile(temp_excel_path, excel_path)
            shutil.copyfile(temp_excel_path, validation_report_path)

            adjust_column_width(excel_path)
            adjust_column_width(validation_report_path)

        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')

