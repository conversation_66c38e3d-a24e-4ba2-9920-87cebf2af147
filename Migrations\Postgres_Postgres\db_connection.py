import psycopg2


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    try:
        connection = psycopg2.connect(user=database_data['name'], password=database_data['password'],
                                      host=database_data['host'], database=database_data['db_name'],
                                      port=database_data['port'])
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near Source database connection", e)
    return connection, error


def target_DB_connection(database_data):
    try:
        connection = psycopg2.connect(user=database_data['name'], password=database_data['password'],
                                      host=database_data['host'], database=database_data['db_name'],
                                      port=database_data['port'])
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near Target database connection", e)
    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except psycopg2.DatabaseError as e:
        print("Issue found near Target database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
