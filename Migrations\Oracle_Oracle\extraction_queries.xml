<Queries>
    <Extraction_Queries>
        <Code_Objects>
            <Package>
                <ListQuery>
                    SELECT object_name,STATUS
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('PACKAGE')
                </ListQuery>
                <DefinitionQuery>
                    with x as
                    (
                    SELECT owner, object_name, object_type
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('PACKAGE')
                    AND object_name like '@name'
                    )
                    SELECT DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner) as ddlcode FROM x
                </DefinitionQuery>
            </Package>
            <Procedure>
                <ListQuery>
                    SELECT object_name,STATUS
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('PROCEDURE')
                </ListQuery>
                <DefinitionQuery>
                    with x as
                    (
                    SELECT owner, object_name, object_type
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('PROCEDURE')
                    AND upper(object_name) = upper('@name')
                    )
                    SELECT DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner) as ddlcode FROM x
                </DefinitionQuery>
            </Procedure>
            <Function>
                <ListQuery>
                    SELECT object_name,STATUS
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('FUNCTION')
                </ListQuery>
                <DefinitionQuery>
                    with x as
                    (
                    SELECT owner, object_name, object_type
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('FUNCTION')
                    AND upper(object_name) = upper('@name')
                    )
                    SELECT DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner) as ddlcode FROM x
                </DefinitionQuery>
            </Function>
            <Trigger>
                <ListQuery>
                    SELECT object_name,STATUS FROM dba_objects WHERE owner = upper('@schemaname') AND object_type IN
                    ('TRIGGER')
                </ListQuery>
                <DefinitionQuery>
                    with x as
                    (
                    SELECT owner, object_name, object_type
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('TRIGGER')
                    AND object_name = '@name'
                    )
                    SELECT DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner) as ddlcode FROM x
                </DefinitionQuery>
            </Trigger>
            <Job>
                <Query>
                    select count(1) from dba_objects where object_type = 'JOB' AND upper(owner) = upper('@schemaname')
                </Query>
            </Job>
            <Schedule>
                <Query>
                    select count(1) from dba_objects where object_type = 'SCHEDULE' AND upper(owner) =
                    upper('@schemaname')
                </Query>
            </Schedule>
            <Program>
                <Query>
                    select count(1) from dba_objects where object_type = 'PROGRAM' AND upper(owner) =
                    upper('@schemaname')
                </Query>
            </Program>
        </Code_Objects>
        <Storage_Objects>
            <Type>
                <ListQuery>
                    SELECT object_name,STATUS
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('TYPE')
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CASE
                    WHEN INSTR(ddl_stmt, ';', -1) > 0 THEN ddl_stmt
                    ELSE ddl_stmt || ';'
                    END AS ddl_with_semicolon
                    FROM (
                    SELECT DBMS_METADATA.GET_DDL('TYPE', '@name', upper('@schemaname')) AS ddl_stmt
                    FROM dual
                    ) t
                </DefinitionQuery>
            </Type>
            <Sequence>
                <ListQuery>
                    SELECT object_name,STATUS FROM dba_objects WHERE owner = upper('@schemaname')
                    AND object_type IN ('SEQUENCE') and lower(object_name) not like lower('ISEQ%') and
                    lower(object_name) not like lower('%$%')
                </ListQuery>
                <DefinitionQuery>
                    select lower(sequence_name) from (
                    select 'create sequence '||SEQUENCE_OWNER||'.'||SEQUENCE_NAME||' minvalue '||MIN_VALUE||' maxvalue
                    '||decode(MAX_VALUE,9999999999999999999999999999,99999999999999999)||
                    ' increment by '||INCREMENT_BY||' start with '||LAST_NUMBER||' CACHE '||CACHE_SIZE||' NO CYCLE;' as
                    sequence_name
                    from dba_sequences where SEQUENCE_OWNER=upper('@schemaname') and upper(SEQUENCE_NAME) = '@name' and
                    lower(SEQUENCE_NAME) not like lower('ISEQ%'))
                </DefinitionQuery>
            </Sequence>
            <Table>
                <ListQuery>
                    select DISTINCT OBJECT_NAME,STATUS from dba_objects a where NOT exists (select 1 from dba_mviews MV
                    where
                    MV.MVIEW_NAME=a.object_name AND MV.OWNER=upper('@schemaname')) AND A.OWNER=upper('@schemaname') and
                    OBJECT_NAME not like '%$%' and OBJECT_NAME not like 'SYS_%' AND A.OBJECT_TYPE='TABLE' and
                    TEMPORARY='N' ORDER BY 1
                </ListQuery>
                <DefinitionQuery>
                    with x as
                    (
                    SELECT owner, object_name, object_type
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('TABLE')
                    AND object_name = '@name'
                    AND object_name not in (select mview_name From dba_mviews where owner=upper('@schemaname')) and
                    TEMPORARY='N'
                    )
                    SELECT concat(DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner),';') as ddlcode FROM x
                </DefinitionQuery>
            </Table>
            <Partition>
                <ListQuery>
                    SELECT table_name,status
                    FROM all_tables
                    WHERE partitioned = 'YES'
                    AND upper(owner) = upper('@schemaname')
                    and table_name not like '%$%'
                </ListQuery>
                <DefinitionQuery>
                    SELECT CONCAT(DBMS_METADATA.GET_DDL('TABLE', table_name, owner),';') AS partition_table_def
                    FROM all_tables
                    WHERE partitioned = 'YES'
                    AND upper(owner) = upper('@schemaname')
                    AND upper(table_name) = upper('@name')
                </DefinitionQuery>
            </Partition>
            <Not_Null_Constraint>
                <ListQuery>
                    select TABLE_NAME||'-'||column_name,'ENABLED'
                    from
                    all_tab_columns
                    where
                    nullable = 'N'
                    and owner in UPPER('@schemaname') and TABLE_NAME not like '%$%'
                    order by
                    owner,
                    table_name,
                    column_name
                </ListQuery>
                <DefinitionQuery>
                    select
                    'alter table ' || owner || '.' || TABLE_NAME || ' ALTER COLUMN ' || column_name || ' SET not null;'
                    from
                    all_tab_columns
                    where
                    nullable = 'N'
                    and owner in UPPER('@schemaname')
                    and TABLE_NAME||'-'||column_name = UPPER('@name')
                    order by
                    owner,
                    table_name,
                    column_name
                </DefinitionQuery>
            </Not_Null_Constraint>
            <Primary_Key>
                <ListQuery>
                    SELECT
                    ac.TABLE_NAME||'-'||ac.constraint_name,STATUS
                    FROM
                    all_constraints ac,
                    (
                    SELECT
                    acc.owner,
                    acc.table_name,
                    acc.constraint_name,
                    listagg(acc.column_name, ',') WITHIN GROUP (
                    ORDER BY
                    acc.position) AS col_list
                    FROM
                    all_cons_columns acc
                    GROUP BY
                    acc.owner,
                    acc.table_name,
                    acc.constraint_name ) ccl
                    WHERE
                    ac.owner IN UPPER('@schemaname')
                    AND ac.constraint_type = 'P'
                    AND ac.owner = ccl.owner
                    AND ac.table_name = ccl.table_name
                    AND ac.constraint_name = ccl.constraint_name
                    AND ac.constraint_name NOT LIKE '%$%' and ac.table_name not like '%$%'
                    ORDER BY
                    ac.owner
                </ListQuery>
                <DefinitionQuery>
                    select
                    'alter table ' || ac.owner || '.' || ac.table_name || ' add constraint ' || ac.constraint_name || '
                    primary key(' || ccl.col_list || ');'
                    from
                    all_constraints ac,
                    (
                    select
                    acc.owner,
                    acc.table_name,
                    acc.constraint_name,
                    listagg(acc.column_name,
                    ',') within group (
                    order by acc.position) as col_list
                    from
                    all_cons_columns acc
                    group by
                    acc.owner,
                    acc.table_name,
                    acc.constraint_name ) ccl
                    where
                    ac.owner in (upper('@schemaname'))
                    and ac.TABLE_NAME||'-'||ac.constraint_name = ('@name')
                    and ac.constraint_type = 'P'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name and ac.constraint_name NOT LIKE '%$%'
                    order by
                    ac.owner
                </DefinitionQuery>
            </Primary_Key>
            <Unique_Constraint>
                <ListQuery>
                    select
                    ac.TABLE_NAME||'-'||ac.constraint_name,STATUS
                    from
                    all_constraints ac,
                    (
                    select
                    acc.owner,
                    acc.table_name,
                    acc.constraint_name,
                    listagg(acc.column_name,
                    ',') within group (
                    order by acc.position) as col_list
                    from
                    all_cons_columns acc
                    group by
                    acc.owner,
                    acc.table_name,
                    acc.constraint_name ) ccl
                    where
                    ac.owner in UPPER('@schemaname')
                    and ac.constraint_type = 'U'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name and ac.constraint_name NOT LIKE '%$%' and ac.table_name
                    not like '%$%'
                    order by
                    ac.owner
                </ListQuery>
                <DefinitionQuery>
                    select
                    'alter table ' || ac.owner || '.' || ac.table_name || ' add constraint ' || ac.constraint_name || '
                    UNIQUE NULLS NOT DISTINCT (' || ccl.col_list || ');'
                    from
                    all_constraints ac,
                    (
                    select
                    acc.owner,
                    acc.table_name,
                    acc.constraint_name,
                    listagg(acc.column_name,
                    ',') within group (
                    order by acc.position) as col_list
                    from
                    all_cons_columns acc
                    group by
                    acc.owner,
                    acc.table_name,
                    acc.constraint_name ) ccl
                    where
                    ac.owner in UPPER('@schemaname')
                    and ac.TABLE_NAME||'-'||ac.constraint_name = UPPER('@name')
                    and ac.constraint_type = 'U'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name and ac.constraint_name NOT LIKE '%$%'
                    order by
                    ac.owner
                </DefinitionQuery>
            </Unique_Constraint>
            <Foreign_Key>
                <ListQuery>
                    WITH CTE AS(
                    SELECT
                    ac.table_name,
                    ac.constraint_name,status,
                    'ALTER TABLE ' || ac.owner || '.' || ac.table_name || ' ADD CONSTRAINT ' || ac.constraint_name || '
                    FOREIGN KEY (' ||
                    (
                    SELECT
                    LISTAGG(alc.column_name || '', ',')
                    WITHIN GROUP (
                    ORDER BY alc.position)
                    FROM
                    all_cons_columns alc
                    WHERE
                    alc.constraint_name = ac.R_constraint_name
                    AND alc.owner = ac.R_owner)
                    || ')' || ' REFERENCES ' ||
                    (
                    SELECT
                    LISTAGG(aLC.owner || '.' || aLC.table_name || '', ',')
                    WITHIN GROUP (
                    ORDER BY alc.position)
                    FROM
                    all_cons_columns alc
                    WHERE
                    alc.constraint_name = ac.r_constraint_name
                    AND alc.owner = ac.r_owner)
                    || '(' || (
                    SELECT
                    LISTAGG(alc.column_name || '', ',')
                    WITHIN GROUP (
                    ORDER BY alc.position)
                    FROM
                    all_cons_columns alc
                    WHERE
                    alc.constraint_name = ac.r_constraint_name
                    AND alc.owner = ac.r_owner)|| ')' ||
                    ';'
                    FROM
                    all_constraints ac
                    JOIN all_cons_columns acc ON
                    ac.owner = acc.owner
                    AND ac.table_name = acc.table_name
                    AND ac.constraint_name = acc.constraint_name
                    WHERE
                    ac.owner IN UPPER('@schemaname') AND ac.constraint_name NOT LIKE '%$%' and ac.table_name not like
                    '%$%'
                    AND ac.constraint_type = 'R'
                    ORDER BY
                    ac.owner)
                    SELECT
                    DISTINCT CTE.TABLE_NAME||'-'||CTE.constraint_name,status
                    FROM
                    CTE
                </ListQuery>
                <DefinitionQuery>
                    with CTE as(
                    select
                    'ALTER TABLE ' || ac.owner || '.' || ac.table_name || ' ADD CONSTRAINT ' || ac.constraint_name || '
                    FOREIGN KEY (' ||
                    (
                    select
                    LISTAGG(alc.column_name || '',
                    ',')
                    within group (
                    order by alc.position)
                    from
                    all_cons_columns alc
                    where
                    alc.constraint_name = ac.constraint_name
                    and alc.owner = ac.owner)

                    || ')' || ' REFERENCES ' ||

                    /*(SELECT aLC.owner || '.' || aLC.table_name
                    FROM all_cons_columns alc
                    WHERE alc.constraint_name = ac.r_constraint_name AND alc.owner = ac.r_owner)*/
                    (
                    select
                    LISTAGG(aLC.owner || '.' || aLC.table_name || '',
                    ',')
                    within group (
                    order by alc.position)
                    from
                    all_cons_columns alc
                    where
                    alc.constraint_name = ac.r_constraint_name
                    and alc.owner = ac.r_owner)
                    || '(' || (
                    select
                    LISTAGG(alc.column_name || '',
                    ',')
                    within group (
                    order by alc.position)
                    from
                    all_cons_columns alc
                    where
                    alc.constraint_name = ac.r_constraint_name
                    and alc.owner = ac.r_owner)|| ')' ||
                    ';'
                    from
                    all_constraints ac
                    join all_cons_columns acc on
                    ac.owner = acc.owner
                    and ac.table_name = acc.table_name
                    and ac.constraint_name = acc.constraint_name
                    where
                    ac.owner in UPPER('@schemaname')
                    and ac.TABLE_NAME||'-'||ac.constraint_name = UPPER('@name')
                    and ac.constraint_type = 'R'
                    order by
                    ac.owner)
                    select
                    distinct *
                    from
                    CTE
                </DefinitionQuery>
            </Foreign_Key>
            <Default_Constraint>
                <ListQuery>
                    select TABLE_NAME||'-'||column_name,'ENABLED'
                    from
                    all_tab_columns
                    where
                    data_default is not null
                    and owner in UPPER('@schemaname')
                    and TABLE_NAME not like '%$%'
                </ListQuery>
                <DefinitionQuery>
                    select
                    'alter table ' || owner || '.' || TABLE_NAME || ' ALTER COLUMN ' || column_name || ' SET DEFAULT ',
                    data_default,';'
                    from
                    all_tab_columns
                    where
                    data_default is not null
                    and owner in UPPER('@schemaname')
                    and TABLE_NAME||'-'||column_name = ('@name')
                </DefinitionQuery>
            </Default_Constraint>
            <Check_Constraint>
                <ListQuery>
                    select ac.table_name||'-'||ac.constraint_name,STATUS
                    from
                    all_constraints ac,
                    all_cons_columns acc
                    where
                    ac.constraint_type = 'C'
                    and ac.owner = acc.owner
                    and ac.table_name = acc.table_name
                    and ac.constraint_name = acc.constraint_name
                    and ac.owner in UPPER('@schemaname')
                    and (AC.TABLE_NAME || '.' || acc.column_name) not in (
                    select
                    distinct (TABLE_NAME || '.' || COLUMN_NAME)
                    from
                    all_tab_columns
                    where
                    all_tab_columns.nullable = 'N')
                    and ac.constraint_name not like '%$%' and ac.table_name not like '%$%'
                    order by
                    ac.owner
                </ListQuery>
                <DefinitionQuery>
                    select
                    'alter table ' || ac.owner || '.' || ac.table_name || ' add constraint ' || ac.constraint_name || '
                    check '||'(',
                    ac.search_condition,
                    ')'||';'
                    from
                    all_constraints ac,
                    all_cons_columns acc
                    where
                    ac.constraint_type = 'C'
                    and ac.owner = acc.owner
                    and ac.table_name = acc.table_name
                    and ac.constraint_name = acc.constraint_name
                    and ac.owner in UPPER('@schemaname')
                    and ac.table_name||'-'||ac.constraint_name = UPPER('@name')
                    and (AC.TABLE_NAME || '.' || acc.column_name) not in (
                    select
                    distinct (TABLE_NAME || '.' || COLUMN_NAME)
                    from
                    all_tab_columns
                    where
                    all_tab_columns.nullable = 'N')
                    and ac.constraint_name not like '%$%'
                    order by
                    ac.owner
                </DefinitionQuery>
            </Check_Constraint>
            <Index>
                <ListQuery>
                    SELECT
                    table_name||'-'||index_name,status
                    FROM
                    (
                    SELECT
                    schema_name,
                    table_name,
                    table_type,
                    index_cols,
                    index_name,
                    uniqueness,
                    constraint_type,status,
                    lower('create' || CASE WHEN UNIQUENESS = 'NONUNIQUE' THEN ' index ' ELSE ' UNIQUE index ' END ||
                    INDEX_NAME || ' on ' || schema_name || '.' || table_name || '(' || index_cols || ');') IDX_DEF
                    FROM
                    (
                    SELECT
                    *
                    FROM
                    (
                    WITH cols AS (
                    SELECT
                    idx.table_owner AS schema_name,
                    idx.table_name,
                    idx.index_name,
                    cols.column_name,
                    cols.column_position,
                    idx.uniqueness,status,
                    decode(cols.descend,
                    'ASC',
                    '',
                    ' ' || cols.descend) descend
                    FROM
                    DBA_INDEXES idx,
                    DBA_IND_COLUMNS cols
                    WHERE
                    idx.owner = cols.index_owner
                    AND idx.table_name = cols.table_name
                    AND idx.index_name = cols.index_name
                    AND idx.table_owner

                    IN (upper('@schemaname'))
                    AND idx.table_name NOT LIKE '%$%'

                    ),
                    expr AS (
                    SELECT
                    extractValue(xs.object_value,
                    '/ROW/TABLE_NAME') AS table_name

                    ,
                    extractValue(xs.object_value,
                    '/ROW/INDEX_NAME') AS index_name

                    ,
                    extractValue(xs.object_value,
                    '/ROW/COLUMN_EXPRESSION') AS column_expression

                    ,
                    extractValue(xs.object_value,
                    '/ROW/COLUMN_POSITION') AS column_position
                    FROM
                    (
                    SELECT
                    XMLTYPE(

                    DBMS_XMLGEN.GETXML( 'SELECT table_name, index_name, column_expression, column_position FROM

                    DBA_IND_EXPRESSIONS WHERE table_name not like ''%$%'' '

                    || ' union all SELECT null, null, null, null FROM dual '

                    )

                    ) AS xml
                    FROM
                    DUAL

                    ) x

                    ,
                    TABLE(XMLSEQUENCE(EXTRACT(x.xml,
                    '/ROWSET/ROW'))) xs

                    )
                    SELECT
                    cols.status,
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE AS table_type,
                    listagg(CASE
                    WHEN cols.column_name LIKE 'SYS_N%' THEN expr.column_expression || cols.descend
                    ELSE

                    cols.column_name || cols.descend
                    END,
                    ', ') WITHIN GROUP(
                    ORDER BY
                    cols.column_position) AS Index_Cols,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    FROM
                    cols
                    LEFT JOIN expr ON
                    cols.table_name = expr.table_name
                    AND cols.index_name = expr.index_name
                    AND cols.column_position = expr.column_position
                    LEFT JOIN all_constraints ac ON
                    cols.index_name = ac.constraint_name
                    AND cols.table_name = ac.table_name
                    --AND ac.constraint_type = 'P'
                    AND ac.owner = cols.schema_name
                    LEFT JOIN DBA_OBJECTS DO ON
                    DO.OWNER = cols.schema_name
                    AND do.OBJECT_NAME = cols.table_name
                    AND do.object_type = 'MATERIALIZED VIEW'
                    LEFT JOIN dba_dependencies DD ON
                    do.OBJECT_NAME = dd.NAME
                    GROUP BY
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type,
                    cols.status
                    --, DBMS_METADATA.get_ddl ('INDEX', cols.index_name, cols.schema_name)
                    ORDER BY
                    cols.table_name,
                    cols.index_name,
                    cols.uniqueness)) res
                    WHERE
                    INDEX_NAME not in ( select INDEX_NAME from all_constraints where owner = upper('@schemaname') and
                    constraint_type in ( 'P', 'U'))
                    GROUP BY
                    res.schema_name,
                    res.table_name,
                    res.table_type,
                    res.index_cols,
                    res.index_name,
                    res.uniqueness,
                    res.constraint_type,res.status
                    ORDER BY
                    1,
                    2)tab
                </ListQuery>
                <DefinitionQuery>
                    SELECT IDX_DEF FROM (
                    SELECT
                    schema_name,
                    table_name,
                    table_type,
                    index_cols,
                    index_name,
                    uniqueness,
                    constraint_type,
                    lower('create' || CASE WHEN UNIQUENESS = 'NONUNIQUE' THEN ' index ' ELSE ' UNIQUE index ' END ||
                    INDEX_NAME || ' on ' || schema_name || '.' || table_name || '(' || index_cols || ')' || CASE WHEN
                    UNIQUENESS = 'NONUNIQUE' THEN '' ELSE ' NULLS NOT DISTINCT ' END || ';') IDX_DEF
                    FROM
                    (
                    SELECT
                    *
                    FROM
                    (
                    WITH cols AS (
                    SELECT
                    idx.table_owner AS schema_name,
                    idx.table_name,
                    idx.index_name,
                    cols.column_name,
                    cols.column_position,
                    idx.uniqueness,
                    decode(cols.descend,
                    'ASC',
                    '',
                    ' ' || cols.descend) descend
                    FROM
                    DBA_INDEXES idx,
                    DBA_IND_COLUMNS cols
                    WHERE
                    idx.owner = cols.index_owner
                    AND idx.table_name = cols.table_name
                    AND idx.index_name = cols.index_name
                    AND idx.table_owner IN(upper('@schemaname'))
                    AND idx.table_name||'-'||idx.index_name = UPPER('@name')
                    AND idx.table_name NOT LIKE '%$%'

                    ),
                    expr AS (
                    SELECT
                    extractValue(xs.object_value,
                    '/ROW/TABLE_NAME') AS table_name

                    ,
                    extractValue(xs.object_value,
                    '/ROW/INDEX_NAME') AS index_name

                    ,
                    extractValue(xs.object_value,
                    '/ROW/COLUMN_EXPRESSION') AS column_expression

                    ,
                    extractValue(xs.object_value,
                    '/ROW/COLUMN_POSITION') AS column_position
                    FROM
                    (
                    SELECT
                    XMLTYPE(

                    DBMS_XMLGEN.GETXML( 'SELECT table_name, index_name, column_expression, column_position FROM

                    DBA_IND_EXPRESSIONS WHERE table_name not like ''%$%'' '

                    || ' union all SELECT null, null, null, null FROM dual '

                    )

                    ) AS xml
                    FROM
                    DUAL

                    ) x

                    ,
                    TABLE(XMLSEQUENCE(EXTRACT(x.xml,
                    '/ROWSET/ROW'))) xs

                    )
                    SELECT
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE AS table_type,
                    listagg(CASE
                    WHEN cols.column_name LIKE 'SYS_N%' THEN expr.column_expression || cols.descend
                    ELSE

                    cols.column_name || cols.descend
                    END,
                    ', ') WITHIN GROUP(
                    ORDER BY
                    cols.column_position) AS Index_Cols,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    FROM
                    cols
                    LEFT JOIN expr ON
                    cols.table_name = expr.table_name
                    AND cols.index_name = expr.index_name
                    AND cols.column_position = expr.column_position
                    LEFT JOIN all_constraints ac ON
                    cols.index_name = ac.constraint_name
                    AND cols.table_name = ac.table_name
                    --AND ac.constraint_type = 'P'
                    AND ac.owner = cols.schema_name
                    LEFT JOIN DBA_OBJECTS DO ON
                    DO.OWNER = cols.schema_name
                    AND do.OBJECT_NAME = cols.table_name
                    AND do.object_type = 'MATERIALIZED VIEW'
                    LEFT JOIN dba_dependencies DD ON
                    do.OBJECT_NAME = dd.NAME
                    GROUP BY
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    --, DBMS_METADATA.get_ddl ('INDEX', cols.index_name, cols.schema_name)
                    ORDER BY
                    cols.table_name,
                    cols.index_name,
                    cols.uniqueness)) res
                    --WHERE
                    -- res.UNIQUENESS = 'NONUNIQUE'
                    GROUP BY
                    res.schema_name,
                    res.table_name,
                    res.table_type,
                    res.index_cols,
                    res.index_name,
                    res.uniqueness,
                    res.constraint_type
                    ORDER BY
                    1,
                    2)tab
                </DefinitionQuery>
            </Index>
            <Synonym>
                <ListQuery>
                    SELECT object_name,STATUS
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('SYNONYM')
                </ListQuery>
                <DefinitionQuery>
                    with x as
                    (
                    SELECT owner, object_name, object_type
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('SYNONYM')
                    AND object_name like '@name'
                    )
                    SELECT
                    CASE
                    WHEN INSTR(ddlcode, ';', -1) > 0 THEN ddlcode
                    ELSE ddlcode || ';'
                    END AS ddl_with_semicolon
                    FROM (
                    SELECT DBMS_METADATA.get_ddl (object_type, object_name, owner) as ddlcode FROM X
                    ) T
                </DefinitionQuery>
            </Synonym>
            <View>
                <ListQuery>
                    SELECT object_name,status FROM dba_objects WHERE owner = upper('@schemaname') AND object_type IN
                    ('VIEW')
                    and
                    object_name not like '%$%'
                </ListQuery>
                <DefinitionQuery>
                    with x as
                    (
                    SELECT owner, object_name, object_type
                    FROM dba_objects
                    WHERE owner = upper('@schemaname')
                    AND object_type IN ('VIEW')
                    AND object_name = '@name'
                    )
                    SELECT concat(DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner),';') as ddlcode FROM x
                </DefinitionQuery>
            </View>
            <Materialized_View>
                <ListQuery>
                    SELECT object_name,status FROM dba_objects WHERE owner = upper('@schemaname') AND object_type IN
                    ('MATERIALIZED VIEW') and object_name not like '%$%'
                </ListQuery>
                <DefinitionQuery>
                    select LOWER('CREATE MATERIALIZED VIEW '||owner||'.'||MVIEW_NAME||' AS '),QUERY ,';' from dba_mviews
                    where
                    upper(owner)=upper('@schemaname') and upper(MVIEW_NAME)=upper('@name')
                </DefinitionQuery>
            </Materialized_View>
            <Temporary_Table>
                <ListQuery>
                    select table_name,status from dba_tables where TEMPORARY = 'Y' AND OWNER = upper('@schemaname') AND
                    table_name NOT
                    LIKE '%$%'
                </ListQuery>
                <DefinitionQuery>
                    WITH CTC
                    AS
                    (
                    select dbms_metadata.get_ddl('TABLE','@name',upper('@schemaname')) AS TEXT from dual
                    )
                    SELECT REPLACE(REPLACE(REPLACE(TEXT,'GLOBAL TEMPORARY ',''),'ON COMMIT DELETE ROWS',';'),'ON COMMIT
                    PRESERVE
                    ROWS',';') AS DDL FROM CTC
                </DefinitionQuery>
            </Temporary_Table>
            <Datatype>
                <Query>
                    select count(1)
                    FROM (
                    select lower(OWNER) as OWNER,lower(TABLE_NAME) as TABLE_NAME,lower(COLUMN_NAME) as
                    COLUMN_NAME,DATA_PRECISION,DATA_SCALE,DATA_LENGTH
                    from dba_tab_columns where OWNER=upper('@schemaname') AND DATA_TYPE='NUMBER' and TABLE_NAME not like
                    '%$%'
                    AND NOT EXISTS (select 1 from dba_views DV WHERE owner=upper('@schemaname') AND
                    DV.VIEW_NAME=dba_tab_columns.TABLE_NAME)
                    AND NOT EXISTS (SELECT 1 FROM DBA_MVIEWS DM WHERE DM.MVIEW_NAME=dba_tab_columns.TABLE_NAME and
                    upper(owner)=upper('@schemaname'))
                    and NOT exists (select 1 from DBA_SYNONYMS DS where owner=upper('@schemaname') and
                    dba_tab_columns.TABLE_NAME=DS.TABLE_NAME)
                    order by TABLE_NAME
                    )
                </Query>
            </Datatype>
            <Column>
                <Query>
                    SELECT count(1) FROM dba_TAB_columns dtc inner join dba_tables dt on dtc.table_name = dt.table_name
                    and dtc.owner = dt.owner WHERE dtc.owner = upper('@schemaname')
                </Query>
            </Column>
            <Dblink>
                <Query>
                    select count(1) from dba_objects where object_type = 'DATABASE LINK' AND upper(owner) =
                    upper('@schemaname')
                </Query>
            </Dblink>
        </Storage_Objects>
    </Extraction_Queries>
    <Database_Exclusion_Query>
        select distinct lower(COLUMN_NAME) from dba_tab_columns
        where OWNER NOT IN ('SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS',
        'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS', 'DVSYS', 'GSMADMIN_INTERNAL',
        'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB', 'WMSYS', 'ORDSYS',
        'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT', 'SYSMAN', 'SCOTT') AND COLUMN_NAME IS
        NOT NULL
        UNION
        select DISTINCT lower(argument_name) from dba_arguments
        where OWNER NOT IN ('SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS',
        'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS', 'DVSYS', 'GSMADMIN_INTERNAL',
        'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB', 'WMSYS', 'ORDSYS',
        'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT', 'SYSMAN', 'SCOTT')
        AND argument_name IS NOT NULL
        UNION
        SELECT distinct lower(OBJECT_name) FROM DBA_OBJECTS
        WHERE OWNER NOT IN( 'SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS',
        'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS',
        'DVSYS', 'GSMADMIN_INTERNAL', 'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM',
        'XDB', 'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS',
        'OWBSYS', 'OWBSYS_AUDIT', 'SYSMAN', 'SCOTT')
        AND object_type NOT IN ('TRIGGER','LOB','TABLE PARTITION','INDEX PARTITION','JOB','EVALUATION CONTEXT','RULE
        SET','QUEUE','TYPE BODY','PACKAGE BODY')
        AND object_name NOT LIKE 'SYS%'
        AND object_name NOT LIKE '%$%'
        AND OBJECT_name IS NOT NULL
        UNION
        SELECT lower(object_name||'.'||PROCEDURE_NAME) FROM DBA_PROCEDURES
        WHERE OWNER NOT IN( 'SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS',
        'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS',
        'DVSYS', 'GSMADMIN_INTERNAL', 'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM',
        'XDB', 'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS',
        'OWBSYS', 'OWBSYS_AUDIT', 'SYSMAN', 'SCOTT')
        AND object_type IN ('PACKAGE', 'TYPE')
        AND PROCEDURE_NAME != 'NEW'
        AND object_name IS NOT NULL
    </Database_Exclusion_Query>
    <Source_Schemas>
        SELECT username FROM DBA_USERS WHERE username NOT IN ( 'SYSTEM', 'SYS', 'APPQOSSYS',
        'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS', 'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS',
        'DVSYS', 'GSMADMIN_INTERNAL', 'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM',
        'XDB', 'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT', 'SYSMAN',
        'SCOTT') AND Account_status = 'OPEN' order by Username
    </Source_Schemas>
    <Target_Schemas>
         SELECT username FROM DBA_USERS WHERE username NOT IN ( 'SYSTEM', 'SYS', 'APPQOSSYS',
        'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS', 'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS',
        'DVSYS', 'GSMADMIN_INTERNAL', 'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM',
        'XDB', 'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT', 'SYSMAN',
        'SCOTT') AND Account_status = 'OPEN' order by Username
    </Target_Schemas>
<!--    <Deployment_File>-->
<!--        <Table>-->
<!--            SELECT-->
<!--            ('CREATE TABLE ' || table_schema || '.' || table_name || ' (' ||-->
<!--            array_to_string(-->
<!--            ARRAY(-->
<!--            SELECT-->
<!--            ' ' || column_name || ' ' || data_type ||-->
<!--            CASE-->
<!--            WHEN character_maximum_length IS NOT NULL THEN '(' || character_maximum_length || ')'-->
<!--            ELSE ''-->
<!--            END ||-->
<!--            CASE-->
<!--            WHEN is_nullable = 'NO' THEN ' NOT NULL'-->
<!--            ELSE ''-->
<!--            END-->
<!--            FROM-->
<!--            information_schema.columns-->
<!--            WHERE-->
<!--            columns.table_schema = tables.table_schema-->
<!--            AND columns.table_name = tables.table_name-->
<!--            ORDER BY-->
<!--            ordinal_position-->
<!--            ),-->
<!--            ', '-->
<!--            ) || ');')-->
<!--            FROM-->
<!--            information_schema.tables-->
<!--            WHERE-->
<!--            table_schema NOT IN ('pg_catalog', 'information_schema')-->
<!--            AND table_type = 'BASE TABLE'-->
<!--            and upper(table_schema)='@schemaname'-->
<!--            ;-->
<!--        </Table>-->
<!--        <Primary_Key>-->
<!--            SELECT-->

<!--            'ALTER TABLE ' || table_schema || '.' || table_name ||-->

<!--            ' ADD CONSTRAINT ' || constraint_name ||-->

<!--            ' PRIMARY KEY (' || column_list || ');'-->

<!--            FROM (-->

<!--            SELECT-->

<!--            tc.table_schema,-->

<!--            tc.table_name,-->

<!--            tc.constraint_name,-->

<!--            string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list-->

<!--            FROM information_schema.table_constraints AS tc-->

<!--            JOIN information_schema.key_column_usage AS kcu-->

<!--            ON tc.constraint_name = kcu.constraint_name-->

<!--            AND tc.table_schema = kcu.table_schema-->

<!--            AND tc.table_name = kcu.table_name-->

<!--            WHERE upper(tc.constraint_type) = 'PRIMARY KEY'-->

<!--            AND upper(tc.table_schema) = upper('@schemaname')-->

<!--            GROUP BY tc.table_schema, tc.table_name, tc.constraint_name-->

<!--            ) AS primary_keys;-->
<!--        </Primary_Key>-->
<!--        <Unique_Constraint>-->
<!--            SELECT-->

<!--            'ALTER TABLE ' || table_schema || '.' || table_name ||-->

<!--            ' ADD CONSTRAINT ' || constraint_name ||-->

<!--            ' UNIQUE (' || column_list || ');'-->

<!--            FROM (-->

<!--            SELECT-->

<!--            tc.table_schema,-->

<!--            tc.table_name,-->

<!--            tc.constraint_name,-->

<!--            string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list-->

<!--            FROM information_schema.table_constraints AS tc-->

<!--            JOIN information_schema.key_column_usage AS kcu-->

<!--            ON tc.constraint_name = kcu.constraint_name-->

<!--            AND tc.table_schema = kcu.table_schema-->

<!--            AND tc.table_name = kcu.table_name-->

<!--            WHERE upper(tc.constraint_type) = 'UNIQUE'-->

<!--            AND upper(tc.table_schema) = upper('@schemaname')-->
<!--            GROUP BY tc.table_schema, tc.table_name, tc.constraint_name-->

<!--            ) AS unique_constraints;-->
<!--        </Unique_Constraint>-->
<!--        <Foreign_Key>-->
<!--            WITH cte AS (-->
<!--            SELECT-->
<!--            conname AS constraint_name,-->
<!--            ns.nspname AS schema_name,-->
<!--            tbl.relname AS table_name,-->
<!--            a.attname AS column_name,-->
<!--            nsf.nspname AS referenced_schema,-->
<!--            reftbl.relname AS referenced_table,-->
<!--            fa.attname AS referenced_column-->
<!--            FROM-->
<!--            pg_constraint AS c-->
<!--            JOIN-->
<!--            pg_class AS tbl ON c.conrelid = tbl.oid-->
<!--            JOIN-->
<!--            pg_namespace AS ns ON tbl.relnamespace = ns.oid-->
<!--            JOIN-->
<!--            pg_attribute AS a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid-->
<!--            JOIN-->
<!--            pg_class AS reftbl ON c.confrelid = reftbl.oid-->
<!--            JOIN-->
<!--            pg_namespace AS nsf ON reftbl.relnamespace = nsf.oid-->
<!--            JOIN-->
<!--            pg_attribute AS fa ON fa.attnum = ANY(c.confkey) AND fa.attrelid = c.confrelid-->
<!--            )-->
<!--            SELECT-->
<!--            'ALTER TABLE ' || schema_name || '.' || table_name || ' ADD CONSTRAINT ' || constraint_name ||-->
<!--            ' FOREIGN KEY (' || column_name || ') REFERENCES ' || referenced_schema || '.' || referenced_table ||-->
<!--            ' (' || referenced_column || ');'-->
<!--            FROM-->
<!--            cte where upper(schema_name) = upper('@schemaname');-->
<!--        </Foreign_Key>-->
<!--        <Not_Null_Constraint>-->

<!--            SELECT-->
<!--            'ALTER TABLE ' || table_schema || '.' || table_name || ' ALTER COLUMN ' || column_name || ' SET NOT NULL;'-->

<!--            FROM information_schema.columns-->

<!--            WHERE upper(is_nullable) = 'NO'-->

<!--            AND upper(table_schema) = upper('@schemaname')-->
<!--            ;-->
<!--        </Not_Null_Constraint>-->
<!--        <Check_Constraint>-->
<!--            with cte as(select-->
<!--            n.nspname AS schema_name,-->
<!--            conname AS constraint_name,-->
<!--            conrelid::regclass AS table_name,-->
<!--            pg_get_constraintdef(c.oid) AS check_expression-->
<!--            FROM pg_constraint c-->
<!--            JOIN pg_namespace n ON n.oid = c.connamespace-->
<!--            WHERE confrelid = 0 AND contype = 'c'-->
<!--            )select 'alter table '||''||table_name||''||' add constraint '||''||constraint_name||' check '||''||-->
<!--            check_expression ||''||';'-->
<!--            from cte where upper(cte.schema_name) = upper('@schemaname');-->
<!--        </Check_Constraint>-->
<!--        <Default_Constraint>-->
<!--            SELECT-->
<!--            'alter table ' ||table_schema||'.'||table_name||' ALTER COLUMN '||column_name||' SET DEFAULT '||-->
<!--            column_default ||''||';'-->
<!--            FROM information_schema.columns-->
<!--            WHERE column_default IS NOT null-->
<!--            and upper(table_schema)=upper('@schemaname') ;-->
<!--        </Default_Constraint>-->
<!--        <Index>-->
<!--            SELECT-->
<!--            indexdef||';' AS index_definition-->
<!--            FROM-->
<!--            pg_indexes-->
<!--            WHERE-->
<!--            schemaname NOT IN ('pg_catalog', 'information_schema')-->
<!--            and upper(schemaname)=upper('@schemaname')-->
<!--            ;-->
<!--        </Index>-->
<!--        <View>-->
<!--            SELECT-->

<!--            'create view'||' '||schemaname||'.'||viewname||' '||' as'||' ' ||definition AS view_definition-->
<!--            FROM-->
<!--            pg_views-->
<!--            WHERE-->
<!--            schemaname NOT IN ('pg_catalog', 'information_schema')-->
<!--            and upper(schemaname)= upper('@schemaname');-->
<!--        </View>-->
<!--        <Materialized_View>-->
<!--            SELECT-->
<!--            'create materialized view'||' '||schemaname||'.'||matviewname||' '||' as'||' ' || definition AS-->
<!--            materialized_view_definition-->
<!--            FROM-->
<!--            pg_matviews-->
<!--            WHERE-->
<!--            schemaname NOT IN ('pg_catalog', 'information_schema')-->
<!--            and upper(schemaname)=upper('@schemaname')-->
<!--        </Materialized_View>-->
<!--        <Procedure>-->
<!--            SELECT-->
<!--            pg_get_functiondef(p.oid) AS ddl-->
<!--            FROM-->
<!--            pg_proc p-->
<!--            JOIN-->
<!--            pg_namespace ns ON p.pronamespace = ns.oid-->
<!--            WHERE-->
<!--            ns.nspname NOT IN ('pg_catalog', 'information_schema')-->
<!--            AND p.prokind = 'p'-->
<!--            and ns.nspname=upper('@schemaname')-->
<!--            ;-->
<!--        </Procedure>-->
<!--        <Function>-->
<!--            SELECT-->
<!--            pg_get_functiondef(p.oid) AS ddl-->
<!--            FROM-->
<!--            pg_proc p-->
<!--            JOIN-->
<!--            pg_namespace ns ON p.pronamespace = ns.oid-->
<!--            WHERE-->
<!--            ns.nspname NOT IN ('pg_catalog', 'information_schema')-->
<!--            AND p.prokind = 'f'-->
<!--            and upper(ns.nspname) = upper('@schemaname')-->
<!--        </Function>-->
<!--    </Deployment_File>-->
<!--    <Server_Parameters>-->
<!--        <CPU_Usage>-->
<!--                        WITH-->
<!--                cpu_cores AS (-->
<!--                    SELECT value AS num_cores-->
<!--                    FROM v$osstat-->
<!--                    WHERE stat_name = 'NUM_CPUS'-->
<!--                ),-->
<!--                cpu_usage AS (-->
<!--                    SELECT SUM(value) AS total_cpu_usage-->
<!--                    FROM v$sys_time_model-->
<!--                    WHERE stat_name IN ('DB CPU', 'background cpu time')-->
<!--                ),-->
<!--                elapsed_time AS (-->
<!--                    SELECT value AS elapsed_seconds-->
<!--                    FROM v$sys_time_model-->
<!--                    WHERE stat_name = 'DB time'-->
<!--                )-->
<!--            SELECT-->
<!--                (SELECT num_cores FROM cpu_cores) AS cpu_cores,-->
<!--                ROUND(-->
<!--                    (SELECT total_cpu_usage FROM cpu_usage) /-->
<!--                    ((SELECT num_cores FROM cpu_cores) * (SELECT elapsed_seconds FROM elapsed_time)) * 100,-->
<!--                    2-->
<!--                ) AS-->
<!--            cpu_utilization_percentage-->
<!--            FROM-->
<!--                dual-->
<!--        </CPU_Usage>-->
<!--        <Memory_Allocated>-->
<!--                        WITH-->
<!--                sga_memory AS (-->
<!--                    SELECT SUM(bytes) AS total_sga-->
<!--                    FROM v$sgastat-->
<!--                ),-->
<!--                pga_memory AS (-->
<!--                    SELECT SUM(value) AS total_pga-->
<!--                    FROM v$pgastat-->
<!--                    WHERE name = 'total PGA allocated'-->
<!--                ),-->
<!--                total_physical_memory AS (-->
<!--                    SELECT value AS-->
<!--            total_memory-->
<!--                    FROM v$osstat-->
<!--                    WHERE stat_name = 'PHYSICAL_MEMORY_BYTES'-->
<!--                ),-->
<!--                sga_parameters AS (-->
<!--                    SELECT-->
<!--                        name,-->
<!--                        value/(1024*1024) AS value-->
<!--                    FROM-->
<!--                        v$parameter-->
<!--                    WHERE-->
<!--                        name IN ('sga_target', 'sga_max_size')-->
<!--                )-->
<!--            SELECT-->
<!--                ROUND((SELECT total_sga FROM sga_memory) / (1024 * 1024), 2) AS sga_total_mb,-->
<!--                ROUND((SELECT total_pga FROM pga_memory) / (1024 * 1024), 2) AS pga_total_mb,-->
<!--                ROUND(((SELECT total_sga FROM sga_memory) + (SELECT total_pga FROM pga_memory)) / (1024 * 1024), 2) AS db_total_utlizn_mem_mb,-->
<!--                ROUND((SELECT total_memory FROM total_physical_memory) / (1024 * 1024), 2) AS system_total_memory_mb,-->
<!--                ROUND(((SELECT total_sga FROM sga_memory) + (SELECT total_pga FROM pga_memory)) / (SELECT total_memory FROM total_physical_memory) * 100, 2) AS db_vs_sys_mem_util_percentg,-->
<!--                ROUND((((SELECT total_sga FROM sga_memory)/(1024 * 1024) + (SELECT total_pga FROM pga_memory)/(1024 * 1024)) / ((SELECT value FROM sga_parameters WHERE name = 'sga_max_size') + (SELECT value FROM sga_parameters WHERE name = 'sga_target'))) * 100, 2) AS db_vs_total_db_mem_util_perctg,-->
<!--                (SELECT value FROM sga_parameters WHERE name = 'sga_target') +-->
<!--                (SELECT value FROM sga_parameters WHERE name = 'sga_max_size') AS Db_totoal_Memory-->
<!--            FROM-->
<!--                dual-->
<!--        </Memory_Allocated>-->
<!--        <DB_Size>-->
<!--            SELECT-->
<!--            (SELECT SUM(bytes) / 1024 / 1024 / 1024 FROM dba_data_files) +-->
<!--            (SELECT SUM(bytes) / 1024 / 1024 / 1024 FROM dba_temp_files)-->
<!--            AS total_size_in_gb FROM-->
<!--            dual-->
<!--        </DB_Size>-->
<!--        <TableSpace_Size>-->
<!--            SELECT-->
<!--            tablespace_name,-->
<!--            SUM(bytes) / 1024 / 1024 / 1024 AS size_in_gb-->
<!--            FROM-->
<!--            dba_data_files-->
<!--            GROUP BY-->
<!--            tablespace_name-->
<!--            ORDER BY-->
<!--            tablespace_name-->
<!--        </TableSpace_Size>-->
<!--        <PhysicalwritetotalIOrequest>-->
<!--            WITH Snapshots AS (-->
<!--            SELECT-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID AS BEGIN_SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID AS END_SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME-->
<!--            FROM-->
<!--            DBA_HIST_SNAPSHOT s1-->
<!--            JOIN-->
<!--            DBA_HIST_SNAPSHOT s2-->
<!--            ON-->
<!--            s2.SNAP_ID = s1.SNAP_ID + 1-->
<!--            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER &#45;&#45; Ensure consecutive snapshots in the same instance-->
<!--            WHERE-->
<!--            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') &#45;&#45; Adjust as needed-->
<!--            )-->
<!--            SELECT-->
<!--            s.INSTANCE_NUMBER,-->
<!--            s.BEGIN_SNAP_ID,-->
<!--            s.BEGIN_INTERVAL_TIME,-->
<!--            s.END_SNAP_ID,-->
<!--            s.END_INTERVAL_TIME,-->
<!--            &#45;&#45; (ss2.VALUE - ss1.VALUE),-->
<!--            (ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))) AS phy_wrt_IO_reqs_per_sec-->
<!--            FROM-->
<!--            Snapshots s-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss1-->
<!--            ON-->
<!--            ss1.SNAP_ID = s.BEGIN_SNAP_ID-->
<!--            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss1.STAT_NAME = 'physical write total IO requests'-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss2-->
<!--            ON-->
<!--            ss2.SNAP_ID = s.END_SNAP_ID-->
<!--            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss2.STAT_NAME = 'physical write total IO requests'-->
<!--            ORDER BY-->
<!--            BEGIN_SNAP_ID,INSTANCE_NUMBER, phy_wrt_IO_reqs_per_sec DESC-->
<!--        </PhysicalwritetotalIOrequest>-->
<!--        <PhysicalreadtotalIOrequests>-->
<!--            WITH Snapshots AS (-->
<!--            SELECT-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID AS BEGIN_SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID AS END_SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME-->
<!--            FROM-->
<!--            DBA_HIST_SNAPSHOT s1-->
<!--            JOIN-->
<!--            DBA_HIST_SNAPSHOT s2-->
<!--            ON-->
<!--            s2.SNAP_ID = s1.SNAP_ID + 1-->
<!--            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER &#45;&#45; Ensure consecutive snapshots in the same instance-->
<!--            WHERE-->
<!--            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') &#45;&#45; Adjust as needed-->
<!--            )-->
<!--            SELECT-->
<!--            s.INSTANCE_NUMBER,-->
<!--            s.BEGIN_SNAP_ID,-->
<!--            s.BEGIN_INTERVAL_TIME,-->
<!--            s.END_SNAP_ID,-->
<!--            s.END_INTERVAL_TIME,-->
<!--            &#45;&#45; (ss2.VALUE - ss1.VALUE),-->
<!--            (ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))) AS phy_read_IO_reqs_per_sec-->
<!--            FROM-->
<!--            Snapshots s-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss1-->
<!--            ON-->
<!--            ss1.SNAP_ID = s.BEGIN_SNAP_ID-->
<!--            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss1.STAT_NAME = 'physical read total IO requests'-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss2-->
<!--            ON-->
<!--            ss2.SNAP_ID = s.END_SNAP_ID-->
<!--            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss2.STAT_NAME = 'physical read total IO requests'-->
<!--            ORDER BY-->
<!--            BEGIN_SNAP_ID,INSTANCE_NUMBER, phy_read_IO_reqs_per_sec DESC-->
<!--        </PhysicalreadtotalIOrequests>-->
<!--        <Physicalwritebytes>-->
<!--            WITH Snapshots AS (-->
<!--            SELECT-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID AS BEGIN_SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID AS END_SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME-->
<!--            FROM-->
<!--            DBA_HIST_SNAPSHOT s1-->
<!--            JOIN-->
<!--            DBA_HIST_SNAPSHOT s2-->
<!--            ON-->
<!--            s2.SNAP_ID = s1.SNAP_ID + 1-->
<!--            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER &#45;&#45; Ensure consecutive snapshots in the same instance-->
<!--            WHERE-->
<!--            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') &#45;&#45; Adjust as needed-->
<!--            )-->
<!--            SELECT-->
<!--            s.INSTANCE_NUMBER,-->
<!--            s.BEGIN_SNAP_ID,-->
<!--            s.BEGIN_INTERVAL_TIME,-->
<!--            s.END_SNAP_ID,-->
<!--            s.END_INTERVAL_TIME,-->
<!--            ((ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024) AS-->
<!--            phy_write_per_sec_IN_MB-->
<!--            FROM-->
<!--            Snapshots s-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss1-->
<!--            ON-->
<!--            ss1.SNAP_ID = s.BEGIN_SNAP_ID-->
<!--            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss1.STAT_NAME = 'physical write bytes'-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss2-->
<!--            ON-->
<!--            ss2.SNAP_ID = s.END_SNAP_ID-->
<!--            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss2.STAT_NAME = 'physical write bytes'-->
<!--            ORDER BY-->
<!--            BEGIN_SNAP_ID,INSTANCE_NUMBER, phy_write_per_sec_IN_MB DESC-->
<!--        </Physicalwritebytes>-->
<!--        <Physicalreadbytes>-->
<!--            WITH Snapshots AS (-->
<!--            SELECT-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID AS BEGIN_SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID AS END_SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME-->
<!--            FROM-->
<!--            DBA_HIST_SNAPSHOT s1-->
<!--            JOIN-->
<!--            DBA_HIST_SNAPSHOT s2-->
<!--            ON-->
<!--            s2.SNAP_ID = s1.SNAP_ID + 1-->
<!--            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER &#45;&#45; Ensure consecutive snapshots in the same instance-->
<!--            WHERE-->
<!--            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') &#45;&#45; Adjust as needed-->
<!--            )-->
<!--            SELECT-->
<!--            s.INSTANCE_NUMBER,-->
<!--            s.BEGIN_SNAP_ID,-->
<!--            s.BEGIN_INTERVAL_TIME,-->
<!--            s.END_SNAP_ID,-->
<!--            s.END_INTERVAL_TIME,-->
<!--            ((ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024) AS-->
<!--            phy_read_per_sec_IN_MB-->
<!--            FROM-->
<!--            Snapshots s-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss1-->
<!--            ON-->
<!--            ss1.SNAP_ID = s.BEGIN_SNAP_ID-->
<!--            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss1.STAT_NAME = 'physical read bytes'-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss2-->
<!--            ON-->
<!--            ss2.SNAP_ID = s.END_SNAP_ID-->
<!--            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss2.STAT_NAME = 'physical read bytes'-->
<!--            ORDER BY-->
<!--            BEGIN_SNAP_ID,INSTANCE_NUMBER, phy_read_per_sec_IN_MB DESC-->
<!--        </Physicalreadbytes>-->
<!--        <ToIdentifythetimeinterval>-->
<!--            SELECT-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID AS BEGIN_SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID AS END_SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME-->
<!--            FROM-->
<!--            DBA_HIST_SNAPSHOT s1-->
<!--            JOIN-->
<!--            DBA_HIST_SNAPSHOT s2-->
<!--            ON-->
<!--            s2.SNAP_ID = s1.SNAP_ID + 1-->
<!--            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER &#45;&#45; Ensure consecutive snapshots in the same instance-->
<!--            WHERE-->
<!--            ROWNUM = 1-->
<!--            ORDER BY-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME-->
<!--        </ToIdentifythetimeinterval>-->
<!--        <PhysicalwritetotalIOrequestsMAXandAVG>-->
<!--            WITH Snapshots AS (-->
<!--            SELECT-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID AS BEGIN_SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID AS END_SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME-->
<!--            FROM-->
<!--            DBA_HIST_SNAPSHOT s1-->
<!--            JOIN-->
<!--            DBA_HIST_SNAPSHOT s2-->
<!--            ON-->
<!--            s2.SNAP_ID = s1.SNAP_ID + 1-->
<!--            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER &#45;&#45; Ensure consecutive snapshots in the same instance-->
<!--            WHERE-->
<!--            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') &#45;&#45; Adjust as needed-->
<!--            )-->
<!--            SELECT-->
<!--            &#45;&#45; s.BEGIN_SNAP_ID,-->
<!--            &#45;&#45; s.END_SNAP_ID,-->
<!--            max(sum((ss2.VALUE - ss1.VALUE) /((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) *-->
<!--            24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))) AS max_phy_wrt_IO_reqs_per_sec,-->
<!--            avg(sum((ss2.VALUE - ss1.VALUE) /((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) *-->
<!--            24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))) AS avg_phy_wrt_IO_reqs_per_sec-->
<!--            FROM-->
<!--            Snapshots s-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss1-->
<!--            ON-->
<!--            ss1.SNAP_ID = s.BEGIN_SNAP_ID-->
<!--            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss1.STAT_NAME = 'physical write total IO requests'-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss2-->
<!--            ON-->
<!--            ss2.SNAP_ID = s.END_SNAP_ID-->
<!--            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss2.STAT_NAME = 'physical write total IO requests'-->
<!--            group by BEGIN_SNAP_ID,END_SNAP_ID-->
<!--        </PhysicalwritetotalIOrequestsMAXandAVG>-->
<!--        <PhysicalreadtotalIOrequestsMAXandAVG>-->
<!--            WITH Snapshots AS (-->
<!--            SELECT-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID AS BEGIN_SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID AS END_SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME-->
<!--            FROM-->
<!--            DBA_HIST_SNAPSHOT s1-->
<!--            JOIN-->
<!--            DBA_HIST_SNAPSHOT s2-->
<!--            ON-->
<!--            s2.SNAP_ID = s1.SNAP_ID + 1-->
<!--            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER &#45;&#45; Ensure consecutive snapshots in the same instance-->
<!--            WHERE-->
<!--            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') &#45;&#45; Adjust as needed-->
<!--            )-->
<!--            SELECT-->
<!--            max((ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)))) AS max_phy_read_IO_reqs_per_sec,-->
<!--            avg((ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)))) AS avg_phy_read_IO_reqs_per_sec-->
<!--            FROM-->
<!--            Snapshots s-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss1-->
<!--            ON-->
<!--            ss1.SNAP_ID = s.BEGIN_SNAP_ID-->
<!--            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss1.STAT_NAME = 'physical read total IO requests'-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss2-->
<!--            ON-->
<!--            ss2.SNAP_ID = s.END_SNAP_ID-->
<!--            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss2.STAT_NAME = 'physical read total IO requests'-->
<!--        </PhysicalreadtotalIOrequestsMAXandAVG>-->
<!--        <PhysicalwritebytesMAXandAVG>-->
<!--            WITH Snapshots AS (-->
<!--            SELECT-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID AS BEGIN_SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID AS END_SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME-->
<!--            FROM-->
<!--            DBA_HIST_SNAPSHOT s1-->
<!--            JOIN-->
<!--            DBA_HIST_SNAPSHOT s2-->
<!--            ON-->
<!--            s2.SNAP_ID = s1.SNAP_ID + 1-->
<!--            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER &#45;&#45; Ensure consecutive snapshots in the same instance-->
<!--            WHERE-->
<!--            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') &#45;&#45; Adjust as needed-->
<!--            )-->
<!--            SELECT-->
<!--            max(((ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024)) AS-->
<!--            max_phy_write_per_sec_IN_MB,-->
<!--            avg(((ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024)) AS-->
<!--            avg_write_per_sec_IN_MB-->

<!--            FROM-->
<!--            Snapshots s-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss1-->
<!--            ON-->
<!--            ss1.SNAP_ID = s.BEGIN_SNAP_ID-->
<!--            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss1.STAT_NAME = 'physical write bytes'-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss2-->
<!--            ON-->
<!--            ss2.SNAP_ID = s.END_SNAP_ID-->
<!--            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss2.STAT_NAME = 'physical write bytes'-->
<!--        </PhysicalwritebytesMAXandAVG>-->
<!--        <PhysicalreadbytesForAVGandMAX>-->
<!--            WITH Snapshots AS (-->
<!--            SELECT-->
<!--            s1.INSTANCE_NUMBER,-->
<!--            s1.SNAP_ID AS BEGIN_SNAP_ID,-->
<!--            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,-->
<!--            s2.SNAP_ID AS END_SNAP_ID,-->
<!--            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME-->
<!--            FROM-->
<!--            DBA_HIST_SNAPSHOT s1-->
<!--            JOIN-->
<!--            DBA_HIST_SNAPSHOT s2-->
<!--            ON-->
<!--            s2.SNAP_ID = s1.SNAP_ID + 1-->
<!--            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER &#45;&#45; Ensure consecutive snapshots in the same instance-->
<!--            WHERE-->
<!--            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') &#45;&#45; Adjust as needed-->
<!--            )-->
<!--            SELECT-->
<!--            max(((ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024)) AS-->
<!--            max_phy_read_per_sec_IN_MB,-->
<!--            avg(((ss2.VALUE - ss1.VALUE) /-->
<!--            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +-->
<!--            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +-->
<!--            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +-->
<!--            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024)) AS-->
<!--            avg_phy_read_per_sec_IN_MB-->
<!--            FROM-->
<!--            Snapshots s-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss1-->
<!--            ON-->
<!--            ss1.SNAP_ID = s.BEGIN_SNAP_ID-->
<!--            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss1.STAT_NAME = 'physical read bytes'-->
<!--            JOIN-->
<!--            DBA_HIST_SYSSTAT ss2-->
<!--            ON-->
<!--            ss2.SNAP_ID = s.END_SNAP_ID-->
<!--            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER-->
<!--            AND ss2.STAT_NAME = 'physical read bytes'-->
<!--        </PhysicalreadbytesForAVGandMAX>-->
<!--        <OracleDBVersion>-->
<!--            select * from v$version-->
<!--        </OracleDBVersion>-->
<!--        <Check_DB_Statistics>-->
<!--            SELECT name, value FROM v$parameter WHERE name in ('statistics_level','control_management_pack_access')-->
<!--        </Check_DB_Statistics>-->
<!--    </Server_Parameters>-->
<!--    <Conversion>-->
<!--        <Package>-->
<!--            <Get_package_objects>-->
<!--                SELECT-->
<!--                DISTINCT (ap.object_name||'.'||ap.procedure_name) AS CO_NAME-->
<!--                FROM-->
<!--                all_procedures ap-->
<!--                JOIN all_objects ao ON-->
<!--                ap.object_name = ao.object_name-->
<!--                AND ap.owner = ao.owner-->
<!--                WHERE-->
<!--                ap.owner = UPPER('@schemaname')-->
<!--                AND ao.object_type = 'PACKAGE' and upper(ap.object_name) = upper('@name')-->
<!--                AND ap.procedure_name IS NOT NULL ORDER BY CO_NAME asc-->
<!--            </Get_package_objects>-->
<!--            <Input_Variable>-->
<!--                SELECT-->
<!--                argument_name AS variable_name,-->
<!--                data_type AS variable_data_type,-->
<!--                'Input_variable' AS FETCH_type-->
<!--                FROM-->
<!--                all_arguments-->
<!--                WHERE-->
<!--                lower(owner) = lower('@schemaname')-->
<!--                AND lower(package_name||'.'||object_name) = lower('@packageobject')-->
<!--                AND argument_name IS NOT NULL-->
<!--                ORDER BY-->
<!--                sequence-->
<!--            </Input_Variable>-->
<!--            <Dependent_Storage_Code_Objects>-->
<!--                WITH package_dependencies AS (-->
<!--                SELECT-->
<!--                d.OWNER AS source_owner,-->
<!--                d.NAME AS package_name,-->
<!--                d.REFERENCED_OWNER AS dependent_owner,-->
<!--                d.REFERENCED_NAME AS dependent_name,-->
<!--                d.REFERENCED_TYPE AS dependent_type-->
<!--                FROM-->
<!--                ALL_DEPENDENCIES d-->
<!--                WHERE-->
<!--                d.OWNER = UPPER('@schemaname')-->
<!--                AND d.NAME = upper('@name')-->
<!--                AND d.TYPE = 'PACKAGE BODY'-->
<!--                AND d.REFERENCED_TYPE IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'VIEW', 'TABLE', 'SEQUENCE')-->
<!--                AND d.REFERENCED_OWNER NOT IN ('SYS', 'SYSTEM', 'CTXSYS', 'DBSNMP', 'OUTLN', 'ORDDATA', 'ORDPLUGINS',-->
<!--                'ORDSYS', 'SI_INFORMTN_SCHEMA', 'XDB')-->
<!--                )-->
<!--                SELECT-->

<!--                CASE-->
<!--                WHEN pd1.PROCEDURE_NAME != pd1.dependent_name THEN pd1.dependent_name || '_' || pd1.PROCEDURE_NAME-->
<!--                ELSE pd1.dependent_name-->
<!--                END AS CO_NAME,-->
<!--                pd1.owner,-->
<!--                CASE-->
<!--                WHEN pd1.dependent_type = 'TABLE' THEN 'Dependent_Storage_Objects'-->
<!--                WHEN pd1.dependent_type = 'VIEW' THEN 'Dependent_Storage_Objects'-->
<!--                WHEN pd1.dependent_type = 'MATERIALIZED VIEW'-->
<!--                THEN 'Dependent_Storage_Objects'-->
<!--                ELSE 'Dependent_' || pd1.dependent_type-->
<!--                END AS object_type-->
<!--                FROM-->
<!--                (-->
<!--                SELECT-->
<!--                pd.source_owner,-->
<!--                pd.dependent_owner AS owner,-->
<!--                pd.package_name,-->
<!--                pd.dependent_type,-->
<!--                pd.dependent_name,-->
<!--                CASE-->
<!--                WHEN pd.dependent_type = 'PACKAGE'-->
<!--                AND ap.PROCEDURE_NAME IS NOT NULL THEN ap.PROCEDURE_NAME-->
<!--                WHEN pd.dependent_type IN ('PROCEDURE', 'FUNCTION', 'VIEW', 'TABLE', 'SEQUENCE') THEN pd.dependent_name-->
<!--                END AS PROCEDURE_NAME-->
<!--                FROM-->
<!--                package_dependencies pd-->
<!--                LEFT JOIN-->
<!--                ALL_PROCEDURES ap-->
<!--                ON-->
<!--                pd.dependent_owner = ap.OWNER-->
<!--                AND pd.dependent_name = ap.OBJECT_NAME-->
<!--                AND (ap.OBJECT_TYPE = pd.dependent_type-->
<!--                OR (ap.OBJECT_TYPE = 'PACKAGE'-->
<!--                AND pd.dependent_type IN ('PROCEDURE', 'FUNCTION')))-->
<!--                WHERE-->
<!--                ap.OWNER IS NOT NULL-->
<!--                OR pd.dependent_type IN ('VIEW', 'TABLE', 'SEQUENCE')-->
<!--                ORDER BY-->
<!--                pd.dependent_owner,-->
<!--                pd.package_name,-->
<!--                pd.dependent_name,-->
<!--                ap.PROCEDURE_NAME-->
<!--                ) pd1-->
<!--                WHERE-->
<!--                pd1.PROCEDURE_NAME IS NOT NULL-->
<!--                OR pd1.dependent_type IN ('VIEW', 'TABLE', 'SEQUENCE')-->
<!--            </Dependent_Storage_Code_Objects>-->
<!--            <Package_List>-->
<!--                SELECT OBJECT_NAME AS "PACKAGE_NAME"-->
<!--                FROM ALL_OBJECTS-->
<!--                WHERE OBJECT_TYPE = 'PACKAGE'-->
<!--                AND upper(OWNER) = upper('@schemaname')-->
<!--                ORDER BY OBJECT_NAME-->
<!--            </Package_List>-->

<!--        </Package>-->

<!--        <Procedure>-->
<!--            <Input_Variable>-->
<!--                SELECT-->
<!--                argument_name AS variable_name,-->
<!--                data_type AS variable_data_type,-->
<!--                'Input_Variable'-->
<!--                FROM-->
<!--                all_arguments-->
<!--                WHERE-->
<!--                upper(OWNER)=upper('@schemaname')-->
<!--                and lower(object_name)=lower('@name') AND argument_name IS NOT NULL-->
<!--                ORDER BY-->
<!--                SEQUENCE-->
<!--            </Input_Variable>-->
<!--            <Dependent_Code_Objects>-->
<!--                SELECT DISTINCT REFERENCED_OWNER,-->
<!--                REFERENCED_NAME AS FULL_NAME ,-->
<!--                'Dependent_'||REFERENCED_TYPE AS REFERENCED_TYPE-->
<!--                FROM (-->
<!--                SELECT-->
<!--                LEVEL LVL,-->
<!--                REFERENCED_OWNER,-->
<!--                REFERENCED_NAME,-->
<!--                REFERENCED_TYPE-->
<!--                FROM-->
<!--                ALL_DEPENDENCIES-->
<!--                WHERE-->
<!--                REFERENCED_TYPE IN ('PROCEDURE', 'PACKAGE', 'FUNCTION')-->
<!--                AND REFERENCED_OWNER NOT IN ('QUAD')-->
<!--                START WITH-->
<!--                upper(OWNER) = upper('@schemaname')-->
<!--                AND LOWER(NAME) = LOWER('@name')-->
<!--                CONNECT BY-->
<!--                OWNER = PRIOR REFERENCED_OWNER-->
<!--                AND NAME = PRIOR REFERENCED_NAME-->
<!--                AND TYPE = PRIOR REFERENCED_TYPE-->
<!--                )-->
<!--                WHERE-->
<!--                REFERENCED_TYPE NOT IN ('VIEW', 'MATERIALIZED VIEW')-->
<!--                AND REFERENCED_NAME NOT IN ('SYS_STUB_FOR_PURITY_ANALYSIS', 'STANDARD', 'DUAL')-->
<!--                AND REFERENCED_NAME NOT LIKE 'DBMS_%'-->
<!--                AND REFERENCED_NAME NOT LIKE 'UTL_%'-->
<!--                AND REFERENCED_OWNER NOT IN ('SYS')-->
<!--                ORDER BY-->
<!--                FULL_NAME,-->
<!--                REFERENCED_TYPE-->
<!--            </Dependent_Code_Objects>-->
<!--            <Dependent_Storage_Objects>-->
<!--                SELECT DISTINCT referenced_name AS table_name, owner, 'Dependent_Storage_Objects'-->
<!--                FROM all_dependencies-->
<!--                WHERE type IN ('PROCEDURE', 'FUNCTION')-->
<!--                AND referenced_type IN ('TABLE', 'VIEW', 'SEQUENCE')-->
<!--                AND upper(owner)=upper('@schemaname')-->
<!--                and upper(name) = upper('@name')-->
<!--            </Dependent_Storage_Objects>-->
<!--        </Procedure>-->
<!--        <Function>-->
<!--            <Input_Variable>-->
<!--                SELECT-->
<!--                argument_name AS variable_name,-->
<!--                data_type AS variable_data_type,-->
<!--                'Input_Variable'-->
<!--                FROM-->
<!--                all_arguments-->
<!--                WHERE-->
<!--                upper(OWNER)=upper('@schemaname')-->
<!--                and lower(object_name)=lower('@name') AND argument_name IS NOT NULL-->
<!--                ORDER BY-->
<!--                SEQUENCE-->
<!--            </Input_Variable>-->
<!--            <Dependent_Code_Objects>-->
<!--                SELECT DISTINCT REFERENCED_OWNER,-->
<!--                REFERENCED_NAME AS FULL_NAME ,-->
<!--                'Dependent_'||REFERENCED_TYPE AS REFERENCED_TYPE-->
<!--                FROM (-->
<!--                SELECT-->
<!--                LEVEL LVL,-->
<!--                REFERENCED_OWNER,-->
<!--                REFERENCED_NAME,-->
<!--                REFERENCED_TYPE-->
<!--                FROM-->
<!--                ALL_DEPENDENCIES-->
<!--                WHERE-->
<!--                REFERENCED_TYPE IN ('PROCEDURE', 'PACKAGE', 'FUNCTION')-->
<!--                AND REFERENCED_OWNER NOT IN ('QUAD')-->
<!--                START WITH-->
<!--                upper(OWNER) = upper('@schemaname')-->
<!--                AND LOWER(NAME) = LOWER('@name')-->
<!--                CONNECT BY-->
<!--                OWNER = PRIOR REFERENCED_OWNER-->
<!--                AND NAME = PRIOR REFERENCED_NAME-->
<!--                AND TYPE = PRIOR REFERENCED_TYPE-->
<!--                )-->
<!--                WHERE-->
<!--                REFERENCED_TYPE NOT IN ('VIEW', 'MATERIALIZED VIEW')-->
<!--                AND REFERENCED_NAME NOT IN ('SYS_STUB_FOR_PURITY_ANALYSIS', 'STANDARD', 'DUAL')-->
<!--                AND REFERENCED_NAME NOT LIKE 'DBMS_%'-->
<!--                AND REFERENCED_NAME NOT LIKE 'UTL_%'-->
<!--                AND REFERENCED_OWNER NOT IN ('SYS')-->
<!--                ORDER BY-->
<!--                FULL_NAME,-->
<!--                REFERENCED_TYPE-->
<!--            </Dependent_Code_Objects>-->
<!--            <Dependent_Storage_Objects>-->
<!--                SELECT DISTINCT referenced_name AS table_name, owner, 'Dependent_Storage_Objects'-->
<!--                FROM all_dependencies-->
<!--                WHERE type IN ('PROCEDURE', 'FUNCTION')-->
<!--                AND referenced_type IN ('TABLE', 'VIEW', 'SEQUENCE')-->
<!--                AND upper(owner)=upper('@schemaname')-->
<!--                and upper(name) = upper('@name')-->
<!--            </Dependent_Storage_Objects>-->
<!--        </Function>-->
<!--        <Table>-->
<!--            <Input_Variable>-->
<!--                SELECT OWNER || '.' || table_name || '.' || COLUMN_NAME AS table_column,-->
<!--                DATA_TYPE,-->
<!--                'Table_Input_variable' AS object_type-->
<!--                FROM ALL_TAB_COLUMNS-->
<!--                WHERE UPPER(OWNER) IN (-->
<!--                SELECT UPPER(username)-->
<!--                FROM DBA_USERS-->
<!--                WHERE username NOT IN ('SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS',-->
<!--                'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS', 'DVSYS', 'GSMADMIN_INTERNAL',-->
<!--                'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB',-->
<!--                'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT',-->
<!--                'SYSMAN', 'SCOTT')-->
<!--                AND Account_status = 'OPEN'-->
<!--                )-->
<!--                ORDER BY COLUMN_ID-->
<!--            </Input_Variable>-->
<!--        </Table>-->
<!--        <Temporary_Table>-->
<!--            <Input_Variable>-->
<!--                SELECT-->
<!--                TABLE_NAME,OWNER,-->
<!--                CASE WHEN TEMPORARY ='Y' THEN 'Temporary_Table'-->
<!--                END AS TABLE_STATUS-->
<!--                FROM DBA_TABLES-->

<!--                WHERE TEMPORARY = 'Y'-->
<!--                AND OWNER IN (-->
<!--                SELECT USERNAME-->
<!--                FROM DBA_USERS-->
<!--                WHERE USERNAME NOT IN (-->
<!--                'SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS',-->
<!--                'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS', 'DVSYS', 'GSMADMIN_INTERNAL',-->
<!--                'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB',-->
<!--                'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT',-->
<!--                'SYSMAN', 'SCOTT'-->
<!--                )-->
<!--                AND ACCOUNT_STATUS = 'OPEN'-->
<!--                )-->
<!--                ORDER BY OWNER, TABLE_NAME-->
<!--            </Input_Variable>-->
<!--        </Temporary_Table>-->
<!--    </Conversion>-->
</Queries>