<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
<!--                <Sequence>-->
<!--                    select /*+ PARALLEL(@degree) */ DISTINCT sequence_owner,sequence_name, TO_NUMBER(LAST_NUMBER) - -->
<!--                    TO_NUMBER(INCREMENT_BY) AS sequence_value-->
<!--                    from all_sequences-->
<!--                    where sequence_owner in (upper('@schemaname'))-->
<!--                    AND sequence_name NOT LIKE '%$%'-->
<!--                </Sequence>-->
                <Table>
                    SELECT
                    user_name(uid) as Schema_name, name as Table_Name, case when type = 'U' then 'BASE TABLE' end as Table_Type
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND lower(user_name(t.uid)) = lower('@schemaname')

                </Table>
                <Primary_Key>
                    SELECT user_name(o.uid) as schema_name ,o.name as table_name , i.name AS
                    Primarykey_Name,index_col(o.name, i.indid, c.colid) as Column_name,
                    'ENABLED' AS Status
                    FROM sysobjects o
                    JOIN sysindexes i ON o.id = i.id
                    join syscolumns c on o.id = c.id
                    WHERE o.type = 'U'
                    --and o.name = '@tablename' --- Table Name
                    AND i.status2 &amp; 2 = 2
                    AND lower(user_name(o.uid)) = lower('@schemaname')
                    AND i.indid = 1
                    and index_col(o.name, i.indid, c.colid) is not null


                </Primary_Key>
                <Unique_Constraint>
                   SELECT
                    USER_NAME(o.uid) AS schema_name,
                    o.name AS table_name,
                    i.name AS unique_index_name
                    FROM
                    sysobjects o
                    JOIN
                    sysindexes i ON o.id = i.id
                    WHERE
                    o.type = 'U' -- 'U' indicates user tables
                    AND i.status = 2 -- Filter for unique indexes (status = 2)
                    AND LOWER(USER_NAME(o.uid)) = LOWER('@schemaname') --
                    ORDER BY
                    schema_name, table_name, unique_index_name;


                </Unique_Constraint>
                <Foreign_Key>

                    SELECT user_name(parent_obj.uid),
                    parent_obj.name AS table_name, -- The parent table that holds the foreign key
                    fk_obj.name AS foreign_key_name  -- The name of the foreign key constraint
                    FROM
                    sysreferences r
                    JOIN
                    sysobjects fk_obj ON r.constrid = fk_obj.id -- Join to get foreign key constraint name
                    JOIN
                    sysobjects parent_obj ON r.tableid = parent_obj.id -- Join to get parent (referencing) table
                    WHERE
                    -- parent_obj.name = '@tablename' -- Replace with your table name
                    -- AND
                    user_name(parent_obj.uid) = '@schemaname'


                </Foreign_Key>
                <Not_Null_Constraint>

                    SELECT user_name(obj.uid) as table_schema,obj.name as table_name,col.name AS column_name
                    FROM syscolumns col
                    JOIN sysobjects obj ON col.id = obj.id
                    WHERE col.status &amp; 8 = 0 -- Status 8 indicates NULL constraint
                    --and obj.name = '@tablename' --- Table Name
                    AND obj.type = 'U'
                    AND lower(user_name(obj.uid)) = lower('@schemaname')
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>

                    SELECT
                    user_name(t.uid) as table_schema,
                    t.name AS table_name,
                    col.name AS Column_Name,
                    c.text AS column_default
                    FROM
                    sysobjects t
                    JOIN
                    syscolumns col ON t.id = col.id
                    JOIN
                    sysobjects dc ON dc.id = col.cdefault
                    JOIN
                    syscomments c ON dc.id = c.id
                    WHERE
                    t.type = 'U'
                    --and t.name = '@tablename' --- Table Name
                    AND lower(user_name(t.uid)) = lower('@schemaname')
                    ORDER BY
                    table_schema,table_name,Column_Name;

                </Default_Constraint>
                <Check_Constraint>
                    SELECT
                    user_name(o.uid) as table_schema,
                    o.name AS table_name,
                    co.name As constraint_name,
                    comm.text AS Check_Definition,
                    'ENABLED' AS status
                    FROM
                    sysobjects o
                    JOIN sysconstraints sc ON o.id = sc.tableid
                    JOIN syscolumns c ON sc.colid = c.colid AND o.id = c.id
                    JOIN sysobjects co ON sc.constrid = co.id
                    JOIN syscomments comm ON co.id = comm.id
                    WHERE
                    user_name(o.uid) = '@schemaname' --Replace schemname with your schemaname
                    --and o.name = '@tablename' --- Table Name
                    ORDER BY
                    table_schema, table_name;



                </Check_Constraint>
                <Index>
                     select user_name(o.uid) table_schema,
                    o.name Table_name,
                    i.name Index_name
                    from sysobjects o
                    join sysindexes i ON o.id = i.id
                    where o.type = 'U'
                    and user_name(o.uid) = '@schemaname'
                    and i.status = 0
                    and i.indid &amp; 2 = 2
                    and i.indid != 255
                    order by Table_name
                </Index>
<!--                <Synonym>-->
<!--                    select /*+ PARALLEL(@degree) */ SYNONYM_NAME,TABLE_NAME,TABLE_OWNER from dba_synonyms where-->
<!--                    owner=upper('@schemaname')-->
<!--                </Synonym>-->
                <View>
                     SELECT user_name(uid) as table_schema, name as View_name
                        FROM
                            sysobjects t
                        WHERE
                            t.type = 'V'
                        --	t.name = '@viewname'
                            AND lower(user_name(t.uid)) = lower('@schemaname')
                            AND name NOT LIKE 'sys%' ORDER BY name

                </View>
                <Datatype>
                    SELECT user_name(o.uid) as table_schema,o.name as table_name, c.name as column_name, t.name as
                    datatype ,
                    CASE
                    WHEN t.name IN ('char', 'varchar', 'nchar', 'nvarchar','varbinary','binary') THEN CAST(c.length AS
                    VARCHAR(5))
                    WHEN t.name IN ('decimal', 'numeric') THEN CAST(c.prec AS VARCHAR(5)) + ',' + CAST(c.scale AS
                    VARCHAR(5))
                    ELSE ''
                    END as column_length,
                    c.colid as ORDINAL_POSITION,'Table' AS table_type
                    FROM
                    sysobjects o
                    JOIN
                    syscolumns c ON o.id = c.id
                    JOIN
                    systypes t ON c.usertype = t.usertype
                    WHERE user_name(o.uid) = '@schemaname' --Replace schemname with your schemanam
                    -- and o.name = '@tablename' -- Replace with your table name
                    AND o.type = 'U'
                    ORDER BY
                    table_schema,table_name,ORDINAL_POSITION
                </Datatype>
            </Storage>
<!--            <Code>-->
<!--                <Code_Objects>-->
<!--                    SELECT /*+ PARALLEL(@degree) */-->
<!--                    object_type,-->
<!--                    CASE-->
<!--                    WHEN object_type = 'PACKAGE' THEN object_name || '_' || Method_name-->
<!--                    WHEN object_type = 'TYPE' THEN object_name || '_' || Method_name-->
<!--                    WHEN object_type = 'PROCEDURE' THEN object_name-->
<!--                    WHEN object_type = 'FUNCTION' THEN object_name-->
<!--                    END AS Code_Object_Name-->
<!--                    FROM-->
<!--                    (-->
<!--                    SELECT /*+ PARALLEL(@degree) */-->
<!--                    p.object_type,-->
<!--                    p.object_name,-->
<!--                    CASE-->
<!--                    WHEN p.object_type = 'PACKAGE' THEN p.procedure_name-->
<!--                    WHEN p.object_type = 'TYPE' THEN p.procedure_name-->
<!--                    WHEN p.object_type = 'PROCEDURE' THEN p.object_name-->
<!--                    WHEN p.object_type = 'FUNCTION' THEN p.object_name-->
<!--                    END AS Method_name-->
<!--                    FROM dba_procedures p-->
<!--                    JOIN dba_objects o ON p.object_name = o.object_name AND p.object_type = o.object_type AND p.owner =-->
<!--                    o.owner-->
<!--                    WHERE p.owner = UPPER('@schemaname')-->
<!--                    AND p.OBJECT_TYPE IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'TYPE')-->
<!--                    AND o.status = 'VALID'-->
<!--                    ) a-->
<!--                    WHERE Method_name IS NOT NULL-->
<!--                    ORDER BY object_type, Code_Object_Name-->
<!--                </Code_Objects>-->
<!--                <Trigger>-->
<!--                    SELECT /*+ PARALLEL(@degree) */ table_owner,-->
<!--                    table_name,-->
<!--                    trigger_name-->
<!--                    FROM DBA_TRIGGERS-->
<!--                    WHERE owner = upper('@schemaname') and table_name not like '%$%'-->
<!--                    ORDER BY table_name,-->
<!--                    trigger_name-->
<!--                </Trigger>-->
<!--            </Code>-->
        </Source>
        <Target>
            <Storage>
<!--                <Sequence>-->
<!--                    SELECT-->
<!--                    schema_name = SCHEMA_NAME(iss.schema_id),-->
<!--                    sequence_name = ss.name,-->
<!--                    current_value = cast(ss.current_value as bigint)-->
<!--                    FROM-->
<!--                    sys.sequences AS ss-->
<!--                    JOIN sys.schemas AS iss ON-->
<!--                    ss.schema_id = iss.schema_id-->
<!--                    where-->
<!--                    lower(iss.name) = lower('@schemaname')-->
<!--                    ORDER BY-->
<!--                    schema_name,-->
<!--                    sequence_name;-->
<!--                </Sequence>-->
                <Table>
                    SELECT table_schema,TABLE_NAME,table_type
                    FROM INFORMATION_SCHEMA.TABLES
                    where lower(TABLE_SCHEMA)=lower('@schemaname')
                </Table>
                <Primary_Key>
                    select
                    schema_name(tab.schema_id) as [schema_name],
                    tab.[name] as table_name,
                    pk.[name] as pk_name,
                    substring(column_names, 1, len(column_names)-1) as [columns],
                    CASE
                    WHEN is_disabled = 1 THEN 'DISABLED'
                    ELSE 'ENABLED'
                    END AS Status

                    from sys.tables tab
                    inner join sys.indexes pk
                    on tab.object_id = pk.object_id
                    and pk.is_primary_key = 1
                    cross apply (select col.[name] + ', '
                    from sys.index_columns ic
                    inner join sys.columns col
                    on ic.object_id = col.object_id
                    and ic.column_id = col.column_id
                    where ic.object_id = tab.object_id
                    and ic.index_id = pk.index_id
                    order by col.column_id
                    for xml path ('') ) D (column_names)
                    where schema_name(tab.schema_id)=lower('@schemaname')
                    order by schema_name(tab.schema_id),
                    pk.[name];

                </Primary_Key>
                <Unique_Constraint>
                    SELECT
                    s.name AS owner,
                    t.name AS table_name,
                    kc.name AS constraint_name
                    FROM sys.key_constraints kc
                    JOIN sys.indexes i ON kc.parent_object_id = i.object_id AND kc.unique_index_id = i.index_id
                    JOIN sys.tables t ON kc.parent_object_id = t.object_id
                    JOIN sys.schemas s ON t.schema_id = s.schema_id
                    WHERE kc.type = 'UQ' -- Unique constraints
                    AND s.name = lower('@schemaname')
                    ORDER BY s.name;

                </Unique_Constraint>
                <Foreign_Key>
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    fk.name AS constraint_name

                    FROM
                    sys.foreign_keys fk
                    JOIN
                    sys.tables t ON fk.parent_object_id = t.object_id
                    JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    JOIN
                    sys.tables rt ON fk.referenced_object_id = rt.object_id
                    JOIN
                    sys.schemas rs ON rt.schema_id = rs.schema_id
                    WHERE
                    LOWER(s.name) = LOWER('@schemaname')
                    ORDER BY
                    s.name, t.name, fk.name
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema ,table_name ,column_name
                    FROM information_schema.columns
                    WHERE upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,column_name,column_default
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    and column_default IS NOT NULL
                </Default_Constraint>
                <Check_Constraint>
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    cc.name AS ConstraintName,
                    cc.definition AS CheckDefinition,
                    case when
                    cc.is_disabled = '0' then 'ENABLED' when
                    cc.is_disabled = '1' then 'DISABLED' end AS status
                    FROM
                    sys.check_constraints cc
                    INNER JOIN sys.tables t ON cc.parent_object_id = t.object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    where lower(s.name)=lower('@schemaname')
                    ORDER BY
                    t.name, cc.name;

                </Check_Constraint>
                <Index>
                    WITH IndexColumns AS (
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    i.name AS index_name,
                    c.name AS column_name,
                    ic.key_ordinal AS column_position,
                    i.is_unique AS uniqueness,
                    ic.is_descending_key AS descend
                    FROM
                    sys.indexes i
                    INNER JOIN
                    sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    INNER JOIN
                    sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    INNER JOIN
                    sys.tables t ON t.object_id = i.object_id
                    INNER JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    WHERE
                    s.name = LOWER('@schemaname')
                    AND t.name NOT LIKE '%$%'
                    ),
                    ConstraintType AS (
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    i.name AS index_name,
                    CASE
                    WHEN i.is_primary_key = 1 THEN 'P'
                    ELSE 'NP'
                    END AS constraint_type
                    FROM
                    sys.indexes i
                    INNER JOIN
                    sys.tables t ON i.object_id = t.object_id
                    INNER JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    WHERE
                    s.name = LOWER('@schemaname')
                    )
                    SELECT
                    ic.schema_name,
                    ic.table_name,
                    ic.index_name
                    FROM
                    IndexColumns ic
                    LEFT JOIN
                    ConstraintType ct ON ic.schema_name = ct.schema_name AND ic.table_name = ct.table_name AND
                    ic.index_name = ct.index_name
                    GROUP BY
                    ic.schema_name,
                    ic.table_name,
                    ic.index_name
                    ORDER BY
                    ic.schema_name, ic.table_name, ic.index_name

                </Index>
<!--                <Synonym>-->
<!--                    SELECT s.name AS SYNONYM_NAME,base_object_name AS TABLE_NAME, SCHEMA_NAME(o.schema_id) AS-->
<!--                    TABLE_OWNER-->
<!--                    FROM sys.synonyms s JOIN sys.objects o ON s.object_id = OBJECT_ID(SCHEMA_NAME(o.schema_id) + '.' +-->
<!--                    o.name) WHERE SCHEMA_NAME(o.schema_id) = '@schemaname'-->
<!--                </Synonym>-->
                <View>
                  SELECT SCHEMA_NAME(schema_id) AS owner,name AS view_name FROM sys.views
                    WHERE SCHEMA_NAME(schema_id) = '@schemaname' AND name NOT LIKE '%$%' ORDER BY name
                </View>
                <Datatype>
                    SELECT
                    TABLE_SCHEMA,
                    TABLE_NAME,
                    COLUMN_NAME,
                    DATA_TYPE,
                    CASE
                    WHEN DATA_TYPE IN ('int', 'decimal', 'bigint', 'smallint', 'tinyint', 'numeric') THEN
                    CONCAT(CAST(NUMERIC_PRECISION AS VARCHAR), ',', CAST(NUMERIC_SCALE AS VARCHAR))
                    WHEN DATA_TYPE IN ('nvarchar','varchar')AND CHARACTER_MAXIMUM_LENGTH =-1THEN'4000'
                    WHEN DATA_TYPE IN ('xml') AND CHARACTER_MAXIMUM_LENGTH =-1 THEN '2000'
                    WHEN DATA_TYPE IN ('nvarchar', 'varchar', 'char','nchar') THEN
                    CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR)
                    ELSE NULL
                    END AS column_size,
                    ORDINAL_POSITION,
                    'Table' AS table_type
                    FROM
                    information_schema.columns
                    WHERE
                    LOWER(TABLE_SCHEMA) = LOWER('@schemaname');
                </Datatype>
            </Storage>
<!--            <Code>-->
<!--                <Code_Objects>-->
<!--                    SELECT-->
<!--                    CASE-->
<!--                    WHEN object_type = 'PROCEDURE' THEN 'PROCEDURE'-->
<!--                    WHEN object_type = 'FUNCTION' THEN 'FUNCTION'-->
<!--                    WHEN object_type = 'PACKAGE' THEN 'PACKAGE'-->
<!--                    WHEN object_type = 'TYPE' THEN 'TYPE'-->
<!--                    END AS object_type,-->
<!--                    CASE-->
<!--                    WHEN object_type IN ('PACKAGE', 'TYPE') THEN object_name + '_' + method_name-->
<!--                    ELSE object_name-->
<!--                    END AS Code_Object_Name-->
<!--                    FROM-->
<!--                    (-->
<!--                    SELECT-->
<!--                    CASE-->
<!--                    WHEN ROUTINE_TYPE = 'PROCEDURE' THEN 'PROCEDURE'-->
<!--                    WHEN ROUTINE_TYPE = 'FUNCTION' THEN 'FUNCTION'-->
<!--                    WHEN ROUTINE_TYPE = 'FUNCTION' THEN 'PACKAGE'-->
<!--                    ELSE 'TYPE'-->
<!--                    END AS object_type,-->
<!--                    SPECIFIC_NAME AS object_name,-->
<!--                    ROUTINE_NAME AS method_name-->
<!--                    FROM-->
<!--                    INFORMATION_SCHEMA.ROUTINES-->
<!--                    WHERE-->
<!--                    SPECIFIC_SCHEMA = '@schemaname' AND-->
<!--                    ROUTINE_TYPE IN ('PROCEDURE', 'FUNCTION')-->
<!--                    ) a-->
<!--                    WHERE-->
<!--                    method_name IS NOT NULL-->
<!--                    ORDER BY-->
<!--                    object_type,-->
<!--                    Code_Object_Name;-->
<!--                </Code_Objects>-->
<!--                <Trigger>-->
<!--                    SELECT SCHEMA_NAME(t.schema_id) AS table_owner,t.name AS table_name,tr.name AS trigger_name-->
<!--                    FROM sys.tables t JOIN sys.triggers tr ON t.object_id = tr.parent_id-->
<!--                    WHERE SCHEMA_NAME(t.schema_id) = '@schemaname' AND t.name NOT LIKE '%$%' ORDER BY t.name, tr.name-->
<!--                </Trigger>-->
<!--                &lt;!&ndash;                <Type>&ndash;&gt;-->
<!--                &lt;!&ndash;                    select type_name from dba_types&ndash;&gt;-->
<!--                &lt;!&ndash;                    where owner = upper('@schemaname')&ndash;&gt;-->
<!--                &lt;!&ndash;                    order by type_name&ndash;&gt;-->
<!--                &lt;!&ndash;                </Type>&ndash;&gt;-->
<!--            </Code>-->
        </Target>
    </Validation_Queries>
</Queries>