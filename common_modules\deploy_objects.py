import os, sys, re,json, base64,msal, requests
from azure.identity import DefaultAzureCredential, ClientSecretCredential
from datetime import datetime
from import_file import import_file
from common_modules.api import decrypt_database_details, api_authentication
from common_modules.stored_procedures import get_object_defination, request_insert, request_update
from common_modules.common_functions import deploy_object

TYPE_MAPPING = {
    "AzureBlobStorageReadSettings": "AzureBlobFSReadSettings",
    "AzureBlobStorageWriteSettings": "AzureBlobFSWriteSettings",
    "AzurePostgreSqlTable": "AzurePostgreSqlSink",
    "DelimitedText": "DelimitedTextSink"
}

DEFAULTS = {
    "columnDelimiter": ",",
    "escapeChar": "\\",
    "firstRowAsHeader": True,
    "quoteChar": "\"",
    "rowDelimiter": None,
    "fileExtension": ".csv"
}

def deploy_pipeline_trigger(task_name, project_id, migration_name, iteration_id, schema_name, source_connection_id,
                             target_connection_id, target_schema, object_type, object_name, restart_flag, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = os.path.join(local_root_path, 'config.py')
    request_id = None
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        db_module_path = os.path.join(local_root_path, 'Migrations', migration_name, 'db_connection.py')
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)
            function_call = getattr(import_object, 'connect_database')

            try:
                token_data = api_authentication()
                project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
                project_connection = function_call(project_DB_details)

                pipeline_names = [p.strip().strip("'\"") for p in object_name.split(",")]

                source_DB_details = decrypt_database_details(token_data, project_id, 'Source', source_connection_id)
                tenant_id = source_DB_details.get('host')
                client_id = source_DB_details.get('port')
                client_secret = source_DB_details.get('password')
                synapse_workspace_name = source_DB_details.get('service_name')
                credential = ClientSecretCredential(tenant_id, client_id, client_secret)

                target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)
                workspace_id = target_DB_details.get('db_name')
                synapse_base_url = f"https://{synapse_workspace_name}.dev.azuresynapse.net"

                def get_synapse_resource(resource_type, resource_name):
                    token = credential.get_token("https://dev.azuresynapse.net/.default").token
                    headers = {"Authorization": f"Bearer {token}"}
                    url = f"{synapse_base_url}/{resource_type}/{resource_name}?api-version=2020-12-01"
                    response = requests.get(url, headers=headers)
                    response.raise_for_status()
                    return response.json()

                def extract_datasets_and_linked_services(activities):
                    datasets, linked_services = set(), set()
                    for act in activities:
                        datasets.update(i["referenceName"] for i in act.get("inputs", []))
                        datasets.update(o["referenceName"] for o in act.get("outputs", []))
                        if "linkedServiceName" in act:
                            linked_services.add(act["linkedServiceName"]["referenceName"])
                        if "activities" in act:
                            ds, ls = extract_datasets_and_linked_services(act["activities"])
                            datasets.update(ds)
                            linked_services.update(ls)
                    return datasets, linked_services

                def classify_linked_service(name):
                    return 0 if "source" in name.lower() else 1 if "sink" in name.lower() else 2

                def get_fabric_connections():
                    app = msal.ConfidentialClientApplication(
                        client_id=client_id,
                        client_credential=client_secret,
                        authority=f"https://login.microsoftonline.com/{tenant_id}"
                    )
                    token = app.acquire_token_for_client(scopes=["https://api.fabric.microsoft.com/.default"]).get("access_token")
                    if not token:
                        raise Exception("Failed to acquire Fabric token")
                    headers = {"Authorization": f"Bearer {token}"}
                    response = requests.get("https://api.fabric.microsoft.com/v1/connections", headers=headers)
                    response.raise_for_status()
                    return {conn["displayName"].lower(): conn for conn in response.json().get("value", [])}

                def convert_pipeline(pipeline, resources, fabric_connections):
                    def get_linked_service_name(dataset_name):
                        return resources["datasets"].get(dataset_name, {}).get("properties", {}).get("linkedServiceName", {}).get("referenceName", "")

                    def get_type_properties(dataset_name):
                        return resources["datasets"].get(dataset_name, {}).get("properties", {}).get("typeProperties", {})

                    def find_fabric_connection(synapse_ls_name):
                        synapse_name_lower = synapse_ls_name.lower()
                        for conn_name, conn_data in fabric_connections.items():
                            if conn_name == synapse_name_lower or synapse_name_lower in conn_name or conn_name in synapse_name_lower:
                                return conn_data["id"]
                        return None

                    def get_format_settings(dataset_name):
                        props = get_type_properties(dataset_name)
                        return {
                            "columnDelimiter": props.get("columnDelimiter", DEFAULTS["columnDelimiter"]),
                            "escapeChar": props.get("escapeChar", DEFAULTS["escapeChar"]),
                            "firstRowAsHeader": props.get("firstRowAsHeader", DEFAULTS["firstRowAsHeader"]),
                            "quoteChar": props.get("quoteChar", DEFAULTS["quoteChar"]),
                            "rowDelimiter": props.get("rowDelimiter", DEFAULTS["rowDelimiter"])
                        }

                    def get_location_settings(dataset_name):
                        props = get_type_properties(dataset_name)
                        location = props.get("location", {})
                        return {
                            "fileSystem": location.get("container") or location.get("fileSystem"),
                            "folderPath": location.get("folderPath", ""),
                            "fileName": location.get("fileName")
                        }

                    def convert_activity(activity):
                        source_ds = activity["inputs"][0]["referenceName"]
                        sink_ds = activity["outputs"][0]["referenceName"]
                        src_ls = get_linked_service_name(source_ds)
                        snk_ls = get_linked_service_name(sink_ds)

                        src_conn = find_fabric_connection(src_ls)
                        snk_conn = find_fabric_connection(snk_ls)
                        if not snk_conn or not src_conn:
                            raise ValueError(f"Missing connection for source {src_ls} or sink {snk_ls}")

                        src_format = get_format_settings(source_ds)
                        src_location = get_location_settings(source_ds)
                        snk_dataset = resources["datasets"].get(sink_ds, {}).get("properties", {})
                        snk_dataset_type = snk_dataset.get("type", "")
                        snk_type_properties = snk_dataset.get("typeProperties", {})
                        sink_type = TYPE_MAPPING.get(snk_dataset_type, snk_dataset_type)

                        sink_json = {
                            "type": sink_type,
                            "writeBatchSize": 1000000,
                            "writeBatchTimeout": "00:30:00",
                            "datasetSettings": {
                                "externalReferences": {"connection": snk_conn},
                                "type": snk_dataset_type,
                                "typeProperties": snk_type_properties
                            }
                        }

                        store_settings = {"type": "AzureBlobFSReadSettings", "recursive": True}
                        if src_location.get("fileName"):
                            store_settings["filePath"] = src_location["fileName"]

                        source_location = {
                            "type": "AzureBlobFSLocation",
                            "fileSystem": src_location["fileSystem"],
                            "folderPath": src_location["folderPath"]
                        }
                        if src_location.get("fileName"):
                            source_location["fileName"] = src_location["fileName"]

                        return {
                            "name": activity["name"],
                            "type": "Copy",
                            "dependsOn": activity.get("dependsOn", []),
                            "policy": activity.get("policy", {}),
                            "typeProperties": {
                                "source": {
                                    "type": "DelimitedTextSource",
                                    "storeSettings": store_settings,
                                    "formatSettings": {"type": "DelimitedTextReadSettings", "skipLineCount": 0},
                                    "datasetSettings": {
                                        "type": "DelimitedText",
                                        "typeProperties": {
                                            "location": source_location,
                                            **src_format
                                        },
                                        "externalReferences": {"connection": src_conn}
                                    }
                                },
                                "sink": sink_json,
                                "translator": {
                                    "type": "TabularTranslator",
                                    "typeConversion": True,
                                    "typeConversionSettings": {
                                        "allowDataTruncation": True,
                                        "treatBooleanAsNumber": False
                                    }
                                },
                                "enableStaging": False
                            }
                        }

                    return {"properties": {"activities": [convert_activity(act) for act in pipeline["properties"]["activities"]]}}

                fabric_connections = get_fabric_connections()

                for pipeline_name in pipeline_names:
                    request_id = request_insert(project_connection,None, source_connection_id, task_name, 'Pipeline',
                                    None, object_type)[0]
                    try:
                        pipeline_json = get_synapse_resource("pipelines", pipeline_name)
                        activities = pipeline_json["properties"]["activities"]
                        dataset_names, linked_service_names = extract_datasets_and_linked_services(activities)

                        dataset_jsons = {}
                        dataset_linked_services = set()
                        for name in dataset_names:
                            ds_json = get_synapse_resource("datasets", name)
                            dataset_jsons[name] = ds_json
                            dataset_linked_services.add(ds_json["properties"]["linkedServiceName"]["referenceName"])

                        all_linked_services = sorted(linked_service_names.union(dataset_linked_services),
                                                     key=lambda n: (classify_linked_service(n), n))
                        linked_service_jsons = {n: get_synapse_resource("linkedservices", n) for n in all_linked_services}
                        resources = {"datasets": dataset_jsons, "linkedServices": linked_service_jsons}
                        fabric_pipeline = convert_pipeline(pipeline_json, resources, fabric_connections)

                        encoded = base64.b64encode(json.dumps(fabric_pipeline).encode()).decode()
                        payload = {
                            "name": pipeline_name,
                            "displayName": pipeline_name,
                            "description": "Auto-converted pipeline",
                            "type": "DataPipeline",
                            "definition": {
                                "parts": [{
                                    "path": "pipeline-content.json",
                                    "payload": encoded,
                                    "payloadType": "InlineBase64"
                                }]
                            }
                        }

                        token = credential.get_token("https://api.fabric.microsoft.com/.default").token
                        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
                        res = requests.post(f"https://api.fabric.microsoft.com/v1/workspaces/{workspace_id}/items",
                                            headers=headers, json=payload)

                        deploy_log_folder = os.path.join(root_path, f"PRJ{project_id}SRC", str(iteration_id), 'Deployment_Logs', 'Pipeline')
                        os.makedirs(deploy_log_folder, exist_ok=True)
                        deploy_log_file = os.path.join(deploy_log_folder,
                                                       f"{pipeline_name}_Deploy_{datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss')}.log")

                        with open(deploy_log_file, 'w') as f:
                            if res.status_code == 201:
                                f.write("Pipeline deployed to Microsoft Fabric.\n")
                                f.write("Pipeline ID: " + res.json().get("id", "") + "\n")
                                print(f"{pipeline_name} deployed to Microsoft Fabric.")
                            else:
                                f.write("Deployment failed.\n")
                                f.write("Response: " + json.dumps(res.json(), indent=2))
                                print(f"Deployment failed for {pipeline_name}. Status: {res.status_code}")

                        request_update(project_connection, request_id, 'Completed', None)

                    except Exception as pe:
                        print(f"Error deploying pipeline {pipeline_name}: {str(pe)}")
                        request_update(project_connection, request_id, 'Error', str(pe))

            except Exception as e:
                print(f"Error occurred in pipeline deployment: {str(e)}")
                request_update(project_connection, request_id, 'Error', str(e))
        else:
            print(f"DB module not found: {db_module_path}")
    else:
        print(f"Config path not found: {config_path}")

def deploy_objects_trigger(task_name, project_id, migration_name, iteration_id, schema_name,target_connection_id, target_schema, object_type,
                           object_name, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'

    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)
            function_call = getattr(import_object, 'connect_database')

            project_DB_details = {}
            request_id = ''

            try:
                token_data = api_authentication()
                project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
                project_connection = function_call(project_DB_details)

                request_id = request_insert(project_connection, None, None, task_name, task_name,
                                   schema_name, object_type)[0]

                target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)
                target_function_call = getattr(import_object, 'target_DB_connection')
                target_connection, error = target_function_call(target_DB_details)

                objects_list = str(object_name).replace('"', '').replace("'", '').strip().split(',')

                deploy_errors_list = []
                for object_name in objects_list:
                    try:
                        object_defination_data = get_object_defination(project_connection, iteration_id, None,
                                                                       target_schema,object_type, object_name)

                        for target_tuple in object_defination_data:
                            tgt_ddl_code = target_tuple[4]
                            tgt_ddl_code = re.sub(rf'{schema_name}.', target_schema + '.', tgt_ddl_code,
                                                  flags=re.IGNORECASE | re.DOTALL)
                            deploy_error = deploy_object(target_connection, tgt_ddl_code)
                            status_msgs = ['table can have only one primary key', 'existing object', 'already NOT NULL',
                                           'already indexed', 'already exists', 'column default value expression',
                                           'Duplicate key name ', 'Multiple primary key defined']
                            if deploy_error != '' and all(
                                    str(i).lower() not in str(deploy_error).lower() for i in status_msgs):
                                deploy_errors_list.append(
                                    "Deployment failed for " + object_name + " for the below code" + tgt_ddl_code + "\nError:" + deploy_error)
                            else:
                                deploy_errors_list.append(
                                    "Deployment Success for " + object_name + " for the below code" + tgt_ddl_code + '\n')
                        print(f"Deploy process completed for {object_name}")
                    except Exception as object_error:
                        print(f"Error occurred at deploying object {object_name} : {str(object_error)}")
                deploy_errors_str = '\n'.join(deploy_errors_list)
                output_deployment_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + str(
                    iteration_id) + '/' + 'Deployment_Logs'
                if not os.path.exists(output_deployment_path):
                    os.makedirs(output_deployment_path)

                output_deployment_file_path = output_deployment_path + '/' + object_type + '/' + str(target_DB_details[
                                                                                                         'db_name']) + '_' + target_schema.capitalize() + '_' + object_type + '_Deploy_{}.log'.format(
                    datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))
                if not os.path.exists(output_deployment_path + '/' + object_type):
                    os.makedirs(output_deployment_path + '/' + object_type)
                with open(output_deployment_file_path, 'w') as f:
                    f.write("\n\n{}".format(deploy_errors_str))

                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Completed', None)
            except Exception as error:
                print(f"Error occurred at deploying objects : {str(error)}")

                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Error', str(error))

        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')

