import psycopg2, pymysql,mysql.connector

def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'] , database=db_data['db_name'],
                                  port=db_data['port'])
    return connection



def target_DB_connection(database_data):
    try:
        connection = pymysql.connect(
            host=database_data['host'],
            port=int(database_data['port']),
            user=database_data['name'],
            password=database_data['password'],
            database=database_data['db_name']
        )
        error = ''
    except pymysql.connect.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near Source database connection", e)
    return connection, error


def DB_connection(database_data):
    try:
        connection = pymysql.connect(
            host=database_data['host'],
            port=int(database_data['port']),
            user=database_data['name'],
            password=database_data['password'],
            database=database_data['db_name']
        )
        error = ''
    except pymysql.connect.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near Source database connection", e)
    return connection, error



def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except mysql.connector.DatabaseError as e:
        print("Issue found near Target database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
