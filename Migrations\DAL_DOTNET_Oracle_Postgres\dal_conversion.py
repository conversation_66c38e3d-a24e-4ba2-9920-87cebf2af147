import os, sys, re, shutil
import pandas as pd
from datetime import datetime
import xml.etree.ElementTree as ET
from import_file import import_file
from cryptography.fernet import <PERSON><PERSON><PERSON>
from Encryption_modules.encrypt_decrypt import decrypt_conversion_modules, delete_files_in_directory
from Common_modules.api import decrypt_database_details, api_authentication
from Common_modules.stored_procedures import run_info_insert, request_insert, exe_logging, request_update, \
    source_objects_insertion, target_objects_insertion


def decrypt_conversion_file(file_path, file_encryption_key, working_directory_path):
    f = Fernet(file_encryption_key)
    with open(file_path, 'rb') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = f.decrypt(encrypted)

    if not os.path.exists(working_directory_path):
        os.makedirs(working_directory_path)
    file_name = file_path.split('/')[-1]
    with open(working_directory_path + '/' + file_name, 'wb') as decrypted_file:
        decrypted_file.write(decrypted)


def feature_execution_and(source_data, schema, folder_path, value, storage_category, working_directory_path):
    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    if '&' in str(value[2]):
        excel_list_all = str(value[2]).split('&')
        list_all = []
        for key in excel_list_all:
            if '~' in key:
                est = re.findall(r'~\S+', key)[0]
                key = key.replace(est, '').strip()
                list_all.append(key)
            else:
                key = key.strip()
                list_all.append(key)
        list_all = [i for i in list_all if i != '']
        status_list = []
        for key in list_all:
            if '*' in key or '%' in key or '.' in key:
                key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace(
                    '..',
                    '.').replace(
                    '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
                key = key.replace('(', '\(').replace('%', '.*?').replace(')', '\)').replace('.', '\.')
                key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
            else:
                ignore_list = [':=', ':']
                check_ignore_item = ['True' for i in ignore_list if i in key]
                if check_ignore_item:
                    key_pattern = r'\s*' + key + r'\s*'
                else:
                    key_pattern = r'\b' + key + r'\b'
                key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
        if 0 not in status_list:
            try:
                encrypt_decrypt_path = folder_path + value[6] + '/' + str(value[1]).lower() + '.py'
                encrypt_key = decrypt_conversion_modules()
                decrypt_conversion_file(encrypt_decrypt_path, encrypt_key, working_directory_path)
                import_object = import_file(working_directory_path + '/' + str(value[1]).lower() + '.py')
                delete_files_in_directory(working_directory_path)
                function_name = [i for i in dir(import_object) if i.lower() == str(value[1]).lower()][0]
                function_call = getattr(import_object, function_name.strip())
                output = function_call(source_data, schema)
                source_data = output
            except Exception as e:
                print('Error in conversion ' + str(e) + ' at module ' + str(value[1]).lower())
                source_data = source_data
    return source_data


def feature_execution_pipe(source_data, schema, folder_path, value, storage_category, working_directory_path):
    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    excel_list_any = str(value[2]).split('|')
    list_any = []
    for key in excel_list_any:
        if '~' in key:
            est = re.findall(r'~\S+', key)[0]
            key = key.replace(est, '').strip()
            list_any.append(key)
        else:
            key = key.strip()
            list_any.append(key)
    list_any = [i for i in list_any if i != '']
    status_list = []
    for key in list_any:
        key = key.strip()
        if '*' in key or '%' in key or '.' in key:
            key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace('..',
                                                                                                                  '.').replace(
                '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
            key = key.replace('(', '\(').replace('%', '.*?').replace(')', '\)').replace('.', '\.')
            key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
        else:
            ignore_list = [':=', ':']
            check_ignore_item = ['True' for i in ignore_list if i in key]
            if check_ignore_item:
                key_pattern = r'\s*' + key + r'\s*'
            else:
                key_pattern = r'\b' + key + r'\b'
            key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
    if any(i > 0 for i in status_list):
        try:
            encrypt_decrypt_path = folder_path + value[6] + '/' + str(value[1]).lower() + '.py'
            encrypt_key = decrypt_conversion_modules()
            decrypt_conversion_file(encrypt_decrypt_path, encrypt_key, working_directory_path)
            import_object = import_file(working_directory_path + '/' + str(value[1]).lower() + '.py')
            delete_files_in_directory(working_directory_path)
            function_name = [i for i in dir(import_object) if i.lower() == str(value[1]).lower()][0]
            function_call = getattr(import_object, function_name.strip())
            output = function_call(source_data, schema)
            source_data = output
        except Exception as e:
            print('Error in conversion ' + str(e) + ' at module ' + str(value[1]).lower())
    return source_data


def feature_execution(source_data, schema, folder_path, excel_data, complete_excel_data, storage_category,
                      working_directory_path):
    feature_execution_list = []
    for index, value in excel_data.iterrows():
        if value[3] == 'No Predecessor':
            if value[1] not in feature_execution_list:
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, storage_category,
                                                        working_directory_path)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value, storage_category,
                                                         working_directory_path)
                feature_execution_list.append(value[1])
            else:
                source_data = source_data
                feature_execution_list = feature_execution_list
        else:
            if value[3] in feature_execution_list:
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, storage_category,
                                                        working_directory_path)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value, storage_category,
                                                         working_directory_path)
                feature_execution_list.append(value[1])
            else:
                parent_feature_name = value[3]
                pred_excel_data = complete_excel_data[complete_excel_data['Feature_Name'] == parent_feature_name]
                source_data = feature_execution(source_data, schema, folder_path, pred_excel_data, complete_excel_data,
                                                storage_category,
                                                working_directory_path)
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, storage_category,
                                                        working_directory_path)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value, storage_category,
                                                         working_directory_path)
                feature_execution_list.append(value[3])
                feature_execution_list.append(value[1])
    return source_data


def build_common_object_list(objects_excel_data, object_path):
    object_path_list = []

    object_excel_data = objects_excel_data[objects_excel_data['Object_Id'] == object_path]
    object_path_list.append(object_path)

    if not object_excel_data.empty:
        linked_objects_value = str(object_excel_data.iloc[0]['Linked_Objects'])
        if linked_objects_value != 'nan':
            linked_objects_split = object_excel_data.iloc[0]['Linked_Objects'].split(',')
            if linked_objects_split:
                for path in linked_objects_split:
                    temp_list = build_common_object_list(objects_excel_data, path)
                    if temp_list:
                        object_path_list.extend(temp_list)
    return object_path_list


def pre_features_execution(source_data, schema, path_modules, object_path, excel_data, objects_excel_data,
                           storage_category, working_directory_path):
    object_path_list = build_common_object_list(objects_excel_data, str(object_path + '/' + 'Pre').replace('//', '/'))

    pre_excel_data = excel_data[excel_data['Object_Path'].isin(object_path_list)]
    if len(pre_excel_data) <= 0:
        output = source_data
    else:
        output = feature_execution(source_data, schema, path_modules, pre_excel_data, pre_excel_data, storage_category,
                                   working_directory_path)
    return output


def post_features_execution(source_data, schema, path_modules, object_path, excel_data, objects_excel_data,
                            storage_category, working_directory_path):
    object_path_list = build_common_object_list(objects_excel_data, str(object_path + '/' + 'Post').replace('//', '/'))

    post_excel_data = excel_data[excel_data['Object_Path'].isin(object_path_list)]
    if len(post_excel_data) <= 0:
        output = source_data
    else:
        output = feature_execution(source_data, schema, path_modules, post_excel_data, post_excel_data,
                                   storage_category,
                                   working_directory_path)
    return output


def tuple_execution(tuple, migration_name, schema, object_path, excel_data, rules_excel_data, objects_excel_data,
                    local_root_path, storage_category, working_directory_path):
    path_modules = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/'
    sys.path.append(path_modules)

    support_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'support_modules.py'
    pre_output = pre_features_execution(tuple[1], schema, path_modules, object_path, excel_data, objects_excel_data,
                                        storage_category, working_directory_path)
    pre_output_split = pre_output.split('\n')
    # pre_output_split = [re.sub(r' +', ' ', i) for i in pre_output_split]
    # pre_output_split = [i.strip() for i in pre_output_split if i != '\n']
    # pre_output_split = [i.strip() for i in pre_output_split if i != '']
    pre_output = '\n'.join(pre_output_split)

    current_objects_data = objects_excel_data[
        (objects_excel_data['Migration_Name'] == migration_name) & (
            objects_excel_data['Object_Id'].str.contains(pat=object_path))]

    if '/' in object_path:
        object_path_split = object_path.split('/')
        object_path_length = len(object_path_split)
        filtered_objects_data = pd.DataFrame(columns=current_objects_data.columns)
        for index, row in current_objects_data.iterrows():
            if len(row['Object_Id'].split('/')) == object_path_length:
                filtered_objects_data.loc[len(filtered_objects_data)] = row
    else:
        filtered_objects_data = current_objects_data[
            (current_objects_data['Migration_Name'] == migration_name) &
            (current_objects_data['Object_Id'] == object_path)]

    sys.path.append(support_module_path)
    import_object = import_file(support_module_path)
    create_object_tuples_function_call = getattr(import_object, 'create_object_tuples')

    comment_dict = {}
    if filtered_objects_data.iloc[0]['Object_Process_Style'] == 'Sequential':
        sub_object_names_list = []
        object_path = object_path + '/'
        objects_rules_data = objects_excel_data[
            (objects_excel_data['Migration_Name'] == migration_name) & (
                objects_excel_data['Object_Id'].str.contains(pat=object_path)) & ~(
                objects_excel_data['Object_Id'].str.contains(pat='Pre')) & ~(
                objects_excel_data['Object_Id'].str.contains(pat='Post'))]
        objects_rules_data = objects_rules_data.sort_values("Object_Execution_Order")

        object_path_split = object_path.split('/')
        object_path_split = [i for i in object_path_split if i != '']
        object_path_length = len(object_path_split)
        for index, row in objects_rules_data.iterrows():
            if len(row['Object_Id'].split('/')) == object_path_length + 1:
                sub_object_names_list.append(row['Object_Id'].split('/')[-1])

        for sub_object_name in sub_object_names_list:
            sub_object_path = object_path + sub_object_name
            sub_object_tuple_list, comment_dict, single_quote_comment_dictionary = create_object_tuples_function_call(
                pre_output,
                migration_name,
                sub_object_path,
                rules_excel_data,
                objects_excel_data)
            if sub_object_tuple_list:
                counter = 0
                for sub_tuple in sub_object_tuple_list:
                    sub_tuple_data = sub_tuple[1]
                    sub_tuple_data_split = sub_tuple_data.split('\n')
                    # sub_tuple_data_split = [re.sub(r' +', ' ', i) for i in sub_tuple_data_split]
                    # sub_tuple_data_split = [i.strip() for i in sub_tuple_data_split if i != '\n']
                    # sub_tuple_data_split = [i.strip() for i in sub_tuple_data_split if i != '']
                    sub_tuple_data = '\n'.join(sub_tuple_data_split)

                    if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                        unique_marker = 'qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_US '
                        pre_output = pre_output.replace(sub_tuple_data, unique_marker, 1)
                        sub_output = tuple_execution(sub_tuple, migration_name, schema, sub_object_path,
                                                     excel_data, rules_excel_data, objects_excel_data, local_root_path,
                                                     storage_category, working_directory_path)
                        pre_output = str(pre_output).replace(unique_marker, '\n' + str(sub_output))
                    counter = counter + 1
                else:
                    pre_output = pre_output
            else:
                pre_output = pre_output

    elif filtered_objects_data.iloc[0]['Object_Process_Style'] == 'Mutually Exclusive':
        sub_object_path = object_path + '/'
        sub_object_tuple_list, comment_dict, single_quote_comment_dictionary = create_object_tuples_function_call(
            tuple[1],
            migration_name,
            sub_object_path,
            rules_excel_data,
            objects_excel_data)
        if sub_object_tuple_list:
            counter = 0
            for sub_tuple in sub_object_tuple_list:
                sub_tuple_data = sub_tuple[1]
                sub_tuple_data_split = sub_tuple_data.split('\n')
                # sub_tuple_data_split = [re.sub(r' +', ' ', i) for i in sub_tuple_data_split]
                # sub_tuple_data_split = [i.strip() for i in sub_tuple_data_split if i != '\n']
                # sub_tuple_data_split = [i.strip() for i in sub_tuple_data_split if i != '']
                sub_tuple_data = '\n'.join(sub_tuple_data_split)
                if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                    unique_marker = 'qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_US '
                    pre_output = pre_output.replace(sub_tuple_data, unique_marker, 1)
                    current_object_path = object_path + '/' + str(sub_tuple[0])
                    sub_output = tuple_execution(sub_tuple, migration_name, schema, current_object_path, excel_data,
                                                 rules_excel_data, objects_excel_data, local_root_path,
                                                 storage_category, working_directory_path)
                    pre_output = pre_output.replace(unique_marker, '\n' + str(sub_output))
                counter = counter + 1
            else:
                pre_output = pre_output
        else:
            pre_output = pre_output
    else:
        pre_output = pre_output

    post_output = post_features_execution(pre_output, schema, path_modules, object_path, excel_data, objects_excel_data,
                                          storage_category, working_directory_path)

    return post_output


def dal_conversion(file_name, migration_name, excel_data, rules_excel_data, rules_object_name, object_name,
                   objects_excel_data, local_root_path, storage_category, working_directory_path,
                   source_directory_path):
    object_name_file = file_name.split('/')[-1].strip().split('.')[0].strip().upper()
    f = open(file_name, 'r', encoding='utf-8', errors='ignore')
    source_data = f.read()
    # source_data = "".join([s for s in source_data.strip().splitlines(True) if s.strip()])

    # source_data = re.sub(' +', ' ', source_data)

    support_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'support_modules.py'
    sys.path.append(support_module_path)
    import_object = import_file(support_module_path)
    create_object_tuples_function_call = getattr(import_object, 'create_object_tuples')

    object_path = rules_object_name
    object_tuple_list = create_object_tuples_function_call(source_data, migration_name, object_path, rules_excel_data,
                                                           objects_excel_data)

    final_output = ''
    if object_tuple_list:
        for tuple in object_tuple_list:
            object_path = tuple[0]
            tuple_output = tuple_execution(tuple, migration_name, '', object_path, excel_data, rules_excel_data,
                                           objects_excel_data, local_root_path, storage_category,
                                           working_directory_path)
            final_output = final_output + '\n' + tuple_output
            return source_data, final_output
    else:
        return source_data, final_output


def dal_conversion_trigger(project_id, migration_name, iteration_str, object_category, object_type, storage_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    config_path = local_root_path + '/' + 'Path_Config' + '/' + 'Config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, storage_category.capitalize() + '_Path')

        working_directory_path = getattr(import_object, storage_category.capitalize() + '_Directory_Path')
        sys.path.append(working_directory_path)

        objects_list = []
        code_objects_list = []

        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'extraction_queries.xml'
        tree = ET.parse(xml_path)
        root = tree.getroot()
        code_objects_tags = root.find('Extraction_Queries/Code_Objects')
        for code_tag in code_objects_tags.iter():
            code_objects_list.append(code_tag.tag)
        code_objects_list = [i for i in code_objects_list if i not in ['Code_Objects']]

        object_category_folder = ''
        if object_category == 'Code_Objects':
            objects_list = code_objects_list
            object_category_folder = object_category

        if object_type.capitalize() == 'All':
            objects_list = objects_list
        else:
            objects_list = str(object_type).replace('"', '').replace("'", '').strip().split(',')

        path_excel = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '.csv'
        excel_data = pd.read_csv(path_excel)

        rules_excel = local_root_path + '/' + 'Dynamic_Rules' + '/' + migration_name + '/' + migration_name + '.csv'
        rules_excel_data = pd.read_csv(rules_excel)

        objects_excel = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '_objects_data.csv'
        objects_excel_data = pd.read_csv(objects_excel)

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        token_data = api_authentication()

        sys.path.append(db_module_path)
        import_object = import_file(db_module_path)

        project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
        application_function_call = getattr(import_object, 'connect_database')
        application_conn = application_function_call(project_DB_details)

        request_data = request_insert(application_conn, None, '', 'DAL_Conversion', 'DAL',
                                      'Code_Objects', iteration_str)
        request_id = request_data[0][1]
        iteration_id = iteration_str
        # iteration_id = run_info_insert(application_conn, iteration_str, '', 'All'.capitalize(), 'DAL_Conversion',
        #                                'Code_Objects', None)

        exe_logging(application_conn,
                    object_category_folder + ' DAL Conversion Started for iteration ' + str(iteration_id),
                    iteration_id)

        source_directory_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + str(
            iteration_id) + '/' + 'Source' + '/'
        conversion_directory_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + str(
            iteration_id) + '/' + 'Conversion' + '/'
        # if not os.path.exists(source_directory_path):
        #     os.makedirs(source_directory_path)
        # if not os.path.exists(conversion_directory_path):
        #     os.makedirs(conversion_directory_path)

        execution_log_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + str(
            iteration_id) + '/' + 'Execution_Logs' + '/' + 'Conversion'
        if not os.path.exists(execution_log_path):
            os.makedirs(execution_log_path)

        execution_file_path = execution_log_path + '/' + str(
            object_category) + '_' + str(object_type) + '_execution_log_{}.txt'.format(
            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

        for object_name in objects_list:
            rules_object_name = object_name

            object_rules_data = rules_excel_data[(rules_excel_data['Migration_Name'] == migration_name) & (
                    rules_excel_data['Object_Path'] == rules_object_name)]
            try:
                if not object_rules_data.empty:
                    if os.path.exists(source_directory_path):
                        object_files_list = []
                        for dirpath, dirnames, filenames in os.walk(source_directory_path):
                            for file in filenames:
                                object_files_list.append(os.path.join(dirpath, file))

                        for file_name in object_files_list:
                            print(file_name)
                            file_name = file_name.replace('\\', '/')
                            object_item_name = file_name.split('/')[-1]

                            try:
                                source_data, file_output = dal_conversion(file_name, migration_name, excel_data,
                                                                          rules_excel_data,
                                                                          rules_object_name, object_name,
                                                                          objects_excel_data,
                                                                          local_root_path,
                                                                          storage_category, working_directory_path,
                                                                          source_directory_path)
                                target_file_name = file_name.replace(
                                    'PRJ' + str(project_id) + 'SRC/' + str(iteration_id) + '/Source',
                                    'PRJ' + str(project_id) + 'SRC/' + str(iteration_id) + '/Conversion')

                                if not os.path.exists('/'.join(target_file_name.split('/')[:-1])):
                                    os.makedirs('/'.join(target_file_name.split('/')[:-1]))
                                if file_output not in ['None', '']:
                                    with open(target_file_name, 'w') as f:
                                        f.write(file_output)

                                    source_objects_insertion(application_conn, iteration_id, '', object_item_name,
                                                             object_name,
                                                             source_data, 'VALID',
                                                             None)
                                    target_objects_insertion(application_conn, iteration_id, '', '',
                                                             object_name,
                                                             object_item_name,
                                                             file_output, False, None, None)
                                else:
                                    with open(target_file_name, 'w') as f:
                                        f.write(source_data)
                                conversion_message = 'DAL Conversion completed for ' + object_name + ' ' + str(
                                    file_name.split('/')[-1])
                                with open(execution_file_path, 'a') as f:
                                    f.write("\n{}".format(conversion_message))

                            except Exception as e:
                                print('Error in DAL file conversion' + str(e) + ' at file ' + file_name.split('/')[-1])

                                with open(execution_file_path, 'a') as f:
                                    f.write("\n{}".format(
                                        'Error in DAL file conversion' + str(e) + ' at file ' + file_name.split('/')[-1]))

                        dirs = os.listdir(conversion_directory_path)
                        dirs = [i for i in dirs if os.path.isdir(conversion_directory_path + '/' + i)]
                        for directory in dirs:
                            directory_path = conversion_directory_path + directory
                            shutil.make_archive(directory_path, 'zip', directory_path)
            except Exception as e:
                print('Error Occured in DAL Conversion ', str(e))
        application_conn = application_function_call(project_DB_details)
        exe_logging(application_conn,
                    object_category_folder + ' DAL Conversion ended for iteration ' + str(iteration_id),
                    iteration_id)
        request_update(application_conn, request_id)
    else:
        print("Config file not found")
