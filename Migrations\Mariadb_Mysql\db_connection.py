import psycopg2
import mysql.connector
import mariadb


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


# DB_connection
def DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    database = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        connection = mariadb.connect(
            user=user_name,
            password=password,
            host=host_name,
            port=int(port),
            database=database)
        error = ''
    except mysql.connector.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near Source database connection", e)
    return connection, error


def target_DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    database = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        connection = mysql.connector.connect(
            host=host_name,
            port=port,
            database=database,
            user=user_name,
            password=password
        )
        error = ''
    except mysql.connector.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near Target database connection", e)
    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except mariadb.DatabaseError as e:
        print("Issue found near source database query", e)
        data = None
    except mysql.connector.DatabaseError as e:
        print("Issue found near Target database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
