def schema_insert(connection, schema_name, connection_id):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.schema_insert(%s,%s,%s);fetch all in "dataset";',
                       (schema_name, connection_id, 'dataset'))
    except Exception as err:
        print("Error at schema insert: ", str(err))
    finally:
        connection.commit()
        cursor.close()

def request_insert(connection, iteration_id, connection_id, operation_name, operation_category, schema_name,
                   object_type):
    cursor = connection.cursor()
    try:
        print('call public.request_insert(%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset"',
                       (iteration_id, connection_id, operation_name, operation_category, schema_name, object_type,
                        'dataset'))
        cursor.execute('call public.request_insert(%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset"',
                       (iteration_id, connection_id, operation_name, operation_category, schema_name, object_type,
                        'dataset'))
        data = cursor.fetchall()
    except Exception as error:
        data = None
        print("Error at request insert: ", str(error))
    finally:
        connection.commit()
        cursor.close()
    return data


def request_update(connection, request_id, status, error):
    cursor = connection.cursor()
    try:
        print('call public.request_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (request_id, status, error, 'dataset'))
        cursor.execute('call public.request_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (request_id, status, error, 'dataset'))
    except Exception as err:
        print("Error at request update: ", str(err))
    finally:
        connection.commit()
        cursor.close()


def target_objects_insert(connection, iteration_id, source_connection_id, schema, dr_connection_id,
                          target_schema, object_type, object_name, object_defination, deployed_flag):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.target_objects_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset"',
                       (int(iteration_id), int(source_connection_id), schema, int(dr_connection_id), target_schema, object_type,
                        object_name, object_defination, deployed_flag, 'dataset'))
        data = cursor.fetchall()
        print('Target object insert:',data)
    except Exception as error:
        data = None
        print("Error at target objects insert: ", str(error))
    finally:
        connection.commit()
        cursor.close()
    return data


def target_deployment_insert(connection, iteration_id, dr_connection_id, target_schema, object_type, object_name,
                             observations, fix_duration, deployed_flag, deploy_error):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.target_deployment_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset"',
                       (int(iteration_id), int(dr_connection_id), target_schema, object_type, object_name, observations,
                        fix_duration, deployed_flag, deploy_error, 'dataset'))
        data = cursor.fetchall()
        print('Target deployment insert:',data)

    except Exception as error:
        data = None
        print("Error at target deployment insert: ", str(error))
    finally:
        connection.commit()
        cursor.close()
    return data


def validation_insert(connection, iteration_id, operation_name, operation_category, source_connection_id, schema_name,
                      target_connection_id, target_schema, object_type, total_count, matched_count, unmatched_count):
    cursor = connection.cursor()
    try:
        print('call public.validation_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset"',
                       (int(iteration_id), operation_name, operation_category, int(source_connection_id), schema_name,
                        int(target_connection_id), target_schema, object_type, total_count, matched_count, unmatched_count,
                        'dataset'))
        cursor.execute('call public.validation_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset"',
                       (int(iteration_id), operation_name, operation_category, int(source_connection_id), schema_name,
                        int(target_connection_id), target_schema, object_type, total_count, matched_count, unmatched_count,
                        'dataset'))
        data = cursor.fetchall()
        print('Validation insert:',data)
    except Exception as error:
        data = None
        print("Error at validation insert: ", str(error))
    finally:
        connection.commit()
        cursor.close()
    return data


def manual_validation_insert(connection, iteration_id, source_connection_id, schema_name, target_connection_id,
                             target_schema, object_type, matched_count):
    cursor = connection.cursor()
    try:
        cursor.execute(
            'call public.manual_validation_insert(%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset"',
            (iteration_id, source_connection_id, schema_name, target_connection_id, target_schema, object_type,
             matched_count, 'dataset'))
        data = cursor.fetchall()
        print('Manual Validation insert:',data)
    except Exception as error:
        data = None
        print("Error at manual validation insert: ", str(error))
    finally:
        connection.commit()
        cursor.close()
    return data


def create_deployment_file(connection, iteration_id, target_connection_id, target_schema, object_type):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.create_deployment_file(%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (iteration_id, target_connection_id, target_schema, object_type, 'dataset'))
        data = cursor.fetchall()
        print('Create deployment file:',data)
    except Exception as err:
        data = None
        print("Error at deployment file creation : ", err)
    finally:
        connection.commit()
        cursor.close()
    return data


def get_object_defination(connection, iteration_id, target_connection_id, target_schema, object_type, object_name):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.get_object_defination(%s,%s,%s,%s,%s,%s);fetch all in "dataset";',
                       (iteration_id, target_connection_id, target_schema, object_type, object_name, 'dataset'))
        data = cursor.fetchall()
        print('Get object defination:',data)
    except Exception as err:
        data = None
        print("Error at get object defination: ", err)
    finally:
        connection.commit()
        cursor.close()
    return data

