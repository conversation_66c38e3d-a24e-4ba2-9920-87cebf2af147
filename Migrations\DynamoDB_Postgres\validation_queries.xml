<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
                <Table>
                    WITH cte AS (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    SELECT /*+ <PERSON>RA<PERSON><PERSON>(@degree) */ * FROM cte
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%' and OBJECT_NAME not like '%$%'
                    AND OBJECT_NAME NOT LIKE 'SYS%'
                    ORDER BY 1,2
                </Table>
            </Storage>
        </Source>
        <Target>
            <Storage>
                <Table>
                    SELECT /*+ PARALLEL(@degree) */ table_name FROM information_schema.tables
                    WHERE table_schema = lower('@schemaname') AND table_type = 'BASE TABLE' AND
                    table_schema||'.'||table_name
                    NOT IN (
                    SELECT inhrelid::regclass::text
                    FROM pg_inherits
                    )
                </Table>
            </Storage>
        </Target>
    </Validation_Queries>
</Queries>