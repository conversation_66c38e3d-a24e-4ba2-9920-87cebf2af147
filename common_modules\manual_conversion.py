import re, os, sys
import xml.etree.ElementTree as ET
from import_file import import_file
from common_modules.api import decrypt_database_details, api_authentication
from common_modules.common_functions import deploy_object, drop_table
from common_modules.stored_procedures import target_deployment_insert, get_object_defination, \
    manual_validation_insert, request_insert, request_update


def manual_conversion_trigger(task_name, project_id, migration_name, iteration_id, source_connection_id, schema_name,
                              target_connection_id, target_schema, object_type, object_name, observations, fix_duration,
                              cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
    if os.path.isfile(db_module_path):
        sys.path.append(db_module_path)
        import_object = import_file(db_module_path)
        function_call = getattr(import_object, 'connect_database')

        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'validation_queries.xml'
        tree = ET.parse(xml_path)
        root = tree.getroot()

        project_DB_details = {}
        request_id = ''
        try:
            token_data = api_authentication()
            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_connection = function_call(project_DB_details)

            request_id = request_insert(project_connection, None, None, task_name, task_name,
                                        target_schema, object_type)[0]

            target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)
            target_function_call = getattr(import_object, 'target_DB_connection')
            target_connection, error = target_function_call(target_DB_details)

            object_defination_data = get_object_defination(project_connection, iteration_id, None,
                                                           target_schema, object_type, object_name)

            if object_defination_data:
                if object_type == 'Table':
                    query = ''
                    if migration_name == 'Oracle_Postgres14':
                        query = f"drop table if exists {target_schema}.{object_name} cascade"
                    elif migration_name == 'MSSQL_Oracle':
                        query = f"drop table {target_schema.upper()}.{object_name.upper()} cascade constraints"
                    drop_table(target_connection, query)

                for object_tuple in object_defination_data:
                    target_defination = object_tuple[4]
                    target_defination = re.sub(rf'{schema_name}\.', target_schema + '.', target_defination,
                                               flags=re.IGNORECASE | re.DOTALL)
                    deploy_error = deploy_object(target_connection, target_defination)
                    status_msgs = ['table can have only one primary key', 'existing object', 'already NOT NULL',
                                   'already indexed', 'already exists', 'column default value expression',
                                   'Duplicate key name ', 'Multiple primary key defined']
                    if deploy_error != '' and all(
                            str(i).lower() not in str(deploy_error).lower() for i in status_msgs):
                        target_deployment_insert(project_connection, iteration_id, source_connection_id,
                                                 schema_name, object_type, object_name, observations, fix_duration,
                                                 False, deploy_error)
                    else:
                        target_deployment_insert(project_connection, iteration_id, source_connection_id,
                                                 schema_name, object_type, object_name, observations, fix_duration,
                                                 True, None)

                        if object_type in ['Procedure', 'Function', 'Package']:
                            object_type = 'Code_Objects'
                        elif object_type in ['Partition']:
                            object_type = 'Table'

                        object_list_query = list(root.iterfind('Validation_Queries/Target//' + object_type))[0].text
                        object_list_query = object_list_query.replace('@schemaname', target_schema.upper()).replace(
                            '@degree',
                            str(
                                target_DB_details[
                                    'parallelprocess']))

                        target_execute_call = getattr(import_object, 'execute_query')
                        list_query_output = target_execute_call(target_connection, object_list_query)

                        manual_validation_insert(project_connection, iteration_id, source_connection_id, schema_name,
                                                 target_connection_id,
                                                 target_schema, object_type, len(list_query_output))
            else:
                print('Updated target defination not found')

            project_connection = function_call(project_DB_details)
            request_update(project_connection, request_id, 'Completed', None)

        except Exception as error:
            print(f"Error at manual conversion for {target_schema}.{object_name} : {str(error)}")

            project_connection = function_call(project_DB_details)
            request_update(project_connection, request_id, 'Error', str(error))
    else:
        print(f'Config path {db_module_path} not found')
