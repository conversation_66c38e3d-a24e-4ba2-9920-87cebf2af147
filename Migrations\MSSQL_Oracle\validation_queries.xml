<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
                <Sequence>
                    SELECT
                    schema_name = SCHEMA_NAME(iss.schema_id),
                    sequence_name = ss.name,
                    current_value = cast(ss.current_value as int)
                    FROM
                    sys.sequences AS ss
                    JOIN sys.schemas AS iss ON
                    ss.schema_id = iss.schema_id
                    where
                    lower(iss.name) = lower('@schemaname')
                    ORDER BY
                    schema_name,
                    sequence_name;
                </Sequence>
                <Table>
                    SELECT table_schema,TABLE_NAME,table_type
                    FROM INFORMATION_SCHEMA.TABLES
                    where lower(TABLE_SCHEMA)=lower('@schemaname') and table_type='BASE TABLE'
                </Table>
                <Primary_Key>
                    select
                    schema_name(tab.schema_id) as [schema_name],
                    tab.[name] as table_name,
                    pk.[name] as pk_name,
                    substring(column_names, 1, len(column_names)-1) as [columns],
                    CASE
                    WHEN is_disabled = 1 THEN 'DISABLED'
                    ELSE 'ENABLED'
                    END AS Status

                    from sys.tables tab
                    inner join sys.indexes pk
                    on tab.object_id = pk.object_id
                    and pk.is_primary_key = 1
                    cross apply (select col.[name] + ', '
                    from sys.index_columns ic
                    inner join sys.columns col
                    on ic.object_id = col.object_id
                    and ic.column_id = col.column_id
                    where ic.object_id = tab.object_id
                    and ic.index_id = pk.index_id
                    order by col.column_id
                    for xml path ('') ) D (column_names)
                    where schema_name(tab.schema_id)=lower('@schemaname')
                    order by schema_name(tab.schema_id),
                    pk.[name];

                </Primary_Key>
                <Unique_Constraint>
                    SELECT
                    s.name AS owner,
                    t.name AS table_name,
                    kc.name AS constraint_name,
                    STUFF((SELECT ',' + c.name
                    FROM sys.index_columns ic
                    JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    WHERE ic.object_id = kc.parent_object_id AND ic.index_id = i.index_id
                    ORDER BY ic.key_ordinal
                    FOR XML PATH('')), 1, 1, '') AS col_list,
                    CASE
                    WHEN i.is_disabled = 1 THEN 'DISABLED'
                    ELSE 'ENABLED'
                    END AS status
                    FROM sys.key_constraints kc
                    JOIN sys.indexes i ON kc.parent_object_id = i.object_id AND kc.unique_index_id = i.index_id
                    JOIN sys.tables t ON kc.parent_object_id = t.object_id
                    JOIN sys.schemas s ON t.schema_id = s.schema_id
                    WHERE kc.type = 'UQ' -- Unique constraints
                    AND s.name = lower('@schemaname')
                    ORDER BY s.name;

                </Unique_Constraint>
                <Foreign_Key>
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    fk.name AS constraint_name,
                    ' FOREIGN KEY (' +
                    STUFF((
                    SELECT ', ' + c.name
                    FROM sys.foreign_key_columns AS fkc2
                    JOIN sys.columns AS c ON fkc2.parent_object_id = c.object_id AND fkc2.parent_column_id = c.column_id
                    WHERE fkc2.constraint_object_id = fk.object_id
                    ORDER BY fkc2.constraint_column_id
                    FOR XML PATH(''), TYPE
                    ).value('.', 'NVARCHAR(MAX)'), 1, 2, '') +
                    ') REFERENCES ' +
                    rs.name + '.' + rt.name + ' (' +
                    STUFF((
                    SELECT ', ' + rc.name
                    FROM sys.foreign_key_columns AS fkc3
                    JOIN sys.columns AS rc ON fkc3.referenced_object_id = rc.object_id AND fkc3.referenced_column_id =
                    rc.column_id
                    WHERE fkc3.constraint_object_id = fk.object_id
                    ORDER BY fkc3.constraint_column_id
                    FOR XML PATH(''), TYPE
                    ).value('.', 'NVARCHAR(MAX)'), 1, 2, '') + ');' AS ForeignKeyDDL,
                    'Enabled' AS status
                    FROM
                    sys.foreign_keys fk
                    JOIN
                    sys.tables t ON fk.parent_object_id = t.object_id
                    JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    JOIN
                    sys.tables rt ON fk.referenced_object_id = rt.object_id
                    JOIN
                    sys.schemas rs ON rt.schema_id = rs.schema_id
                    WHERE
                    LOWER(s.name) = LOWER('@schemaname')
                    ORDER BY
                    s.name, t.name, fk.name;
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema ,table_name ,column_name
                    FROM information_schema.columns
                    WHERE upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,column_name,column_default
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    and column_default IS NOT NULL
                </Default_Constraint>
                <Check_Constraint>
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    cc.name AS ConstraintName,
                    cc.definition AS CheckDefinition,
                    case when
                    cc.is_disabled = '0' then 'ENABLED' when
                    cc.is_disabled = '1' then 'DISABLED' end AS status
                    FROM
                    sys.check_constraints cc
                    INNER JOIN sys.tables t ON cc.parent_object_id = t.object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    where lower(s.name)=lower('@schemaname')
                    ORDER BY
                    t.name, cc.name;

                </Check_Constraint>
                <Index>
                    WITH IndexColumns AS (
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    i.name AS index_name,
                    c.name AS column_name,
                    ic.key_ordinal AS column_position,
                    i.is_unique AS uniqueness,
                    ic.is_descending_key AS descend
                    FROM
                    sys.indexes i
                    INNER JOIN
                    sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    INNER JOIN
                    sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    INNER JOIN
                    sys.tables t ON t.object_id = i.object_id
                    INNER JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    WHERE
                    s.name = lower('@schemaname')
                    AND
                    t.name NOT LIKE '%$%'
                    ),
                    ConstraintType AS (
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    i.name AS index_name,
                    CASE
                    WHEN i.is_primary_key = 1 THEN 'P'
                    ELSE 'NP'
                    END AS constraint_type
                    FROM
                    sys.indexes i
                    INNER JOIN
                    sys.tables t ON i.object_id = t.object_id
                    INNER JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    WHERE
                    s.name = lower('@schemaname')
                    )
                    SELECT
                    ic.schema_name,
                    ic.table_name,
                    ic.index_name,
                    STUFF((
                    SELECT ', ' +
                    CASE
                    WHEN ic_inner.column_name LIKE 'SYS_N%' THEN ic_inner.column_name + CASE WHEN ic_inner.descend = 1
                    THEN ' DESC' ELSE ' ASC' END
                    ELSE ic_inner.column_name + CASE WHEN ic_inner.descend = 1 THEN ' DESC' ELSE ' ASC' END
                    END
                    FROM IndexColumns ic_inner
                    WHERE ic.schema_name = ic_inner.schema_name
                    AND ic.table_name = ic_inner.table_name
                    AND ic.index_name = ic_inner.index_name
                    ORDER BY ic_inner.column_position
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS index_cols,
                    CASE
                    WHEN ic.uniqueness = 0 THEN 'NONUNIQUE'
                    ELSE 'UNIQUE'
                    END AS uniqueness,
                    ct.constraint_type,
                    LOWER('CREATE' +
                    CASE
                    WHEN ic.uniqueness = 0 THEN ' INDEX '
                    ELSE ' UNIQUE INDEX '
                    END +
                    ic.index_name + ' ON ' + ic.schema_name + '.' + ic.table_name + '(' +
                    STUFF((
                    SELECT ', ' +
                    CASE
                    WHEN ic_inner.column_name LIKE 'SYS_N%' THEN ic_inner.column_name + CASE WHEN ic_inner.descend = 1
                    THEN ' DESC' ELSE ' ASC' END
                    ELSE ic_inner.column_name + CASE WHEN ic_inner.descend = 1 THEN ' DESC' ELSE ' ASC' END
                    END
                    FROM IndexColumns ic_inner
                    WHERE ic.schema_name = ic_inner.schema_name
                    AND ic.table_name = ic_inner.table_name
                    AND ic.index_name = ic_inner.index_name
                    ORDER BY ic_inner.column_position
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') + ');') AS IDX_DEF,
                    LOWER(ic.schema_name + '.' + ic.table_name + '-' + ic.index_name) AS oraConcat
                    FROM
                    IndexColumns ic
                    LEFT JOIN
                    ConstraintType ct ON ic.schema_name = ct.schema_name AND ic.table_name = ct.table_name AND
                    ic.index_name = ct.index_name
                    GROUP BY
                    ic.schema_name,
                    ic.table_name,
                    ic.index_name,
                    ic.uniqueness,
                    ct.constraint_type
                    ORDER BY
                    ic.schema_name, ic.table_name, ic.index_name;


                </Index>
                <!--                <Synonym>-->
                <!--                    SELECT /*+ PARALLEL(@degree) */ viewname-->
                <!--                    FROM pg_views-->
                <!--                    WHERE schemaname = lower('@schemaname')-->
                <!--                    order by viewname-->
                <!--                </Synonym>-->
                <View>
                    SELECT
                    TABLE_SCHEMA AS owner,
                    TABLE_NAME AS view_name
                    FROM
                    INFORMATION_SCHEMA.VIEWS
                    WHERE
                    TABLE_SCHEMA = UPPER('@schemaname')
                    AND TABLE_NAME NOT LIKE '%$%'
                    ORDER BY
                    TABLE_NAME;
                </View>
                <Datatype>
                    SELECT
                    TABLE_SCHEMA,
                    TABLE_NAME,
                    COLUMN_NAME,
                    DATA_TYPE,
                    CASE
                    WHEN DATA_TYPE IN ('int', 'decimal', 'bigint', 'smallint', 'tinyint', 'numeric') THEN
                    CONCAT(CAST(NUMERIC_PRECISION AS VARCHAR), ',', CAST(NUMERIC_SCALE AS VARCHAR))
                    WHEN DATA_TYPE IN ('nvarchar','varchar')AND CHARACTER_MAXIMUM_LENGTH =-1THEN'4000'
                    WHEN DATA_TYPE IN ('xml') AND CHARACTER_MAXIMUM_LENGTH =-1 THEN '2000'
                    WHEN DATA_TYPE IN ('nvarchar', 'varchar', 'char','nchar') THEN
                    CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR)
                    ELSE NULL
                    END AS column_size,
                    ORDINAL_POSITION,
                    'Table' AS table_type
                    FROM
                    information_schema.columns
                    WHERE
                    LOWER(TABLE_SCHEMA) = LOWER('@schemaname');
                </Datatype>
            </Storage>
            <Code>
                <!--                <Code_Objects>-->
                <!--                    SELECT-->
                <!--                    CASE-->
                <!--                    WHEN object_type = 'PROCEDURE' THEN 'PROCEDURE'-->
                <!--                    WHEN object_type = 'FUNCTION' THEN 'FUNCTION'-->
                <!--                    WHEN object_type = 'PACKAGE' THEN 'PACKAGE'-->
                <!--                    WHEN object_type = 'TYPE' THEN 'TYPE'-->
                <!--                    END AS object_type,-->
                <!--                    CASE-->
                <!--                    WHEN object_type IN ('PACKAGE', 'TYPE') THEN object_name + '_' + method_name-->
                <!--                    ELSE object_name-->
                <!--                    END AS Code_Object_Name-->
                <!--                    FROM-->
                <!--                    (-->
                <!--                    SELECT-->
                <!--                    CASE-->
                <!--                    WHEN ROUTINE_TYPE = 'PROCEDURE' THEN 'PROCEDURE'-->
                <!--                    WHEN ROUTINE_TYPE = 'FUNCTION' THEN 'FUNCTION'-->
                <!--                    WHEN ROUTINE_TYPE = 'FUNCTION' THEN 'PACKAGE'-->
                <!--                    ELSE 'TYPE'-->
                <!--                    END AS object_type,-->
                <!--                    SPECIFIC_NAME AS object_name,-->
                <!--                    ROUTINE_NAME AS method_name-->
                <!--                    FROM-->
                <!--                    INFORMATION_SCHEMA.ROUTINES-->
                <!--                    WHERE-->
                <!--                    SPECIFIC_SCHEMA = 'dbo' AND-->
                <!--                    ROUTINE_TYPE IN ('PROCEDURE', 'FUNCTION')-->
                <!--                    ) a-->
                <!--                    WHERE-->
                <!--                    method_name IS NOT NULL-->
                <!--                    ORDER BY-->
                <!--                    object_type,-->
                <!--                    Code_Object_Name;-->
                <!--                </Code_Objects>-->
                <!--                <Trigger>-->
                <!--                    SELECT SCHEMA_NAME(t.schema_id) AS table_owner,t.name AS table_name,tr.name AS trigger_name-->
                <!--                    FROM sys.tables t JOIN sys.triggers tr ON t.object_id = tr.parent_id-->
                <!--                    WHERE SCHEMA_NAME(t.schema_id) = 'dbo' AND t.name NOT LIKE '%$%' ORDER BY t.name, tr.name-->
                <!--                </Trigger>-->
                <!--                <Type>-->
                <!--                    select type_name from dba_types-->
                <!--                    where owner = upper('@schemaname')-->
                <!--                    order by type_name-->
                <!--                </Type>-->
            </Code>
        </Source>
        <Target>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ DISTINCT sequence_owner,sequence_name, TO_NUMBER(LAST_NUMBER) -
                    TO_NUMBER(INCREMENT_BY) AS sequence_value
                    from all_sequences
                    where sequence_owner in upper('@schemaname')
                    and sequence_owner NOT LIKE '%$%'
                </Sequence>
                <Table>
                    WITH cte AS (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    SELECT /*+ PARALLEL(@degree) */ * FROM cte
                    WHERE owner = upper('@schemaname')
                    AND OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    AND OBJECT_NAME NOT LIKE '%$%'
                    ORDER BY 1,2
                </Table>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.constraint_type = 'P'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name
                    and ac.table_name NOT LIKE '%$%'
                    order by ac.owner
                </Primary_Key>
                <Unique_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.constraint_type = 'U'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name
                    and ac.table_name NOT LIKE '%$%'
                    order by ac.owner
                </Unique_Constraint>
                <Foreign_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner schema_name,ac.table_name,ac.constraint_name,'FOREIGN KEY
                    (' || acc.column_name || ')' || ' REFERENCES ' ||
                    (SELECT /*+ PARALLEL(@degree) */ LISTAGG(alc.owner || '.' || alc.table_name || '(' ||
                    alc.column_name || ')', ', ')
                    WITHIN GROUP (ORDER BY alc.position)
                    FROM all_cons_columns alc
                    WHERE alc.constraint_name = ac.r_constraint_name AND alc.owner = ac.owner) || ';',ac.status
                    FROM all_constraints ac
                    JOIN all_cons_columns acc ON ac.owner = acc.owner
                    AND ac.table_name = acc.table_name
                    AND ac.constraint_name = acc.constraint_name
                    WHERE ac.owner in upper('@schemaname')
                    AND ac.constraint_type = 'R'
                    and ac.table_name NOT LIKE '%$%'
                    ORDER BY ac.owner
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name
                    FROM all_tab_columns
                    WHERE nullable = 'N'
                    AND owner = upper('@schemaname')
                    AND TABLE_NAME NOT LIKE '%$%'
                    order by owner, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name, data_default
                    FROM all_tab_columns
                    WHERE data_default IS NOT NULL
                    AND TABLE_NAME NOT LIKE '%$%'
                    AND owner = upper('@schemaname')
                </Default_Constraint>
                <Check_Constraint>
                    SELECT
                    s.username AS schemaname,
                    t.table_name AS TableName,
                    cc.constraint_name AS ConstraintName,
                    cc.search_condition AS CheckDefinition,
                    CASE
                    WHEN cc.status = 'ENABLED' THEN 'ENABLED'
                    WHEN cc.status = 'DISABLED' THEN 'DISABLED'
                    END AS status
                    FROM
                    all_constraints cc
                    JOIN
                    all_tables t ON cc.table_name = t.table_name AND cc.owner = t.owner
                    JOIN
                    all_users s ON t.owner = s.username
                    WHERE
                    s.username = UPPER('@schemaname')
                    AND cc.constraint_type = 'C'
                    AND cc.CONSTRAINT_NAME NOT LIKE '%SYS%'
                </Check_Constraint>
                <Index>
                    WITH
                    IndexColumns
                    AS
                    (
                    SELECT
                    s.USERNAME AS schema_name,
                    t.table_name,
                    i.index_name,
                    ic.column_name,
                    ic.column_position,
                    DECODE(i.uniqueness, 'UNIQUE', 'UNIQUE', 'NONUNIQUE') AS uniqueness,
                    DECODE(ic.descend, 'YES', 'DESC', 'ASC') AS descend
                    FROM
                    all_indexes i
                    JOIN
                    all_ind_columns ic ON i.index_name = ic.index_name AND i.table_name = ic.table_name AND
                    i.table_owner = ic.table_owner
                    JOIN
                    all_tab_columns c ON ic.column_name = c.column_name AND i.table_name = c.table_name AND
                    i.table_owner = c.owner
                    JOIN
                    all_tables t ON i.table_name = t.table_name AND i.table_owner = t.owner
                    JOIN
                    all_users s ON t.owner = s.username
                    WHERE
                    s.username = upper('@schemaname')
                    ),
                    ConstraintType AS (
                    SELECT
                    s.USERNAME AS schema_name,
                    t.table_name,
                    i.index_name,
                    CASE
                    WHEN i.uniqueness = 'UNIQUE' THEN 'U'
                    ELSE 'NU'
                    END AS constraint_type
                    FROM
                    all_indexes i
                    JOIN
                    all_tables t ON i.table_name = t.table_name AND i.table_owner = t.owner
                    JOIN
                    all_users s ON t.owner = s.username
                    WHERE
                    s.username = upper('@schemaname')
                    )
                    SELECT
                    ic.schema_name,
                    ic.table_name,
                    ic.index_name,
                    LISTAGG(ic.column_name || ' ' || ic.descend, ', ') WITHIN GROUP (ORDER BY ic.column_position) AS
                    index_cols,
                    ic.uniqueness,
                    ct.constraint_type,
                    LOWER('CREATE ' ||
                    CASE
                    WHEN ic.uniqueness = 'NONUNIQUE' THEN 'INDEX '
                    ELSE 'UNIQUE INDEX '
                    END ||
                    ic.index_name || ' ON ' || ic.schema_name || '.' || ic.table_name || '(' ||
                    LISTAGG(ic.column_name || ' ' || ic.descend, ', ') WITHIN GROUP (ORDER BY ic.column_position) ||
                    ');') AS IDX_DEF,
                    LOWER(ic.schema_name || '.' || ic.table_name || '-' || ic.index_name) AS oraConcat
                    FROM
                    IndexColumns ic
                    LEFT JOIN
                    ConstraintType ct ON ic.schema_name = ct.schema_name AND ic.table_name = ct.table_name AND
                    ic.index_name = ct.index_name
                    GROUP BY
                    ic.schema_name,
                    ic.table_name,
                    ic.index_name,
                    ic.uniqueness,
                    ct.constraint_type
                    ORDER BY
                    ic.schema_name, ic.table_name, ic.index_name
                </Index>

                <!--                <Synonym>-->
                <!--                    select /*+ PARALLEL(@degree) */ SYNONYM_NAME,TABLE_NAME,TABLE_OWNER from dba_synonyms where-->
                <!--                    owner=upper('@schemaname')-->
                <!--                </Synonym>-->
                <View>
                    SELECT /*+ PARALLEL(@degree) */ owner,view_name
                    FROM dba_views
                    WHERE owner = upper('@schemaname') and view_name not like '%$%'
                    order by view_name

                </View>
                <Datatype>
                    WITH cte AS (SELECT * from(
                    SELECT owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    ORDER BY 1,2),
                    datatype as(SELECT OWNER, TABLE_NAME, COLUMN_NAME, DATA_TYPE,COLUMN_SIZE,COLUMN_ID FROM (
                    select OWNER, TABLE_NAME, COLUMN_NAME, DATA_TYPE,
                    case when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NULL AND DATA_SCALE IS NULL THEN
                    '('||to_char(DATA_LENGTH)||')'
                    WHEN DATA_TYPE IN ('VARCHAR2', 'NVARCHAR2', 'NCHAR' ) THEN '('||to_char(CHAR_LENGTH)||')'
                    when DATA_TYPE IN ('TIMESTAMP(8)', 'XMLTYPE','CHAR', 'CLOB', 'UROWID') THEN
                    '('||to_char(DATA_LENGTH)||')'
                    when DATA_TYPE IN ('DATE', 'LONG', 'TIMESTAMP(6) WITH TIME ZONE', 'TIMESTAMP(6)', 'ROWID', 'RAW',
                    'BLOB', 'OBJ_SNSDETAILS') THEN NULL
                    when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NOT NULL AND DATA_SCALE IS NOT NULL THEN
                    '('||to_char(DATA_PRECISION)||','||to_char(DATA_SCALE)||')'
                    -- WHEN DATA_TYPE IN ('FLOAT') THEN NVL(DATA_PRECISION,'9999999')
                    END AS "COLUMN_SIZE", CHARACTER_SET_NAME
                    ,DATA_PRECISION, DATA_SCALE, CHAR_COL_DECL_LENGTH, DATA_LENGTH, COLUMN_ID
                    from all_tab_columns
                    where owner in upper('@schemaname')
                    order by owner, table_name, COLUMN_ID
                    )
                    )
                    SELECT kk.owner,
                    kk.table_name,kk.column_name,kk.DATA_TYPE,kk.column_size,
                    kk.column_id,
                    cc.OBJECT_TYPE
                    FROM datatype kk
                    LEFT JOIN cte cc ON kk.owner = cc.owner
                    AND cc.object_name = kk.table_name
                </Datatype>
            </Storage>
            <Code>
                <!--                <Code_Objects>-->
                <!--                    Select res.* from (select routine_type, routine_name as Code_Object_Name from information_schema.routines-->
                <!--                    where routine_type in-->
                <!--                    (-->
                <!--                    'FUNCTION'-->
                <!--                    ,'PROCEDURE'-->
                <!--                    )-->
                <!--                    and lower(specific_schema) = lower('@schemaname')-->
                <!--                    except-->
                <!--                    select routine_type, routine_name as Code_Object_Name from information_schema.routines-->
                <!--                    where routine_type in-->
                <!--                    (-->
                <!--                    'FUNCTION'-->
                <!--                    ,'PROCEDURE'-->
                <!--                    )-->
                <!--                    and lower(specific_schema) = lower('@schemaname')-->
                <!--                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name-->
                <!--                </Code_Objects>-->
                <!--                <Trigger>-->
                <!--                   SELECT upper(trigger_schema) trigger_schema,-->
                <!--                   upper(event_object_table) as Table_Name,-->
                <!--                   upper(trigger_name) as Trigger_Name-->
                <!--                   FROM information_schema.triggers-->
                <!--                   WHERE trigger_schema = lower('@schemaname')-->
                <!--                   GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation-->
                <!--                   ORDER BY event_object_table, trigger_name;-->
                <!--               </Trigger>-->
                <!--                <Type>-->
                <!--                        select user_defined_type_name from information_schema.user_defined_types-->
                <!--                        where user_defined_type_schema = lower('@schemaname') order by user_defined_type_name-->
                <!--                </Type>-->
            </Code>
        </Target>
    </Validation_Queries>
</Queries>