<Queries>
    <Extraction_Queries>
		 <Code_Objects>
            <Procedure>
                <ListQuery>
                    SELECT name as object_name,'VALID' as Status from sys.sql_modules JOIN sys.objects ON sys.sql_modules.object_id =
                    sys.objects.object_id
                    WHERE sys.objects.schema_id = SCHEMA_ID('@schemaname') AND sys.objects.type_desc =
                    'SQL_STORED_PROCEDURE' ;
                </ListQuery>
                <DefinitionQuery>
                    SELECT definition FROM sys.sql_modules JOIN sys.objects ON sys.sql_modules.object_id =
                    sys.objects.object_id
                    WHERE sys.objects.schema_id = SCHEMA_ID('@schemaname') AND sys.objects.type_desc =
                    'SQL_STORED_PROCEDURE' and name = '@name' ;
                </DefinitionQuery>
            </Procedure>
            <Function>
                <ListQuery>
                    SELECT name as object_name,'VALID' as Status from sys.sql_modules JOIN sys.objects ON sys.sql_modules.object_id =
                    sys.objects.object_id
                    WHERE sys.objects.schema_id = SCHEMA_ID('@schemaname') AND sys.objects.type_desc =
                    'SQL_SCALAR_FUNCTION' ;
                </ListQuery>
                <DefinitionQuery>
                    SELECT definition FROM sys.sql_modules JOIN sys.objects ON sys.sql_modules.object_id =
                    sys.objects.object_id
                    WHERE sys.objects.schema_id = SCHEMA_ID('@schemaname') AND sys.objects.type_desc =
                    'SQL_SCALAR_FUNCTION' and name = '@name' ;
                </DefinitionQuery>
            </Function>
        </Code_Objects>
        <Storage_Objects>
            <Sequence>
                <ListQuery>
                    SELECT sequence_name , 'VALID' as status FROM INFORMATION_SCHEMA.sequences where
                    upper(sequence_schema) = upper('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    select replace(replace(res.SequenceScript,']',''),'[','')Sequenceddl from (
                    SELECT
                    'CREATE SEQUENCE ' + QUOTENAME(s.name) + '.' + QUOTENAME(seq.name) +
                    ' START WITH ' + CAST(seq.start_value AS VARCHAR(20)) +
                    ' INCREMENT BY ' + CAST(seq.increment AS VARCHAR(20)) +
                    CASE WHEN seq.minimum_value IS NOT NULL THEN ' MINVALUE ' + CAST(seq.minimum_value AS VARCHAR(20))
                    ELSE '' END +
                    CASE WHEN seq.maximum_value IS NOT NULL THEN ' MAXVALUE ' + CAST(seq.maximum_value AS VARCHAR(20))
                    ELSE '' END +
                    CASE WHEN seq.cache_size > 1 THEN ' CACHE ' + CAST(seq.cache_size AS VARCHAR(20)) ELSE ' NOCACHE'
                    END +
                    CASE WHEN is_cycling = 1 THEN ' CYCLE' ELSE ' NOCYCLE' END AS SequenceScript
                    FROM
                    sys.sequences seq
                    JOIN
                    sys.schemas s ON seq.schema_id = s.schema_id
                    where upper(s.name)=upper('@schemaname') and upper(seq.name)=upper('@name')
                    )res;

                </DefinitionQuery>
            </Sequence>
            <Table>
                <ListQuery>
                    SELECT TABLE_NAME,'VALID' as status
                    FROM INFORMATION_SCHEMA.TABLES
                    where lower(TABLE_SCHEMA)=lower('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    DECLARE @TableName NVARCHAR(128) = '@name';
                    DECLARE @SchemaName NVARCHAR(128) = '@schemaname';
                    DECLARE @ObjectID INT = OBJECT_ID(QUOTENAME(@SchemaName) + '.' + QUOTENAME(@TableName));
                    SELECT
                        REPLACE(REPLACE(res.CreateTableDDL, '[', ''), ']', '') AS CreateTableDDL
                    FROM
                    (
                        SELECT
                            'CREATE TABLE ' + QUOTENAME(s.name) + '.' + QUOTENAME(t.name) + ' (' + CHAR(13) +
                            (
                                SELECT
                                    STRING_AGG(
                                        CHAR(13) + ' ' + QUOTENAME(c.name) + ' ' +
                                        CASE
                                            WHEN t.name IN ('varchar', 'char', 'varbinary', 'binary') THEN
                                                t.name + '(' + CASE WHEN c.max_length = -1 THEN 'MAX' ELSE CAST(c.max_length AS VARCHAR(5)) END + ')'
                                            WHEN t.name IN ('nvarchar', 'nchar') THEN
                                                t.name + '(' + CASE WHEN c.max_length = -1 THEN 'MAX' ELSE CAST(c.max_length / 2 AS VARCHAR(5)) END + ')'
                                            WHEN t.name IN ('decimal', 'numeric') THEN
                                                t.name + '(' + CAST(c.precision AS VARCHAR(5)) + ',' + CAST(c.scale AS VARCHAR(5)) + ')'
                                            ELSE
                                                t.name
                                        END,
                                        ',' + CHAR(13)
                                    ) WITHIN GROUP (ORDER BY c.column_id)
                                FROM
                                    sys.columns c
                                JOIN
                                    sys.types t ON c.user_type_id = t.user_type_id
                                WHERE
                                    c.object_id = @ObjectID
                            ) + CHAR(13) + ')' AS CreateTableDDL
                        FROM
                            sys.tables t
                        JOIN
                            sys.schemas s ON t.schema_id = s.schema_id
                        WHERE
                            t.object_id = @ObjectID
                    ) res;

                </DefinitionQuery>
            </Table>
            <Primary_Key>
                <ListQuery>
                    select
                    res.TableName + '-' + res.ConstraintName,
                    'VALID' as status
                    from
                    (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    kc.name AS ConstraintName
                    FROM
                    sys.key_constraints kc
                    INNER JOIN sys.tables t ON
                    kc.parent_object_id = t.object_id
                    inner join sys.schemas S on
                    s.schema_id = t.schema_id
                    WHERE
                    kc.type = 'PK'
                    and lower(s.name)= lower('@schemaname') ) res ;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + (s.name) + '.' + (t.name) + ' ADD CONSTRAINT ' + (k.name) + ' PRIMARY KEY (' +
                    STUFF((
                    SELECT ', ' + (c.name)
                    FROM sys.columns c
                    JOIN sys.index_columns ic ON c.object_id = ic.object_id AND c.column_id = ic.column_id
                    WHERE t.object_id = ic.object_id
                    AND k.parent_object_id = ic.object_id
                    AND k.unique_index_id = ic.index_id
                    ORDER BY ic.key_ordinal
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') + ')' AS PrimaryKeyDDL
                    FROM
                    sys.schemas s
                    JOIN
                    sys.tables t ON s.schema_id = t.schema_id
                    JOIN
                    sys.key_constraints k ON t.object_id = k.parent_object_id AND k.type = 'PK'
                    WHERE
                    UPPER(s.name) = UPPER('@schemaname')
                    and t.name + '-' + upper(k.name)=upper('@name')
                    GROUP BY
                    s.name, t.name, k.name, t.object_id, k.parent_object_id,k.unique_index_id
                    ORDER BY
                    s.name, t.name, k.name;

                </DefinitionQuery>
            </Primary_Key>
            <Unique_Constraint>
                <ListQuery>
                    select res.TableName + '-' + res.ConstraintName as objectname,
                    'VALID' as status
                    from (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    kc.name AS ConstraintName

                    FROM
                    sys.key_constraints kc
                    INNER JOIN sys.tables t ON kc.parent_object_id = t.object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    WHERE
                    kc.type = 'UQ'
                    and lower(s.name)=lower('@schemaname')
                    ) res ;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + (s.name) + '.' + (t.name) + ' ADD CONSTRAINT ' + (k.name) +
                    ' UNIQUE (' +
                    STUFF((
                    SELECT ', ' + (c.name)
                    FROM sys.columns c
                    JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    WHERE ic.object_id = t.object_id
                    AND ic.index_id = k.unique_index_id
                    ORDER BY ic.key_ordinal
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') +
                    ')' AS UniqueKeyDDL
                    FROM
                    sys.schemas s
                    JOIN sys.tables t ON s.schema_id = t.schema_id
                    JOIN sys.key_constraints k ON t.object_id = k.parent_object_id AND k.type = 'UQ'
                    WHERE
                    UPPER(s.name) = UPPER('@schemaname')
                    and t.name + '-' + upper(k.name)=upper('@name')
                    GROUP BY
                    s.name, t.name, k.name, t.object_id, k.unique_index_id
                    ORDER BY
                    s.name, t.name, k.name;

                </DefinitionQuery>
            </Unique_Constraint>
            <Foreign_Key>
                <ListQuery>
                    select res.TableName + '-' + res.ConstraintName,
                    'VALID' as status
                    FROM (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    fk.name AS ConstraintName,
                    rt.name AS ReferencedTableName
                    FROM
                    sys.foreign_keys fk
                    INNER JOIN sys.tables t ON fk.parent_object_id = t.object_id
                    INNER JOIN sys.tables rt ON fk.referenced_object_id = rt.object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    where
                    lower(s.name)=lower('@schemaname')
                    ) res;

                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + (s.name) + '.' + (t.name) + ' ADD CONSTRAINT ' + (fk.name) +
                    ' FOREIGN KEY (' +
                    STUFF((
                    SELECT ', ' + (c.name)
                    FROM sys.columns c
                    JOIN sys.foreign_key_columns fkc ON fkc.parent_object_id = c.object_id AND fkc.parent_column_id =
                    c.column_id
                    WHERE fkc.constraint_object_id = fk.object_id
                    ORDER BY fkc.constraint_column_id
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') +
                    ') REFERENCES ' +
                    (rs.name) + '.' + (rt.name) + ' (' +
                    STUFF((
                    SELECT ', ' + (rc.name)
                    FROM sys.columns rc
                    JOIN sys.foreign_key_columns fkc ON fkc.referenced_object_id = rc.object_id AND
                    fkc.referenced_column_id = rc.column_id
                    WHERE fkc.constraint_object_id = fk.object_id
                    ORDER BY fkc.constraint_column_id
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') +
                    ')' AS ForeignKeyDDL
                    FROM
                    sys.foreign_keys fk
                    JOIN
                    sys.tables t ON fk.parent_object_id = t.object_id
                    JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    JOIN
                    sys.tables rt ON fk.referenced_object_id = rt.object_id
                    JOIN
                    sys.schemas rs ON rt.schema_id = rs.schema_id
                    WHERE
                    LOWER(s.name) = LOWER('@schemaname')
                    and t.name + '-' + upper(fk.name)=upper('@name')
                    GROUP BY
                    s.name, t.name, fk.name, rs.name, rt.name, fk.object_id
                    ORDER BY
                    s.name, t.name, fk.name;


                </DefinitionQuery>
            </Foreign_Key>
            <Not_Null_Constraint>
                <ListQuery>

                    select t.name + '-' + c.name ,'ENABLED'
                    FROM
                    sys.schemas s
                    JOIN
                    sys.tables t ON s.schema_id = t.schema_id
                    JOIN
                    sys.columns c ON t.object_id = c.object_id
                    WHERE
                    c.is_nullable = 0
                    and upper(s.name)=upper('@schemaname')
                    ORDER BY
                    s.name, t.name, c.name;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + s.name + '.' + t.name + ' modify ' + c.name + ' ' +
                    case when TYPE_NAME(c.user_type_id)= 'bigint' then 'number(20)'
                    when TYPE_NAME(c.user_type_id)= 'decimal' then 'number(20)'
                    when TYPE_NAME(c.user_type_id)= 'smallint' then 'number(5)'
                    when TYPE_NAME(c.user_type_id)= 'tinyint' then 'number(3)'
                    when TYPE_NAME(c.user_type_id)= 'money' then 'number(19,4)'
                    when TYPE_NAME(c.user_type_id)= 'smallmoney' then 'number(20)'
                    when TYPE_NAME(c.user_type_id)= 'float' then 'float(53)'
                    when TYPE_NAME(c.user_type_id)= 'real' then 'float(24)'
                    when TYPE_NAME(c.user_type_id)= 'smalldatetime' then 'date'
                    when TYPE_NAME(c.user_type_id)='nvarchar' then 'varchar2(' + CAST(c.max_length AS VARCHAR(5)) + ')'
                    when TYPE_NAME(c.user_type_id)= 'text' then 'clob'
                    when TYPE_NAME(c.user_type_id)= 'binary' then 'blob'
                    when TYPE_NAME(c.user_type_id)= 'image' then 'blob'
                    when TYPE_NAME(c.user_type_id)= 'xml' then 'sys.xmltype'
                    when TYPE_NAME(c.user_type_id)= 'sysname' then 'varchar(' + CAST(c.max_length AS VARCHAR(5)) + ')'
                    when TYPE_NAME(c.user_type_id)='varchar' then 'varchar2(' + CAST(c.max_length AS VARCHAR(5)) + ')'
                    WHEN TYPE_NAME(c.user_type_id) = 'char' THEN 'CHAR(' + CAST(c.max_length AS VARCHAR(5)) + ')'
                    else
                    TYPE_NAME(c.user_type_id)
                    end
                    +
                    CASE
                    WHEN c.is_nullable = 0 THEN ' NOT NULL'
                    ELSE ' NULL'
                    END AS NotNullConstraintDDL
                    FROM
                    sys.schemas s
                    JOIN
                    sys.tables t ON s.schema_id = t.schema_id
                    JOIN
                    sys.columns c ON t.object_id = c.object_id
                    WHERE
                    c.is_nullable = 0
                    and upper(s.name)=upper('@schemaname')
                    and upper(t.name + '-' + c.name)=upper('@name')
                    ORDER BY
                    s.name, t.name, c.name;

                </DefinitionQuery>
            </Not_Null_Constraint>
            <Default_Constraint>
                <ListQuery>
                    select res.TableName + '-' + res.ColumnName,'ENABLED' as status
                    from (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    dc.name AS ConstraintName,
                    col.name AS ColumnName,
                    dc.definition AS DefaultDefinition
                    FROM
                    sys.default_constraints dc
                    INNER JOIN sys.columns col ON dc.parent_column_id = col.column_id AND dc.parent_object_id =
                    col.object_id
                    INNER JOIN sys.tables t ON t.object_id = dc.parent_object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    where lower(s.name)=lower('@schemaname')
                    )res;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + OBJECT_SCHEMA_NAME(dc.parent_object_id) + '.' + OBJECT_NAME(dc.parent_object_id) +
                    ' ADD CONSTRAINT ' + dc.name +
                    ' DEFAULT ' + dc.definition +
                    ' FOR ' + c.name AS DefaultConstraintDDL
                    FROM
                        sys.default_constraints dc
                    JOIN
                        sys.columns c ON c.object_id = dc.parent_object_id AND c.column_id = dc.parent_column_id
                    WHERE
                        OBJECT_NAME(dc.parent_object_id) + '-' + c.name = '@name'
                        AND OBJECT_SCHEMA_NAME(dc.parent_object_id) = '@schemaname';

                </DefinitionQuery>
            </Default_Constraint>
            <Check_Constraint>
                <ListQuery>
                    select res.TableName + '-' + res.ConstraintName as objectname,'VALID' as status from (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    cc.name AS ConstraintName,
                    cc.definition AS CheckDefinition
                    FROM
                    sys.check_constraints cc
                    INNER JOIN sys.tables t ON cc.parent_object_id = t.object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    where
                    lower(s.name)=lower('@schemaname')
                    ) res;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + s.name + '.' + t.name + ' ADD CONSTRAINT ' + cc.name + ' CHECK ' +
                    replace(replace(cc.definition,'[',''),']','') AS CheckConstraintDDL
                    FROM
                    sys.check_constraints cc
                    JOIN
                    sys.tables t ON cc.parent_object_id = t.object_id
                    JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    where upper(s.name)=upper('@schemaname')
                    and t.name + '-' + upper(cc.name)=upper('@name')
                    ORDER BY
                    s.name, t.name, cc.name;

                </DefinitionQuery>
            </Check_Constraint>
            <Index>
                <ListQuery>
                    SELECT
                    COALESCE(tab.table_name, '') + '-' +
                    CASE WHEN COALESCE(tab.index_name, '')
                    &lt;&gt;
                    '' THEN tab.index_name ELSE '' END AS index_key,
                    'VALID' AS status
                    FROM
                    (
                    SELECT
                    schema_name,
                    table_name,
                    table_type,
                    index_cols,
                    index_name,
                    'VALID' AS status
                    FROM
                    (
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    CASE
                    WHEN t.type = 'U' THEN 'TABLE'
                    WHEN t.type = 'V' THEN 'VIEW'
                    ELSE 'OTHER'
                    END AS table_type,
                    i.name AS index_name,
                    CASE
                    WHEN i.is_unique = 1 THEN 'UNIQUE'
                    ELSE 'NONUNIQUE'
                    END AS uniqueness,
                    CASE
                    WHEN tc.constraint_type IS NOT NULL THEN tc.constraint_type
                    ELSE NULL
                    END AS constraint_type,
                    STUFF((
                    SELECT ', ' +
                    CASE
                    WHEN c.name LIKE 'SYS_N%' THEN ce.column_expression
                    ELSE c.name
                    END
                    FROM sys.indexes i2
                    JOIN sys.index_columns ic ON i2.object_id = ic.object_id AND i2.index_id = ic.index_id
                    JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    LEFT JOIN (
                    SELECT
                    t.name AS table_name,
                    i.name AS index_name,
                    cc.definition AS column_expression,
                    ic.index_column_id AS column_position
                    FROM sys.indexes i
                    JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    JOIN sys.computed_columns cc ON ic.object_id = cc.object_id AND ic.column_id = cc.column_id
                    JOIN sys.tables t ON i.object_id = t.object_id
                    ) ce ON t.name = ce.table_name AND i.name = ce.index_name AND ic.index_column_id =
                    ce.column_position
                    WHERE i2.object_id = i.object_id AND i2.index_id = i.index_id
                    ORDER BY ic.index_column_id
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS index_cols
                    FROM
                    sys.indexes i
                    JOIN sys.tables t ON i.object_id = t.object_id
                    JOIN sys.schemas s ON t.schema_id = s.schema_id
                    LEFT JOIN (
                    SELECT
                    kc.parent_object_id AS object_id,
                    kc.name AS constraint_name,
                    kc.type_desc AS constraint_type
                    FROM sys.key_constraints kc
                    ) tc ON t.object_id = tc.object_id AND i.name = tc.constraint_name
                    WHERE
                    s.name = '@schemaname'
                    AND t.name NOT LIKE '%$%'
                    ) AS sub
                    ) AS tab
                    WHERE
                    COALESCE(tab.table_name, '') + '-' + COALESCE(tab.index_name, '')
                    &lt;&gt;
                    '' and tab.index_name&lt;&gt;''
                    order by index_key asc;


                </ListQuery>
                <DefinitionQuery>
                    WITH IndexColumns AS (
                    SELECT
                    ic.object_id,
                    ic.index_id,
                    ic.index_column_id,
                    CASE WHEN c.name LIKE 'SYS_N%' THEN cc.definition ELSE c.name END AS column_name
                    FROM sys.indexes i
                    JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    LEFT JOIN sys.computed_columns cc ON ic.object_id = cc.object_id AND ic.column_id = cc.column_id
                    ),
                    IndexDetails AS (
                    SELECT DISTINCT
                    s.name AS schema_name,
                    t.name AS table_name,
                    CASE WHEN t.type = 'U' THEN 'TABLE' WHEN t.type = 'V' THEN 'VIEW' ELSE 'OTHER' END AS table_type,
                    i.name AS index_name,
                    CASE WHEN i.is_unique = 1 THEN 'UNIQUE' ELSE 'NONUNIQUE' END AS uniqueness,
                    ISNULL(tc.type_desc, '') AS constraint_type,
                    ic.index_column_id,
                    ic.column_name
                    FROM sys.indexes i
                    JOIN IndexColumns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    JOIN sys.tables t ON i.object_id = t.object_id
                    JOIN sys.schemas s ON t.schema_id = s.schema_id
                    LEFT JOIN sys.key_constraints tc ON i.object_id = tc.parent_object_id AND i.name = tc.name
                    WHERE s.name = '@schemaname'
                    and t.name + '-' + i.name = '@name'

                    AND t.name NOT LIKE '%$%'
                    ),
                    IndexDefinitions AS (
                    SELECT
                    schema_name,
                    table_name,
                    table_type,
                    index_name,
                    uniqueness,
                    constraint_type,
                    STUFF((
                    SELECT ', ' + column_name
                    FROM IndexDetails sub
                    WHERE sub.schema_name = main.schema_name
                    AND sub.table_name = main.table_name
                    AND sub.index_name = main.index_name
                    ORDER BY sub.index_column_id
                    FOR XML PATH(''), TYPE
                    ).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS index_cols
                    FROM IndexDetails main
                    GROUP BY schema_name, table_name, table_type, index_name, uniqueness, constraint_type
                    )
                    SELECT LOWER('CREATE' + CASE WHEN uniqueness = 'NONUNIQUE' THEN ' INDEX ' ELSE ' UNIQUE INDEX ' END
                    + index_name + ' ON ' + schema_name + '.' + table_name + '(' + index_cols + ')') AS IDX_DEF
                    FROM IndexDefinitions
                </DefinitionQuery>
            </Index>
            <!--            <Synonym>-->
            <!--                <ListQuery>-->
            <!--                    SELECT object_name,STATUS-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('SYNONYM')-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    with x as-->
            <!--                    (-->
            <!--                    SELECT owner, object_name, object_type-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('SYNONYM')-->
            <!--                    AND object_name like '@name'-->
            <!--                    )-->
            <!--                    SELECT-->
            <!--                    CASE-->
            <!--                    WHEN INSTR(ddlcode, ';', -1) > 0 THEN ddlcode-->
            <!--                    ELSE ddlcode || ';'-->
            <!--                    END AS ddl_with_semicolon-->
            <!--                    FROM (-->
            <!--                    SELECT DBMS_METADATA.get_ddl (object_type, object_name, owner) as ddlcode FROM X-->
            <!--                    ) T-->
            <!--                </DefinitionQuery>-->
            <!--            </Synonym>-->
                        <View>
                            <ListQuery>
                                SELECT name AS object_name,
                                CASE WHEN is_ms_shipped = 1 THEN 'INVALID'
                                ELSE 'VALID'
                                END AS status
                                FROM sys.objects
                                WHERE schema_id = SCHEMA_ID('@schemaname')
                                AND type = 'V';
                            </ListQuery>
                            <DefinitionQuery>
                                WITH x AS
                                (
                                    SELECT s.name AS schema_name,
                                           o.name AS object_name,
                                           o.type_desc AS object_type
                                    FROM sys.objects o
                                    JOIN sys.schemas s ON o.schema_id = s.schema_id
                                    WHERE s.name = '@schemaname'
                                    AND o.type = 'V'
                                    AND o.name = '@name'
                                )
                                SELECT REPLACE(REPLACE(m.definition, ']', ''), '[', '') AS ddl
                                FROM x
                                JOIN sys.sql_modules m ON x.object_name = OBJECT_NAME(m.object_id);
                            </DefinitionQuery>
                        </View>
            <!--            <Materialized_View>-->
            <!--                <ListQuery>-->
            <!--                    SELECT object_name,status FROM dba_objects WHERE owner = upper('@schemaname') AND object_type IN-->
            <!--                    ('MATERIALIZED VIEW') and object_name not like '%$%'-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    select LOWER('CREATE MATERIALIZED VIEW '||owner||'.'||MVIEW_NAME||' AS '),QUERY ,';' from dba_mviews-->
            <!--                    where-->
            <!--                    lower(owner)=lower('@schemaname') and lower(MVIEW_NAME)=lower('@name')-->
            <!--                </DefinitionQuery>-->
            <!--            </Materialized_View>-->
            <!--            <Temporary_Table>-->
            <!--                <ListQuery>-->
            <!--                    select table_name,status from dba_tables where TEMPORARY = 'Y' AND OWNER = upper('@schemaname') AND-->
            <!--                    table_name NOT-->
            <!--                    LIKE '%$%'-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    WITH CTC-->
            <!--                    AS-->
            <!--                    (-->
            <!--                    select dbms_metadata.get_ddl('TABLE','@name','@schemaname') AS TEXT from dual-->
            <!--                    )-->
            <!--                    SELECT REPLACE(REPLACE(REPLACE(TEXT,'GLOBAL TEMPORARY ',''),'ON COMMIT DELETE ROWS',';'),'ON COMMIT-->
            <!--                    PRESERVE-->
            <!--                    ROWS',';') AS DDL FROM CTC-->
            <!--                </DefinitionQuery>-->
            <!--            </Temporary_Table>-->
            <!--            <Partition>-->
            <!--                <ListQuery>-->
            <!--                    SELECT table_name,status-->
            <!--                    FROM all_tables-->
            <!--                    WHERE partitioned = 'YES'-->
            <!--                    AND upper(owner) = upper('@schemaname')-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    SELECT CONCAT(DBMS_METADATA.GET_DDL('TABLE', table_name, owner),';') AS partition_table_def-->
            <!--                    FROM all_tables-->
            <!--                    WHERE partitioned = 'YES'-->
            <!--                    AND upper(owner) = upper('@schemaname')-->
            <!--                    AND upper(table_name) = upper('@name')-->
            <!--                </DefinitionQuery>-->
            <!--            </Partition>-->
            <Datatype>
                <Query>
                    SELECT COUNT(1)
                    FROM (
                    SELECT
                    LOWER(OBJECT_SCHEMA_NAME(c.object_id)) AS OWNER,
                    LOWER(OBJECT_NAME(c.object_id)) AS TABLE_NAME,
                    LOWER(c.name) AS COLUMN_NAME,
                    c.precision AS DATA_PRECISION,
                    c.scale AS DATA_SCALE,
                    c.max_length AS DATA_LENGTH
                    FROM
                    sys.columns AS c
                    INNER JOIN
                    sys.tables AS t ON c.object_id = t.object_id
                    LEFT JOIN
                    sys.views AS v ON t.object_id = v.object_id
                    LEFT JOIN
                    sys.synonyms AS s ON t.name = s.base_object_name
                    WHERE
                    OBJECT_SCHEMA_NAME(c.object_id) = ('@schemaname')
                    -- AND t.type = 'U' -- Only user tables
                    -- AND c.system_type_id = 108 -- Data type 'numeric'
                    AND t.name NOT LIKE '%$%'
                    AND v.object_id IS NULL
                    AND s.object_id IS NULL
                    -- ORDER BY
                    -- TABLE_NAME
                    )res;
                </Query>
            </Datatype>
            <Column>
                <Query>
                    SELECT COUNT(1)
                    FROM INFORMATION_SCHEMA.COLUMNS dtc
                    INNER JOIN INFORMATION_SCHEMA.TABLES dt
                    ON dtc.TABLE_NAME = dt.TABLE_NAME
                    AND dtc.TABLE_SCHEMA = dt.TABLE_SCHEMA
                    WHERE dtc.TABLE_SCHEMA = '@schemaname';
                </Query>
            </Column>

        </Storage_Objects>
    </Extraction_Queries>
    <Database_Exclusion_Query>
        SELECT DISTINCT LOWER(column_name)
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA NOT IN (
        'db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys'
        )
        AND COLUMN_NAME IS NOT NULL

        UNION

        -- For distinct lower(parameter_name)
        SELECT DISTINCT LOWER(parameter_name)
        FROM INFORMATION_SCHEMA.PARAMETERS
        WHERE SPECIFIC_SCHEMA NOT IN (
        'db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys'
        )
        AND PARAMETER_NAME IS NOT NULL

        UNION

        -- For distinct lower(object_name)
        SELECT DISTINCT LOWER(o.name)
        FROM sys.objects o
        INNER JOIN sys.schemas s ON o.schema_id = s.schema_id
        WHERE s.name NOT IN (
        'db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys'
        )
        AND o.type NOT IN ('TR', 'LO', 'TP', 'IX', 'J', 'EC', 'R', 'Q', 'TB', 'P')
        AND o.name NOT LIKE 'SYS%'
        AND o.name NOT LIKE '%$%'
        AND o.name IS NOT NULL

        UNION

        -- For distinct lower(object_name + '.' + routine_name)
        SELECT DISTINCT LOWER(o.name + '.' + r.routine_name)
        FROM INFORMATION_SCHEMA.ROUTINES r
        INNER JOIN sys.objects o ON r.specific_name = o.name
        INNER JOIN sys.schemas s ON o.schema_id = s.schema_id
        WHERE s.name NOT IN (
        'db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys'
        )
        AND r.routine_type IN ('PROCEDURE', 'FUNCTION')
        AND r.specific_name IS NOT NULL
        AND r.routine_name != 'NEW'
    </Database_Exclusion_Query>
    <Source_Schemas>
        SELECT name
        FROM sys.schemas
        WHERE name NOT IN
        ('db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys')
    </Source_Schemas>
    <Target_Schemas>
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('information_schema', 'pg_catalog')
        AND schema_name NOT LIKE 'pg_toast%'
        AND schema_name NOT LIKE 'pg_temp%';
    </Target_Schemas>
    <!--    <Conversion>-->
    <!--        <Table>-->
    <!--            SELECT-->
    <!--            s.name AS schema_name,-->
    <!--            t.name AS type_name-->
    <!--            FROM-->
    <!--            sys.types t-->
    <!--            JOIN-->
    <!--            sys.schemas s ON t.schema_id = s.schema_id-->
    <!--            where s.name not in ('sys')-->
    <!--            ORDER BY-->
    <!--            s.name, t.name;-->
    <!--        </Table>-->
    <!--    </Conversion>-->
</Queries>