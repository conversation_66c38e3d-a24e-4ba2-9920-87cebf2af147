import os, sys, re
from datetime import datetime
from import_file import import_file
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.common_functions import deploy_file
from common_modules.stored_procedures import request_insert, request_update


def deploy_file_trigger(task_name,project_id, migration_name, target_connection_id, schema_name, target_schema, deploy_file_path,
                        cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)
            function_call = getattr(import_object, 'connect_database')

            project_DB_details = {}
            request_id = ''

            try:
                token_data = api_authentication()
                project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
                project_connection = function_call(project_DB_details)

                request_id = request_insert(project_connection, None, None, task_name, task_name,
                                   schema_name, 'All')[0]

                target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)
                db_name = target_DB_details['db_name']
                user_name = target_DB_details['name']
                password = target_DB_details['password']
                host = target_DB_details['host']
                port = target_DB_details['port']

                file_name = deploy_file_path.split('/')[-1]
                iteration_id = file_name.split('_')[0]
                file_parts_list = deploy_file_path.split('_~_')
                file_parts_list = [i for i in file_parts_list if i != '']
                object_name = file_parts_list[2].split('_Single_File_Output')[0].strip()

                if target_schema != '':
                    f = open(root_path + '/' + deploy_file_path, 'r')
                    single_output_data = f.read()
                    if migration_name == 'Oracle_Oracle':
                        single_output_data = re.sub('"' + schema_name + '"' + ".", schema_name + ".", single_output_data,
                                                    flags=re.IGNORECASE | re.DOTALL)
                        single_output_data = re.sub(rf'{schema_name}\.', target_schema + '.', single_output_data,
                                                    flags=re.IGNORECASE | re.DOTALL)
                        single_output_data = re.sub(target_schema + ".", '"' + target_schema.upper() + '"' + ".",
                                                    single_output_data,
                                                    flags=re.IGNORECASE | re.DOTALL)
                    else:
                        single_output_data = re.sub(rf'{schema_name}.', target_schema + '.', single_output_data,
                                                    flags=re.IGNORECASE | re.DOTALL)

                    modified_file_name = str(
                        iteration_id) + '_' + str(
                        db_name).capitalize() + '_~_' + target_schema.capitalize() + '_~_' + object_name + '_Single_File_Output_{}.sql'.format(
                        datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                    modified_deploy_file_path = deploy_file_path.replace(file_name, modified_file_name)
                    with open(root_path + '/' + modified_deploy_file_path, 'w') as f:
                        f.write(single_output_data)
                else:
                    modified_deploy_file_path = deploy_file_path

                file_name = modified_deploy_file_path.split('/')[-1]

                output_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + 'Deployment_Logs'
                if not os.path.exists(output_path):
                    os.makedirs(output_path)
                output_file_path = output_path + '/' + file_name.replace('.sql', '') + '_{}'.format(
                    datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss')) + '.log'
                deploy_file_path = root_path + '/' + modified_deploy_file_path

                if migration_name in ['Oracle_Postgres14', 'Postgres_Postgres']:
                    os.environ['PGPASSWORD'] = password
                    shell_command = f'psql -h {host} -p {port} -d {db_name} -U {user_name} -f {deploy_file_path}'

                elif migration_name in ['MSSQL_Oracle', 'Oracle_Oracle']:
                    shell_command = f"sqlplus '{user_name}/{password}@{host}:{port}/{db_name}' < {deploy_file_path}"

                elif migration_name in ['Mariadb_Mysql', 'DB2_Mysql']:
                    shell_command = f"mysql -u {user_name} -p{password} -h {host} -P {port} {db_name} < {deploy_file_path}"

                elif migration_name == 'Oracle_SQL':
                    shell_command = f"sqlcmd -S {host},{port} -U {user_name} -P {password} -d {db_name} -i {deploy_file_path}"
                    f = open(deploy_file_path, 'r', encoding='utf-8', errors='replace')
                    source_data = f.read()
                    source_data = source_data.replace('"', '')
                    with open(deploy_file_path, 'w') as f:
                        f.write(source_data)
                else:
                    shell_command = ''

                deploy_file_error = deploy_file(shell_command, output_file_path)
                if deploy_file_error == '':
                    print("Deployment completed successfully for file " + file_name)
                else:
                    print("Error occurred at deployment for file " + file_name + ": " + deploy_file_error)

                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Completed', None)
            except Exception as error:
                print(f"Error occurred at deploy file {deploy_file_path} : {str(error)}")

                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Error', str(error))
    else:
        print("Config file not found")
