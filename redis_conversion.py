import os, sys, rediswq, json, subprocess, requests
from common_modules.api import api_authentication
from import_file import import_file

path = os.path.dirname(os.path.realpath(__file__))

config_path = path + '/' +'config.py'
connection_url = ''
if os.path.isfile(config_path):
    sys.path.append(config_path)
    import_object = import_file(config_path)
    connection_url = getattr(import_object, 'Connection_URL')

token_data = api_authentication()


def get_appli_connect_redisurl(token_data):
    projectid = os.environ['PROJECT_ID']
    url = connection_url + 'Common/Master/SelectProjectURLs?projectId={0}&urlid=3'.format(str(projectid))
    headers = {'content-type': 'application/json',
               'Authorization': 'Bearer ' + token_data}
    response1 = requests.get(url, headers=headers)
    status_code1 = response1.status_code
    status_response1 = {'status_code': status_code1, 'text': response1.text}
    if status_response1['status_code'] == 200:
        status_response1 = {'status_code': status_code1, 'text': response1.text}
        data_text = json.loads(status_response1['text'])
        data_text = data_text['jsonResponseData']
        data_text = data_text['Table1'][0]
        if data_text['redis_ip'] and data_text['redis_pwd']:
            data_text_ip = data_text['redis_ip']
            data_text_pwd = data_text['redis_pwd']
            return data_text_ip, data_text_pwd
    else:
        raise Exception("Request Failed for getting Application DB details from API", status_response1)


def main(datas):
    module_path = path + '/'
    os.chdir(module_path)
    cmd = datas
    output = subprocess.run(cmd, stderr=sys.stderr, universal_newlines=True, shell=True, stdout=sys.stdout)
    return output


try:
    data_text_ip, data_text_pwd = get_appli_connect_redisurl(token_data)
    q = rediswq.RedisWQ(max_retries=0,name="job3", host=data_text_ip, port=6379, db=0, password=data_text_pwd)
    # q = rediswq.RedisWQ(name="job2", host='**************', port=6379, db=0, password='Quadrant@123')
    print(q)
    while not q.empty():
        q.check_expired_leases()
        item = q.lease(lease_secs=10, block=True, timeout=2)
        if item is not None:
            print("Working on " + item)
            status = main(item)
            q.complete(item)
        else:
            print("Waiting for work")
    print("Queue empty, exiting")
except Exception as err:
    print('Redis Connection Refused', err)

# LPUSH python main_conversion.py -task Conversion -project_id 1137 -version V3 -obj_category Code_Objects -schema Quad -mig_name Oracle_Postgres14
