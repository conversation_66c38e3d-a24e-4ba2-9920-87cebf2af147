<Queries>
    <Extraction_Queries>
        <Code_Objects>
            <!--            <Package>-->
            <!--                <ListQuery>-->
            <!--                    SELECT object_name,STATUS-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('PACKAGE')-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    with x as-->
            <!--                    (-->
            <!--                    SELECT owner, object_name, object_type-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('PACKAGE')-->
            <!--                    AND object_name like '@name'-->
            <!--                    )-->
            <!--                    SELECT DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner) as ddlcode FROM x-->
            <!--                </DefinitionQuery>-->
            <!--            </Package>-->
            <!--            <Procedure>-->
            <!--                <ListQuery>-->
            <!--                    SELECT object_name,STATUS-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('PROCEDURE')-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    with x as-->
            <!--                    (-->
            <!--                    SELECT owner, object_name, object_type-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('PROCEDURE')-->
            <!--                    AND upper(object_name) = upper('@name')-->
            <!--                    )-->
            <!--                    SELECT DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner) as ddlcode FROM x-->
            <!--                </DefinitionQuery>-->
            <!--            </Procedure>-->
            <!--            <Function>-->
            <!--                <ListQuery>-->
            <!--                    SELECT object_name,STATUS-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('FUNCTION')-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    with x as-->
            <!--                    (-->
            <!--                    SELECT owner, object_name, object_type-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('FUNCTION')-->
            <!--                    AND upper(object_name) = upper('@name')-->
            <!--                    )-->
            <!--                    SELECT DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner) as ddlcode FROM x-->
            <!--                </DefinitionQuery>-->
            <!--            </Function>-->
            <!--            <Trigger>-->
            <!--                <ListQuery>-->
            <!--                    SELECT object_name,STATUS FROM dba_objects WHERE owner = upper('@schemaname') AND object_type IN-->
            <!--                    ('TRIGGER')-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    with x as-->
            <!--                    (-->
            <!--                    SELECT owner, object_name, object_type-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('TRIGGER')-->
            <!--                    AND object_name = '@name'-->
            <!--                    )-->
            <!--                    SELECT DBMS_METADATA.get_ddl (x.object_type, x.object_name, x.owner) as ddlcode FROM x-->
            <!--                </DefinitionQuery>-->
            <!--            </Trigger>-->
            <!--            <Job>-->
            <!--                <Query>-->
            <!--                    select count(1) from dba_objects where object_type = 'JOB' AND upper(owner) = upper('@schemaname')-->
            <!--                </Query>-->
            <!--            </Job>-->
            <!--            <Schedule>-->
            <!--                <Query>-->
            <!--                    select count(1) from dba_objects where object_type = 'SCHEDULE' AND upper(owner) =-->
            <!--                    upper('@schemaname')-->
            <!--                </Query>-->
            <!--            </Schedule>-->
            <!--            <Program>-->
            <!--                <Query>-->
            <!--                    select count(1) from dba_objects where object_type = 'PROGRAM' AND upper(owner) =-->
            <!--                    upper('@schemaname')-->
            <!--                </Query>-->
            <!--            </Program>-->
        </Code_Objects>
        <Storage_Objects>
            <!--            <Type>-->
            <!--                <ListQuery>-->
            <!--                    SELECT object_name,STATUS-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('TYPE')-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    SELECT-->
            <!--                    CASE-->
            <!--                    WHEN INSTR(ddl_stmt, ';', -1) > 0 THEN ddl_stmt-->
            <!--                    ELSE ddl_stmt || ';'-->
            <!--                    END AS ddl_with_semicolon-->
            <!--                    FROM (-->
            <!--                    SELECT DBMS_METADATA.GET_DDL('TYPE', '@name', '@schemaname') AS ddl_stmt-->
            <!--                    FROM dual-->
            <!--                    ) t-->
            <!--                </DefinitionQuery>-->
            <!--            </Type>-->
            <Sequence>
                <ListQuery>
                    SELECT sequence_name , 'VALID' as status FROM INFORMATION_SCHEMA.sequences where
                    upper(sequence_schema) = upper('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    select replace(replace(res.SequenceScript,']',''),'[','')Sequenceddl from (
                    SELECT
                    'CREATE SEQUENCE ' + QUOTENAME(s.name) + '.' + QUOTENAME(seq.name) +
                    ' START WITH ' + CAST(seq.start_value AS VARCHAR(20)) +
                    ' INCREMENT BY ' + CAST(seq.increment AS VARCHAR(20)) +
                    CASE WHEN seq.minimum_value IS NOT NULL THEN ' MINVALUE ' + CAST(seq.minimum_value AS VARCHAR(20))
                    ELSE '' END +
                    CASE WHEN seq.maximum_value IS NOT NULL THEN ' MAXVALUE ' + CAST(seq.maximum_value AS VARCHAR(20))
                    ELSE '' END +
                    CASE WHEN seq.cache_size > 1 THEN ' CACHE ' + CAST(seq.cache_size AS VARCHAR(20)) ELSE ' NOCACHE'
                    END +
                    CASE WHEN is_cycling = 1 THEN ' CYCLE' ELSE ' NOCYCLE' END AS SequenceScript
                    FROM
                    sys.sequences seq
                    JOIN
                    sys.schemas s ON seq.schema_id = s.schema_id
                    where upper(s.name)=upper('@schemaname') and upper(seq.name)=upper('@name')
                    )res;

                </DefinitionQuery>
            </Sequence>
            <Table>
                <ListQuery>
                    SELECT TABLE_NAME,'VALID' as status
                    FROM INFORMATION_SCHEMA.TABLES
                    where lower(TABLE_SCHEMA)=lower('@schemaname') and TABLE_TYPE ='BASE TABLE'
                </ListQuery>
                <DefinitionQuery>
                    DECLARE @TableName NVARCHAR(128) = '@name';
                    DECLARE @SchemaName NVARCHAR(128) = '@schemaname';
                    DECLARE @ObjectID INT = OBJECT_ID(QUOTENAME(@SchemaName) + '.' + QUOTENAME(@TableName));
                    select
                    replace(replace(res.CreateTableDDL, '[', '"'), ']', '"')
                    from
                    (
                    SELECT
                    'CREATE TABLE ' + replace(replace(QUOTENAME(s.name),'[',''),']','') + '.' +
                    replace(replace(QUOTENAME(t.name),'[',''),']','') + ' (' + CHAR(13) + STUFF((
                    SELECT
                    CHAR(13) + ' ' + QUOTENAME(c.name) + ' ' + t.name + CASE
                    WHEN t.name IN ('varchar', 'char', 'varbinary', 'binary') THEN '(' + CASE
                    WHEN c.max_length = -1 THEN 'MAX'
                    ELSE CAST(c.max_length AS VARCHAR(5))
                    END + ')'
                    WHEN t.name IN ('nvarchar', 'nchar') THEN '(' + CASE
                    WHEN c.max_length = -1 THEN 'MAX'
                    ELSE CAST(c.max_length / 2 AS VARCHAR(5))
                    END + ')'
                    WHEN t.name IN ('decimal', 'numeric') THEN '(' + CAST(c.precision AS VARCHAR(5)) + ',' +
                    CAST(c.scale AS VARCHAR(5)) + ')'
                    ELSE ''
                    END + ',' + CHAR(13)
                    FROM
                    sys.columns c
                    JOIN sys.types t ON
                    c.user_type_id = t.user_type_id
                    WHERE
                    c.object_id = @ObjectID FOR XML PATH(''),
                    TYPE).value('.',
                    'NVARCHAR(MAX)'),
                    1,
                    2,
                    '') + ')' AS CreateTableDDL
                    FROM
                    sys.tables t
                    JOIN sys.schemas s ON
                    t.schema_id = s.schema_id
                    WHERE
                    t.object_id = @ObjectID)res;
                </DefinitionQuery>
            </Table>
            <Primary_Key>
                <ListQuery>
                    select
                    res.TableName + '-' + res.ConstraintName,
                    'VALID' as status
                    from
                    (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    kc.name AS ConstraintName
                    FROM
                    sys.key_constraints kc
                    INNER JOIN sys.tables t ON
                    kc.parent_object_id = t.object_id
                    inner join sys.schemas S on
                    s.schema_id = t.schema_id
                    WHERE
                    kc.type = 'PK'
                    and lower(s.name)= lower('@schemaname') ) res ;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + (s.name) + '.' + (t.name) + ' ADD CONSTRAINT ' + (k.name) + ' PRIMARY KEY (' +
                    STUFF((
                    SELECT ', ' + (c.name)
                    FROM sys.columns c
                    JOIN sys.index_columns ic ON c.object_id = ic.object_id AND c.column_id = ic.column_id
                    WHERE t.object_id = ic.object_id
                    AND k.parent_object_id = ic.object_id
                    AND k.unique_index_id = ic.index_id
                    ORDER BY ic.key_ordinal
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') + ')' AS PrimaryKeyDDL
                    FROM
                    sys.schemas s
                    JOIN
                    sys.tables t ON s.schema_id = t.schema_id
                    JOIN
                    sys.key_constraints k ON t.object_id = k.parent_object_id AND k.type = 'PK'
                    WHERE
                    UPPER(s.name) = UPPER('@schemaname')
                    and t.name + '-' + upper(k.name)=upper('@name')
                    GROUP BY
                    s.name, t.name, k.name, t.object_id, k.parent_object_id,k.unique_index_id
                    ORDER BY
                    s.name, t.name, k.name;

                </DefinitionQuery>
            </Primary_Key>
            <Unique_Constraint>
                <ListQuery>
                    select res.TableName + '-' + res.ConstraintName as objectname,
                    'VALID' as status
                    from (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    kc.name AS ConstraintName

                    FROM
                    sys.key_constraints kc
                    INNER JOIN sys.tables t ON kc.parent_object_id = t.object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    WHERE
                    kc.type = 'UQ'
                    and lower(s.name)=lower('@schemaname')
                    ) res ;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + (s.name) + '.' + (t.name) + ' ADD CONSTRAINT ' + (k.name) +
                    ' UNIQUE (' +
                    STUFF((
                    SELECT ', ' + (c.name)
                    FROM sys.columns c
                    JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    WHERE ic.object_id = t.object_id
                    AND ic.index_id = k.unique_index_id
                    ORDER BY ic.key_ordinal
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') +
                    ')' AS UniqueKeyDDL
                    FROM
                    sys.schemas s
                    JOIN sys.tables t ON s.schema_id = t.schema_id
                    JOIN sys.key_constraints k ON t.object_id = k.parent_object_id AND k.type = 'UQ'
                    WHERE
                    UPPER(s.name) = UPPER('@schemaname')
                    and t.name + '-' + upper(k.name)=upper('@name')
                    GROUP BY
                    s.name, t.name, k.name, t.object_id, k.unique_index_id
                    ORDER BY
                    s.name, t.name, k.name;

                </DefinitionQuery>
            </Unique_Constraint>
            <Foreign_Key>
                <ListQuery>
                    select res.TableName + '-' + res.ConstraintName,
                    'VALID' as status
                    FROM (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    fk.name AS ConstraintName,
                    rt.name AS ReferencedTableName
                    FROM
                    sys.foreign_keys fk
                    INNER JOIN sys.tables t ON fk.parent_object_id = t.object_id
                    INNER JOIN sys.tables rt ON fk.referenced_object_id = rt.object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    where
                    lower(s.name)=lower('@schemaname')
                    ) res;

                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + (s.name) + '.' + (t.name) + ' ADD CONSTRAINT ' + (fk.name) +
                    ' FOREIGN KEY (' +
                    STUFF((
                    SELECT ', ' + (c.name)
                    FROM sys.columns c
                    JOIN sys.foreign_key_columns fkc ON fkc.parent_object_id = c.object_id AND fkc.parent_column_id =
                    c.column_id
                    WHERE fkc.constraint_object_id = fk.object_id
                    ORDER BY fkc.constraint_column_id
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') +
                    ') REFERENCES ' +
                    (rs.name) + '.' + (rt.name) + ' (' +
                    STUFF((
                    SELECT ', ' + (rc.name)
                    FROM sys.columns rc
                    JOIN sys.foreign_key_columns fkc ON fkc.referenced_object_id = rc.object_id AND
                    fkc.referenced_column_id = rc.column_id
                    WHERE fkc.constraint_object_id = fk.object_id
                    ORDER BY fkc.constraint_column_id
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') +
                    ')' AS ForeignKeyDDL
                    FROM
                    sys.foreign_keys fk
                    JOIN
                    sys.tables t ON fk.parent_object_id = t.object_id
                    JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    JOIN
                    sys.tables rt ON fk.referenced_object_id = rt.object_id
                    JOIN
                    sys.schemas rs ON rt.schema_id = rs.schema_id
                    WHERE
                    LOWER(s.name) = LOWER('@schemaname')
                    and t.name + '-' + upper(fk.name)=upper('@name')
                    GROUP BY
                    s.name, t.name, fk.name, rs.name, rt.name, fk.object_id
                    ORDER BY
                    s.name, t.name, fk.name;


                </DefinitionQuery>
            </Foreign_Key>
            <Not_Null_Constraint>
                <ListQuery>
                    select t.name + '-' + c.name ,'ENABLED'
                    FROM
                    sys.schemas s
                    JOIN
                    sys.tables t ON s.schema_id = t.schema_id
                    JOIN
                    sys.columns c ON t.object_id = c.object_id
                    WHERE
                    c.is_nullable = 0
                    and upper(s.name)=upper('@schemaname')
                    ORDER BY
                    s.name, t.name, c.name;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + s.name + '.' + t.name + ' MODIFY (' + c.name + ' ' +
                    CASE
                    WHEN TYPE_NAME(c.user_type_id) = 'bigint' THEN 'NUMBER(20)'
                    WHEN TYPE_NAME(c.user_type_id) = 'decimal' THEN 'NUMBER(20)'
                    WHEN TYPE_NAME(c.user_type_id) = 'smallint' THEN 'NUMBER(5)'
                    WHEN TYPE_NAME(c.user_type_id) = 'tinyint' THEN 'NUMBER(3)'
                    WHEN TYPE_NAME(c.user_type_id) = 'money' THEN 'NUMBER(19,4)'
                    WHEN TYPE_NAME(c.user_type_id) = 'smallmoney' THEN 'NUMBER(20)'
                    WHEN TYPE_NAME(c.user_type_id) = 'float' THEN 'FLOAT(53)'
                    WHEN TYPE_NAME(c.user_type_id) = 'real' THEN 'FLOAT(24)'
                    WHEN TYPE_NAME(c.user_type_id) = 'smalldatetime' THEN 'DATE'
                    WHEN TYPE_NAME(c.user_type_id) = 'text' THEN 'CLOB'
                    WHEN TYPE_NAME(c.user_type_id) = 'binary' THEN 'BLOB'
                    WHEN TYPE_NAME(c.user_type_id) = 'image' THEN 'BLOB'
                    WHEN TYPE_NAME(c.user_type_id) = 'xml' THEN 'SYS.XMLTYPE'
                    WHEN TYPE_NAME(c.user_type_id) = 'sysname' THEN 'VARCHAR2(' + CAST(c.max_length AS VARCHAR(5)) + ')'
                    WHEN TYPE_NAME(c.user_type_id) = 'nvarchar' THEN 'VARCHAR2(' + CAST(c.max_length / 2 AS VARCHAR(5))
                    + ')'
                    WHEN TYPE_NAME(c.user_type_id) = 'varchar' THEN 'VARCHAR2(' + CAST(c.max_length AS VARCHAR(5)) + ')'
                    WHEN TYPE_NAME(c.user_type_id) = 'char' THEN 'CHAR(' + CAST(c.max_length AS VARCHAR(5)) + ')'
                    WHEN TYPE_NAME(c.user_type_id) = 'numeric' THEN 'NUMERIC(' + CAST(c.precision AS VARCHAR(5)) + ',' +
                    CAST(c.scale AS VARCHAR(5)) + ')'
                    ELSE TYPE_NAME(c.user_type_id)
                    END +
                    CASE
                    WHEN c.is_nullable = 0 THEN ' NOT NULL'
                    ELSE ' NULL'
                    END +
                    ')' AS OracleAlterTableDDL
                    FROM
                    sys.schemas s
                    JOIN
                    sys.tables t ON s.schema_id = t.schema_id
                    JOIN
                    sys.columns c ON t.object_id = c.object_id
                    WHERE
                    c.is_nullable = 0
                    AND UPPER(s.name) = UPPER('@schemaname')
                    AND UPPER(t.name + '-' + c.name) = UPPER('@name')
                    ORDER BY
                    s.name, t.name, c.name;


                </DefinitionQuery>
            </Not_Null_Constraint>
            <Default_Constraint>
                <ListQuery>
                    select res.TableName + '-' + res.ColumnName,'ENABLED' as status
                    from (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    dc.name AS ConstraintName,
                    col.name AS ColumnName,
                    dc.definition AS DefaultDefinition
                    FROM
                    sys.default_constraints dc
                    INNER JOIN sys.columns col ON dc.parent_column_id = col.column_id AND dc.parent_object_id =
                    col.object_id
                    INNER JOIN sys.tables t ON t.object_id = dc.parent_object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    where lower(s.name)=lower('@schemaname')
                    )res;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + s.name + '.' + t.name + ' modify ' + c.name + ' DEFAULT ' + dc.definition AS
                    DefaultConstraintDDL
                    FROM
                    sys.default_constraints dc
                    JOIN
                    sys.tables t ON dc.parent_object_id = t.object_id
                    JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    JOIN
                    sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
                    where lower(s.name)=lower('@schemaname')
                    and t.name + '-' + upper(c.name)=upper('@name')

                    ORDER BY
                    s.name, t.name, dc.name;

                </DefinitionQuery>
            </Default_Constraint>
            <Check_Constraint>
                <ListQuery>
                    select res.TableName + '-' + res.ConstraintName as objectname,'VALID' as status from (
                    SELECT
                    s.name as schemaname,
                    t.name AS TableName,
                    cc.name AS ConstraintName,
                    cc.definition AS CheckDefinition
                    FROM
                    sys.check_constraints cc
                    INNER JOIN sys.tables t ON cc.parent_object_id = t.object_id
                    inner join sys.schemas S on s.schema_id =t.schema_id
                    where
                    lower(s.name)=lower('@schemaname')
                    ) res;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' + s.name + '.' + t.name + ' ADD CONSTRAINT ' + cc.name + ' CHECK ' +
                    replace(replace(cc.definition,'[',''),']','') AS CheckConstraintDDL
                    FROM
                    sys.check_constraints cc
                    JOIN
                    sys.tables t ON cc.parent_object_id = t.object_id
                    JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    where upper(s.name)=upper('@schemaname')
                    and t.name + '-' + upper(cc.name)=upper('@name')
                    ORDER BY
                    s.name, t.name, cc.name;

                </DefinitionQuery>
            </Check_Constraint>
            <Index>
                <ListQuery>
                    SELECT
                    COALESCE(tab.table_name, '') + '-' +
                    CASE WHEN COALESCE(tab.index_name, '') &lt;&gt; '' THEN tab.index_name ELSE '' END AS index_key,
                    'VALID' AS status
                    FROM
                    (
                    SELECT
                    schema_name,
                    table_name,
                    table_type,
                    index_cols,
                    index_name,
                    'VALID' AS status
                    FROM
                    (
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    CASE
                    WHEN t.type = 'U' THEN 'TABLE'
                    WHEN t.type = 'V' THEN 'VIEW'
                    ELSE 'OTHER'
                    END AS table_type,
                    i.name AS index_name,
                    CASE
                    WHEN i.is_unique = 1 THEN 'UNIQUE'
                    ELSE 'NONUNIQUE'
                    END AS uniqueness,
                    STUFF((
                    SELECT ', ' +
                    CASE
                    WHEN c.name LIKE 'SYS_N%' THEN ce.column_expression
                    ELSE c.name
                    END
                    FROM sys.indexes i2
                    JOIN sys.index_columns ic ON i2.object_id = ic.object_id AND i2.index_id = ic.index_id
                    JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    LEFT JOIN (
                    SELECT
                    t.name AS table_name,
                    i.name AS index_name,
                    cc.definition AS column_expression,
                    ic.index_column_id AS column_position
                    FROM sys.indexes i
                    JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    JOIN sys.computed_columns cc ON ic.object_id = cc.object_id AND ic.column_id = cc.column_id
                    JOIN sys.tables t ON i.object_id = t.object_id
                    ) ce ON t.name = ce.table_name AND i.name = ce.index_name AND ic.index_column_id =
                    ce.column_position
                    WHERE i2.object_id = i.object_id AND i2.index_id = i.index_id
                    ORDER BY ic.index_column_id
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS index_cols
                    FROM
                    sys.indexes i
                    JOIN sys.tables t ON i.object_id = t.object_id
                    JOIN sys.schemas s ON t.schema_id = s.schema_id
                    LEFT JOIN sys.key_constraints kc ON t.object_id = kc.parent_object_id
                    AND i.index_id = kc.unique_index_id
                    WHERE
                    s.name = '@schemaname'
                    AND t.name NOT LIKE '%$%'
                    AND kc.unique_index_id IS NULL
                    ) AS sub
                    ) AS tab
                    WHERE
                    COALESCE(tab.table_name, '') + '-' + COALESCE(tab.index_name, '') &lt;&gt; ''
                    AND tab.index_name &lt;&gt; ''
                    ORDER BY index_key ASC;
                </ListQuery>
                <DefinitionQuery>
                    WITH IndexColumns AS (
                    SELECT
                    ic.object_id,
                    ic.index_id,
                    ic.index_column_id,
                    CASE WHEN c.name LIKE 'SYS_N%' THEN cc.definition ELSE c.name END AS column_name
                    FROM sys.indexes i
                    JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    LEFT JOIN sys.computed_columns cc ON ic.object_id = cc.object_id AND ic.column_id = cc.column_id
                    ),
                    IndexDetails AS (
                    SELECT DISTINCT
                    s.name AS schema_name,
                    t.name AS table_name,
                    CASE WHEN t.type = 'U' THEN 'TABLE' WHEN t.type = 'V' THEN 'VIEW' ELSE 'OTHER' END AS table_type,
                    i.name AS index_name,
                    CASE WHEN i.is_unique = 1 THEN 'UNIQUE' ELSE 'NONUNIQUE' END AS uniqueness,
                    ISNULL(tc.type_desc, '') AS constraint_type,
                    ic.index_column_id,
                    ic.column_name
                    FROM sys.indexes i
                    JOIN IndexColumns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    JOIN sys.tables t ON i.object_id = t.object_id
                    JOIN sys.schemas s ON t.schema_id = s.schema_id
                    LEFT JOIN sys.key_constraints tc ON i.object_id = tc.parent_object_id AND i.name = tc.name
                    WHERE s.name = '@schemaname'
                    and t.name + '-' + i.name = '@name'

                    AND t.name NOT LIKE '%$%'
                    ),
                    IndexDefinitions AS (
                    SELECT
                    schema_name,
                    table_name,
                    table_type,
                    index_name,
                    uniqueness,
                    constraint_type,
                    STUFF((
                    SELECT ', ' + column_name
                    FROM IndexDetails sub
                    WHERE sub.schema_name = main.schema_name
                    AND sub.table_name = main.table_name
                    AND sub.index_name = main.index_name
                    ORDER BY sub.index_column_id
                    FOR XML PATH(''), TYPE
                    ).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS index_cols
                    FROM IndexDetails main
                    GROUP BY schema_name, table_name, table_type, index_name, uniqueness, constraint_type
                    )
                    SELECT LOWER('CREATE' + CASE WHEN uniqueness = 'NONUNIQUE' THEN ' INDEX ' ELSE ' UNIQUE INDEX ' END
                    + index_name + ' ON ' + schema_name + '.' + table_name + '(' + index_cols + ')') AS IDX_DEF
                    FROM IndexDefinitions
                </DefinitionQuery>
            </Index>
            <!--            <Synonym>-->
            <!--                <ListQuery>-->
            <!--                    SELECT object_name,STATUS-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('SYNONYM')-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    with x as-->
            <!--                    (-->
            <!--                    SELECT owner, object_name, object_type-->
            <!--                    FROM dba_objects-->
            <!--                    WHERE owner = upper('@schemaname')-->
            <!--                    AND object_type IN ('SYNONYM')-->
            <!--                    AND object_name like '@name'-->
            <!--                    )-->
            <!--                    SELECT-->
            <!--                    CASE-->
            <!--                    WHEN INSTR(ddlcode, ';', -1) > 0 THEN ddlcode-->
            <!--                    ELSE ddlcode || ';'-->
            <!--                    END AS ddl_with_semicolon-->
            <!--                    FROM (-->
            <!--                    SELECT DBMS_METADATA.get_ddl (object_type, object_name, owner) as ddlcode FROM X-->
            <!--                    ) T-->
            <!--                </DefinitionQuery>-->
            <!--            </Synonym>-->
            <View>
                <ListQuery>


                    SELECT

                    TABLE_NAME,'VALID' as status
                    FROM
                    INFORMATION_SCHEMA.VIEWS
                    where lower(TABLE_SCHEMA)=lower('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    DECLARE @SchemaName SYSNAME = '@schemaname';
                    DECLARE @ViewName SYSNAME = '@name';
                    SELECT replace(sm.definition,v.name,s.name + '.' + v.name) AS DDLCode
                    FROM
                    sys.views v
                    JOIN
                    sys.schemas s ON v.schema_id = s.schema_id
                    JOIN
                    sys.sql_modules sm ON v.object_id = sm.object_id
                    WHERE
                    s.name = @SchemaName
                    AND v.name = @ViewName
                </DefinitionQuery>
            </View>
            <!--            <Materialized_View>-->
            <!--                <ListQuery>-->
            <!--                    SELECT object_name,status FROM dba_objects WHERE owner = upper('@schemaname') AND object_type IN-->
            <!--                    ('MATERIALIZED VIEW') and object_name not like '%$%'-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    select LOWER('CREATE MATERIALIZED VIEW '||owner||'.'||MVIEW_NAME||' AS '),QUERY ,';' from dba_mviews-->
            <!--                    where-->
            <!--                    lower(owner)=lower('@schemaname') and lower(MVIEW_NAME)=lower('@name')-->
            <!--                </DefinitionQuery>-->
            <!--            </Materialized_View>-->
            <!--            <Temporary_Table>-->
            <!--                <ListQuery>-->
            <!--                    select table_name,status from dba_tables where TEMPORARY = 'Y' AND OWNER = upper('@schemaname') AND-->
            <!--                    table_name NOT-->
            <!--                    LIKE '%$%'-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    WITH CTC-->
            <!--                    AS-->
            <!--                    (-->
            <!--                    select dbms_metadata.get_ddl('TABLE','@name','@schemaname') AS TEXT from dual-->
            <!--                    )-->
            <!--                    SELECT REPLACE(REPLACE(REPLACE(TEXT,'GLOBAL TEMPORARY ',''),'ON COMMIT DELETE ROWS',';'),'ON COMMIT-->
            <!--                    PRESERVE-->
            <!--                    ROWS',';') AS DDL FROM CTC-->
            <!--                </DefinitionQuery>-->
            <!--            </Temporary_Table>-->
            <!--            <Partition>-->
            <!--                <ListQuery>-->
            <!--                    SELECT table_name,status-->
            <!--                    FROM all_tables-->
            <!--                    WHERE partitioned = 'YES'-->
            <!--                    AND upper(owner) = upper('@schemaname')-->
            <!--                </ListQuery>-->
            <!--                <DefinitionQuery>-->
            <!--                    SELECT CONCAT(DBMS_METADATA.GET_DDL('TABLE', table_name, owner),';') AS partition_table_def-->
            <!--                    FROM all_tables-->
            <!--                    WHERE partitioned = 'YES'-->
            <!--                    AND upper(owner) = upper('@schemaname')-->
            <!--                    AND upper(table_name) = upper('@name')-->
            <!--                </DefinitionQuery>-->
            <!--            </Partition>-->
            <Datatype>
                <Query>
                    SELECT COUNT(1)
                    FROM (
                    SELECT
                    LOWER(OBJECT_SCHEMA_NAME(c.object_id)) AS OWNER,
                    LOWER(OBJECT_NAME(c.object_id)) AS TABLE_NAME,
                    LOWER(c.name) AS COLUMN_NAME,
                    c.precision AS DATA_PRECISION,
                    c.scale AS DATA_SCALE,
                    c.max_length AS DATA_LENGTH
                    FROM
                    sys.columns AS c
                    INNER JOIN
                    sys.tables AS t ON c.object_id = t.object_id
                    LEFT JOIN
                    sys.views AS v ON t.object_id = v.object_id
                    LEFT JOIN
                    sys.synonyms AS s ON t.name = s.base_object_name
                    WHERE
                    OBJECT_SCHEMA_NAME(c.object_id) = ('@schemaname')
                    -- AND t.type = 'U' -- Only user tables
                    -- AND c.system_type_id = 108 -- Data type 'numeric'
                    AND t.name NOT LIKE '%$%'
                    AND v.object_id IS NULL
                    AND s.object_id IS NULL
                    -- ORDER BY
                    -- TABLE_NAME
                    )res;
                </Query>
            </Datatype>
            <Column>
                <Query>
                    SELECT COUNT(1)
                    FROM INFORMATION_SCHEMA.COLUMNS dtc
                    INNER JOIN INFORMATION_SCHEMA.TABLES dt
                    ON dtc.TABLE_NAME = dt.TABLE_NAME
                    AND dtc.TABLE_SCHEMA = dt.TABLE_SCHEMA
                    WHERE dtc.TABLE_SCHEMA = '@schemaname';
                </Query>
            </Column>
            <!--            <Dblink>-->
            <!--                <Query>-->
            <!--                    select count(1) from dba_objects where object_type = 'DATABASE LINK' AND upper(owner) =-->
            <!--                    upper('@schemaname')-->
            <!--                </Query>-->
            <!--            </Dblink>-->
        </Storage_Objects>
    </Extraction_Queries>
    <Database_Exclusion_Query>
        SELECT DISTINCT LOWER(column_name)
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA NOT IN (
        'db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys'
        )
        AND COLUMN_NAME IS NOT NULL

        UNION

        -- For distinct lower(parameter_name)
        SELECT DISTINCT LOWER(parameter_name)
        FROM INFORMATION_SCHEMA.PARAMETERS
        WHERE SPECIFIC_SCHEMA NOT IN (
        'db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys'
        )
        AND PARAMETER_NAME IS NOT NULL

        UNION

        -- For distinct lower(object_name)
        SELECT DISTINCT LOWER(o.name)
        FROM sys.objects o
        INNER JOIN sys.schemas s ON o.schema_id = s.schema_id
        WHERE s.name NOT IN (
        'db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys'
        )
        AND o.type NOT IN ('TR', 'LO', 'TP', 'IX', 'J', 'EC', 'R', 'Q', 'TB', 'P')
        AND o.name NOT LIKE 'SYS%'
        AND o.name NOT LIKE '%$%'
        AND o.name IS NOT NULL

        UNION

        -- For distinct lower(object_name + '.' + routine_name)
        SELECT DISTINCT LOWER(o.name + '.' + r.routine_name)
        FROM INFORMATION_SCHEMA.ROUTINES r
        INNER JOIN sys.objects o ON r.specific_name = o.name
        INNER JOIN sys.schemas s ON o.schema_id = s.schema_id
        WHERE s.name NOT IN (
        'db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys'
        )
        AND r.routine_type IN ('PROCEDURE', 'FUNCTION')
        AND r.specific_name IS NOT NULL
        AND r.routine_name != 'NEW'
    </Database_Exclusion_Query>
    <Source_Schemas>
        SELECT
        s.name AS schema_name,
        ISNULL(SUM(a.total_pages) * 8 / 1024.0, 0) AS schema_size_mb
        FROM
        sys.schemas s
        JOIN
        sys.objects o ON s.schema_id = o.schema_id
        JOIN
        sys.indexes i ON o.object_id = i.object_id
        JOIN
        sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
        JOIN
        sys.allocation_units a ON p.partition_id = a.container_id
        WHERE
        s.name NOT IN (
        'db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin',
        'db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys'
        )
        GROUP BY
        s.name
        ORDER BY
        s.name;

    </Source_Schemas>
    <Target_Schemas>
        SELECT username FROM DBA_USERS WHERE username NOT IN ( 'SYSTEM', 'SYS', 'APPQOSSYS',
        'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS', 'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS',
        'DVSYS', 'GSMADMIN_INTERNAL', 'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM',
        'XDB', 'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT', 'SYSMAN',
        'SCOTT') AND Account_status = 'OPEN' order by Username
    </Target_Schemas>
    <Conversion>
        <Table>
            SELECT
            s.name AS schema_name,
            t.name AS type_name
            FROM
            sys.types t
            JOIN
            sys.schemas s ON t.schema_id = s.schema_id
            where s.name not in ('sys')
            ORDER BY
            s.name, t.name;
        </Table>
    </Conversion>
</Queries>