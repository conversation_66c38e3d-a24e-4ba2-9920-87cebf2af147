<Queries>
    <Extraction_Queries>
        <Code_Objects>

        </Code_Objects>
        <Storage_Objects>
            <Table>
                <ListQuery>
                    SELECT
                    t.name AS TABLE_NAME,
                    'VALID' AS status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND t.uid = USER_ID('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CASE
                    -- If it's the first column, include CREATE TABLE statement
                    WHEN c.colid = 1 THEN
                    'CREATE TABLE ' + user_name(o.uid) + '.' + o.name + ' (' + CHAR(13) +
                    ' ' + c.name + ' ' + t.name +
                    CASE
                    WHEN t.name IN ('char', 'varchar', 'nchar', 'nvarchar','varbinary','binary') THEN '(' +
                    CAST(c.length AS VARCHAR(5)) + ')'
                    WHEN t.name IN ('decimal', 'numeric') THEN '(' + CAST(c.prec AS VARCHAR(5)) + ',' + CAST(c.scale AS
                    VARCHAR(5)) + ')'
                    ELSE ''
                    END +
                    CASE WHEN c.status &amp; 0x80 = 0x80 THEN ' IDENTITY' ELSE '' END + ','
                    -- If it's the last column, do not add a comma at the end
                    WHEN c.colid = (SELECT MAX(colid) FROM syscolumns WHERE id = o.id) THEN
                    ' ' + c.name + ' ' + t.name +
                    CASE
                    WHEN t.name IN ('char', 'varchar', 'nchar', 'nvarchar','varbinary','binary') THEN '(' +
                    CAST(c.length AS VARCHAR(5)) + ')'
                    WHEN t.name IN ('decimal', 'numeric') THEN '(' + CAST(c.prec AS VARCHAR(5)) + ',' + CAST(c.scale AS
                    VARCHAR(5)) + ')'
                    ELSE ''
                    END + ' );' +
                    CASE WHEN c.status &amp; 0x80 = 0x80 THEN ' IDENTITY' ELSE '' END
                    -- For other columns, add a comma at the end
                    ELSE
                    ' ' + c.name + ' ' + t.name +
                    CASE
                    WHEN t.name IN ('char', 'varchar', 'nchar', 'nvarchar','varbinary','binary') THEN '(' +
                    CAST(c.length AS VARCHAR(5)) + ')'
                    WHEN t.name IN ('decimal', 'numeric') THEN '(' + CAST(c.prec AS VARCHAR(5)) + ',' + CAST(c.scale AS
                    VARCHAR(5)) + ')'
                    ELSE ''
                    END +
                    CASE WHEN c.status &amp; 0x80 = 0x80 THEN ' IDENTITY' ELSE '' END + ','
                    END AS ddl
                    FROM
                    sysobjects o
                    JOIN
                    syscolumns c ON o.id = c.id
                    JOIN
                    systypes t ON c.usertype = t.usertype
                    WHERE
                    o.name = '@tablename' -- Replace with your table name
                    and user_name(o.uid) = '@schemaname' --Replace schemname with your schemaname
                    AND o.type = 'U'
                    ORDER BY
                    colid
                </DefinitionQuery>
            </Table>
            <Not_Null_Constraint>
                <ListQuery>
                    SELECT
                    t.name AS TABLE_NAME,
                    'VALID' AS status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND t.uid = USER_ID('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    EXEC sp_help '@schemaname.@tablename'
                </DefinitionQuery>
            </Not_Null_Constraint>
            <Primary_Key>
                <ListQuery>
                    SELECT
                    t.name AS TABLE_NAME,
                    'VALID' AS status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND t.uid = USER_ID('@schemaname')
                </ListQuery>
                <Primary_Constraint_Name>
                    SELECT o.name , i.name AS Primarykey_Name,
                    'VALID' AS Status
                    FROM sysobjects o
                    JOIN sysindexes i ON o.id = i.id
                    JOIN sysusers u ON o.uid = u.uid
                    WHERE o.type = 'U'
                    and o.name = '@tablename'
                    AND i.status2 &amp; 2 = 2
                    AND lower(u.name) = lower('@schemaname')
                    AND i.indid = 1

                </Primary_Constraint_Name>
                <DefinitionQuery>


                    SELECT
                    user_name(o.uid) AS schema_name,
                    o.name AS table_name,
                    i.name AS primary_key_name,
                    index_col(o.name, i.indid, c.colid) AS column_name,
                    'ALTER TABLE ' + user_name(o.uid) + '.' + o.name + ' ADD CONSTRAINT ' + i.name + ' PRIMARY KEY (' +
                    index_col(o.name, i.indid, c.colid) + ');' AS AlterStatement FROM
                    sysobjects o
                    JOIN
                    sysindexes i ON o.id = i.id
                    JOIN
                    syscolumns c ON o.id = c.id
                    WHERE
                    o.type = 'U' -- User tables
                    AND i.name = '@primary_name' -- Primary key constraint name
                    AND index_col(o.name, i.indid, c.colid) IS NOT NULL
                </DefinitionQuery>
            </Primary_Key>
            <Unique_Constraint>
                <ListQuery>
                    SELECT
                    t.name AS TABLE_NAME,
                    'VALID' AS status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND t.uid = USER_ID('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    EXEC sp_helpconstraint '@schemaname.@tablename'
                </DefinitionQuery>
            </Unique_Constraint>

            <Foreign_Key>
                <ListQuery>
                    SELECT
                    t.name AS TABLE_NAME,
                    'VALID' AS status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND t.uid = USER_ID('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    EXEC sp_helpconstraint '@schemaname.@tablename'
                </DefinitionQuery>
            </Foreign_Key>
            <Default_Constraint>
                <ListQuery>
                    SELECT
                    t.name AS TABLE_NAME,
                    'VALID' AS status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND t.uid = USER_ID('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    user_name(t.uid) as schema_name,
                    t.name AS TableName,
                    col.name AS ColumnName,
                    dc.name AS ConstraintName,
                    'ENABLED' AS Status,
                    c.text AS ConstraintDefinition,
                    'ALTER TABLE ' + user_name(t.uid) + '.' + t.name + ' ADD CONSTRAINT ' + dc.name +''+ c.text +
                    ' FOR ' + col.name AS AlterStatement
                    FROM
                    sysobjects t
                    JOIN
                    syscolumns col ON t.id = col.id
                    JOIN
                    sysobjects dc ON dc.id = col.cdefault
                    JOIN
                    syscomments c ON dc.id = c.id
                    WHERE
                    t.type = 'U'
                    AND lower(t.name) = lower('@tablename')
                    AND lower(user_name(t.uid)) = lower('@schemaname')
                    ORDER BY
                    col.name
                </DefinitionQuery>
            </Default_Constraint>
            <Check_Constraint>
                <ListQuery>
                    SELECT
                    t.name AS TABLE_NAME,
                    'VALID' AS status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND t.uid = USER_ID('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT user_name(o.uid) as schema_name,
                    o.name AS table_name,
                    c.name AS column_name,
                    co.name As constraint_name,
                    comm.text AS constraint_definition,
                    'ALTER TABLE ' + user_name(o.uid) + '.' + o.name + ' ADD CONSTRAINT ' + co.name + ' ' + comm.text
                    +';' as AlterStatement
                    FROM sysobjects o
                    JOIN sysconstraints sc ON o.id = sc.tableid
                    JOIN syscolumns c ON sc.colid = c.colid AND o.id = c.id
                    JOIN sysobjects co ON sc.constrid = co.id --AND co.type = 'R' -- Get check constraints
                    JOIN syscomments comm ON co.id = comm.id
                    WHERE o.name = '@tablename' -- Replace with your actual table name
                    and user_name(o.uid) = '@schemaname' --Replace schemname with your schemaname
                    ORDER BY c.name
                </DefinitionQuery>
            </Check_Constraint>
            <Index>
                <ListQuery>
                    SELECT
                    t.name AS TABLE_NAME,
                    'VALID' AS status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND t.uid = USER_ID('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    EXEC sp_helpindex '@schemaname.@tablename'
                </DefinitionQuery>
            </Index>
            <Unique_Index>
                <ListQuery>
                    SELECT
                    t.name AS TABLE_NAME,
                    'VALID' AS status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'U'
                    AND t.uid = USER_ID('@schemaname')
                </ListQuery>
                <Unique_Definition>
                    EXEC sp_helpconstraint '@schemaname.@tablename'
                </Unique_Definition>
                <DefinitionQuery>
                    EXEC sp_helpindex '@schemaname.@tablename'
                </DefinitionQuery>
            </Unique_Index>
            <View>
                <ListQuery>
                    SELECT name as View_name,'ENABLED' as Status
                    FROM
                    sysobjects t
                    WHERE
                    t.type = 'V'
                    AND lower(user_name(t.uid)) = lower('@schemaname')
                    AND name NOT LIKE 'sys%' ORDER BY name

                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    c.text
                    FROM
                    sysobjects o
                    JOIN
                    syscomments c ON o.id = c.id
                    WHERE
                    o.name = '@tablename'
                    AND o.type = 'V'
                    AND LOWER(USER_NAME(o.uid)) = LOWER('@schemaname')
                    ORDER BY
                    c.colid

                </DefinitionQuery>
            </View>

        </Storage_Objects>
    </Extraction_Queries>
    <Database_Exclusion_Query>

    </Database_Exclusion_Query>
    <Source_Schemas>
        SELECT
        DISTINCT user_name(uid)
        FROM
        sysobjects;
    </Source_Schemas>
    <Target_Schemas>
        SELECT name
        FROM sys.schemas
        WHERE name NOT IN
        ('db_accessadmin','db_backupoperator','db_datareader','db_datawriter','db_ddladmin','db_denydatareader','db_denydatawriter','db_owner','db_securityadmin',
        'guest','INFORMATION_SCHEMA','sys')
    </Target_Schemas>

</Queries>