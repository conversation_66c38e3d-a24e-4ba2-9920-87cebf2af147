import argparse, os, sys
from import_file import import_file
from common_modules.api import api_authentication, decrypt_file, delete_files_in_directory

parser = argparse.ArgumentParser()

parser.add_argument('-task', '--task_name',
                    help="Provide task name", nargs='?', const='', default='')
parser.add_argument('-project_id', '--project_id',
                    help="Provide project id", nargs='?', const='', default='')
parser.add_argument('-mig_name', '--migration_name',
                    help="Provide migration name", nargs='?', const='', default='')
parser.add_argument('-iteration_id', '--iteration_id',
                    help="Provide iteration id", nargs='?', const='', default='')
parser.add_argument('-object_category', '--object_category',
                    help="Provide object category", nargs='?', const='', default='')
parser.add_argument('-source_connection_id', '--source_connection_id',
                    help="Provide source connection id", nargs='?', const='', default='')
parser.add_argument('-dr_connection_id', '--dr_connection_id',
                    help="Provide deployment ready connection id", nargs='?', const='', default='')
parser.add_argument('-target_connection_id', '--target_connection_id',
                    help="Provide target connection id", nargs='?', const='', default='')
parser.add_argument('-cloud_category', '--cloud_category',
                    help="Provide cloud category", nargs='?', const='', default='')
parser.add_argument('-schema', '--schema_name',
                    help="Provide schema name", nargs='?', const='', default='')
parser.add_argument('-target_schema', '--target_schema',
                    help="Provide target schema name", nargs='?', const='', default='')
parser.add_argument('-object_type', '--object_type',
                    help="Provide object type", nargs='?', const='', default='')
parser.add_argument('-object_name', '--object_name',
                    help="Provide object_name", nargs='?', const='', default='')
parser.add_argument('-deploy_file_path', '--deploy_file_path',
                    help="Provide deploy_file_path", nargs='?', const='', default='')
parser.add_argument('-deploy_file_create_option', '--deploy_file_create_option',
                    help="Provide deploy_file_create_option", nargs='?', const='', default='')
parser.add_argument('-deploy_file_check', '--deploy_file_check',
                    help="Provide deploy_file_check", nargs='?', const='', default='')
parser.add_argument('-observations', '--observations',
                    help="Provide observations", nargs='?', const='', default='')
parser.add_argument('-fix_duration', '--fix_duration',
                    help="Provide fix_duration", nargs='?', const='', default='')

parser.add_argument('-process_type', '--process_type',
                    help="Provide Process Type E2E", nargs='?', const='', default='')
parser.add_argument('-mig_operation', '--mig_operation',
                    help="Provide operation for data migration for E2E", nargs='?', const='', default='')
parser.add_argument('-table_name', '--table_name',
                    help="Provide table_name", nargs='?', const='', default='')
parser.add_argument('-file_name', '--file_name',
                    help="Provide file_name", nargs='?', const='', default='')
parser.add_argument('-chunk_size', '--chunk_size',
                    help="Provide chunk size", nargs='?', const='', default='')
parser.add_argument('-concurrency', '--concurrency',
                    help="Provide concurrency", nargs='?', const='', default='')
parser.add_argument('-request_cpu', '--request_cpu',
                    help="Provide requests cpu", nargs='?', const='', default='')
parser.add_argument('-limit_cpu', '--limit_cpu',
                    help="Provide limit cpu", nargs='?', const='', default='')
parser.add_argument('-read_size', '--read_size',
                    help="Provide read_size for sql loader", nargs='?', const='', default='')
parser.add_argument('-batch_size', '--batch_size',
                    help="Provide batch_size", nargs='?', const='', default='')
parser.add_argument('-restart_flag', '--restart_flag',
                    help="Provide restart_flag", nargs='?', const='', default='')
parser.add_argument('-db_flag', '--db_flag',
                    help="Provide db_flag", nargs='?', const='', default='')

parser.add_argument('-host_name', '--host_name',
                    help="Provide hostname", nargs='?', const='', default='')
parser.add_argument('-port', '--port',
                    help="Provide port", nargs='?', const='', default='')
parser.add_argument('-service_name', '--service_name',
                    help="Provide service_name", nargs='?', const='', default='')
parser.add_argument('-user_name', '--user_name',
                    help="Provide user_name", nargs='?', const='', default='')
parser.add_argument('-password', '--password',
                    help="Provide password", nargs='?', const='', default='')
parser.add_argument('-dba_mode', '--dba_mode',
                    help="Provide dba_mode", nargs='?', const='', default='')

parser.add_argument('-data_load_type', '--data_load_type',
                    help="Provide data_load_type", nargs='?', const='', default='')
parser.add_argument('-cdc_load_type', '--cdc_load_type',
                    help="Provide cdc_load_type", nargs='?', const='', default='')

args = parser.parse_args()

task_name = args.task_name
project_id = args.project_id
migration_name = args.migration_name
iteration_id = args.iteration_id
object_category = args.object_category
source_connection_id = args.source_connection_id
dr_connection_id = args.dr_connection_id
target_connection_id = args.target_connection_id
cloud_category = args.cloud_category
schema_name = args.schema_name
target_schema = args.target_schema
object_type = args.object_type
object_name = args.object_name
deploy_file_path = args.deploy_file_path
deploy_file_create_option = args.deploy_file_create_option
deploy_file_check = args.deploy_file_check
observations = args.observations
fix_duration = args.fix_duration

process_type = args.process_type
mig_operation = args.mig_operation
table_name = args.table_name
file_name = args.file_name
chunk_size = args.chunk_size
concurrency = args.concurrency
request_cpu = args.request_cpu
limit_cpu = args.limit_cpu
read_size = args.read_size
batch_size = args.batch_size
restart_flag = args.restart_flag
db_flag = args.db_flag

host_name = args.host_name
port = args.port
service_name = args.service_name
user_name = args.user_name
password = args.password
dba_mode = args.dba_mode

data_load_type = args.data_load_type
cdc_load_type = args.cdc_load_type

local_root_path = (os.path.dirname(os.path.realpath(__file__)))
config_path = local_root_path + '/' + 'config.py'
sys.path.append(config_path)
import_object = import_file(config_path)
working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')

token_data = api_authentication()

migration_path = local_root_path + '/' + 'Migrations' + '/' + migration_name
common_modules_path = local_root_path + '/' + 'common_modules'

if __name__ == "__main__":
    if task_name in ['Conversion']:
        decrypt_file(token_data, migration_path + '/' + 'object_conversion.py', cloud_category)
        from tmp.object_conversion import conversion_trigger

        conversion_trigger = conversion_trigger
        delete_files_in_directory(working_directory_path)
        conversion_trigger(task_name, project_id, migration_name, iteration_id, object_category, object_type,
                           schema_name, target_schema, source_connection_id, dr_connection_id, target_connection_id,
                           restart_flag, cloud_category)
        delete_files_in_directory(working_directory_path)

        decrypt_file(token_data, migration_path + '/' + 'validation.py', cloud_category)
        from tmp.validation import validation_trigger

        validation_trigger = validation_trigger
        delete_files_in_directory(working_directory_path)
        validation_trigger(task_name, project_id, migration_name, iteration_id, object_category, schema_name,
                           target_schema, source_connection_id, dr_connection_id, cloud_category)
        delete_files_in_directory(working_directory_path)

        if process_type in ['E2E']:
            decrypt_file(token_data, migration_path + '/' + 'e2e_api.py', cloud_category)
            from tmp.e2e_api import e2e_api_trigger

            e2e_api_trigger = e2e_api_trigger
            delete_files_in_directory(working_directory_path)
            e2e_api_trigger(process_type, project_id, migration_name, iteration_id, source_connection_id,
                            dr_connection_id, target_connection_id, schema_name, target_schema, table_name,
                            mig_operation, data_load_type, cdc_load_type, request_cpu, limit_cpu, file_name,
                            cloud_category)

    elif task_name in ['Validation']:
        decrypt_file(token_data, migration_path + '/' + 'validation.py', cloud_category)
        from tmp.validation import validation_trigger

        validation_trigger = validation_trigger
        delete_files_in_directory(working_directory_path)
        validation_trigger(task_name, project_id, migration_name, iteration_id, object_category, schema_name,
                           target_schema, source_connection_id, target_connection_id, cloud_category)
        delete_files_in_directory(working_directory_path)
    elif task_name in ['Pipeline_Validation']:
        decrypt_file(token_data, migration_path + '/' + 'validation.py', cloud_category)
        from tmp.validation import pipeline_validation_trigger

        pipeline_validation_trigger = pipeline_validation_trigger
        delete_files_in_directory(working_directory_path)
        pipeline_validation_trigger(task_name, project_id, migration_name, iteration_id, object_category, schema_name, target_schema,
                       source_connection_id, target_connection_id, cloud_category)
        delete_files_in_directory(working_directory_path)

    elif task_name in ['Deploy_File']:
        decrypt_file(token_data, common_modules_path + '/' + 'deploy_file.py', cloud_category)
        from tmp.deploy_file import deploy_file_trigger

        deploy_file_trigger = deploy_file_trigger
        delete_files_in_directory(working_directory_path)
        deploy_file_trigger(task_name, project_id, migration_name, target_connection_id, schema_name,
                            target_schema, deploy_file_path, cloud_category)

    elif task_name in ['Deployment_File']:
        decrypt_file(token_data, common_modules_path + '/' + 'deployment_file_creation.py', cloud_category)
        from tmp.deployment_file_creation import deployment_file_creation_trigger

        deployment_file_creation_trigger = deployment_file_creation_trigger
        delete_files_in_directory(working_directory_path)
        deployment_file_creation_trigger(task_name, project_id, migration_name, deploy_file_create_option, iteration_id,
                                         target_connection_id, target_schema, object_type, deploy_file_check,
                                         cloud_category)

    elif task_name in ['Deploy_Objects']:
        decrypt_file(token_data, common_modules_path + '/' + 'deploy_objects.py', cloud_category)
        from tmp.deploy_objects import deploy_objects_trigger

        deploy_objects_trigger = deploy_objects_trigger
        delete_files_in_directory(working_directory_path)
        deploy_objects_trigger(task_name, project_id, migration_name, iteration_id, schema_name, target_connection_id,
                               target_schema, object_type, object_name, cloud_category)
    elif task_name in ['Deploy_Pipeline']:
        decrypt_file(token_data, common_modules_path + '/' + 'deploy_objects.py', cloud_category)
        from tmp.deploy_objects import deploy_pipeline_trigger

        deploy_pipeline_trigger = deploy_pipeline_trigger
        delete_files_in_directory(working_directory_path)
        deploy_pipeline_trigger(task_name, project_id, migration_name, iteration_id, schema_name,source_connection_id,
                             target_connection_id, target_schema, object_type, object_name, restart_flag,cloud_category)

    elif task_name in ['Manual_Conversion']:
        decrypt_file(token_data, common_modules_path + '/' + 'manual_conversion.py', cloud_category)
        from tmp.manual_conversion import manual_conversion_trigger

        manual_conversion_trigger = manual_conversion_trigger
        delete_files_in_directory(working_directory_path)
        manual_conversion_trigger(task_name, project_id, migration_name, iteration_id, source_connection_id,
                                  schema_name,
                                  target_connection_id,
                                  target_schema, object_type, object_name, observations, fix_duration, cloud_category)

    elif task_name in ['Extract_Schemas']:
        decrypt_file(token_data, migration_path + '/' + 'extract_schemas.py', cloud_category)
        from tmp.extract_schemas import extract_schemas_trigger

        extract_schemas_trigger = extract_schemas_trigger
        delete_files_in_directory(working_directory_path)
        extract_schemas_trigger(task_name, project_id, migration_name, target_connection_id, cloud_category)

    elif task_name in ['Error_Dependent_Files']:
        decrypt_file(token_data, migration_path + '/' + 'error_dependent_files.py', cloud_category)
        from tmp.error_dependent_files import error_dependent_files_trigger

        error_dependent_files_trigger = error_dependent_files_trigger
        delete_files_in_directory(working_directory_path)
        error_dependent_files_trigger(task_name, project_id, migration_name, iteration_id, source_connection_id,
                                      schema_name, object_type, object_name, cloud_category)
        delete_files_in_directory(working_directory_path)
