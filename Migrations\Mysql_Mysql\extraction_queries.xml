<Queries>
    <Extraction_Queries>
        <Code_Objects>
            <Procedure>
                <ListQuery>
                    SELECT ROUTINE_NAME as PROCEDURE_NAME,'VALID' as STATUS
                    FROM information_schema.ROUTINES
                    WHERE ROUTINE_TYPE = 'PROCEDURE'
                    AND UPPER(ROUTINE_SCHEMA) = UPPER('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    select
                    case
                    when param_list is not null
                    and param_list != '' then
                    CONCAT(
                    'CREATE PROCEDURE ', r.ROUTINE_SCHEMA, '.', r.ROUTINE_NAME,
                    ' (',
                    param_list,
                    ') ',
                    r.ROUTINE_DEFINITION, ';\n'
                    )
                    else
                    CONCAT(
                    'CREATE PROCEDURE ', r.ROUTINE_SCHEMA, '.', r.ROUTINE_NAME,
                    ' ( ) \n',
                    r.ROUTINE_DEFINITION, ';\n'
                    )
                    end as procedure_ddl
                    from
                    information_schema.ROUTINES r
                    left join
                    (
                    select
                    p.SPECIFIC_SCHEMA,
                    p.SPECIFIC_NAME,
                    GROUP_CONCAT(
                    CONCAT(
                    p.PARAMETER_NAME, ' ',
                    p.DATA_TYPE,
                    case
                    when upper(p.DATA_TYPE) in ('VARCHAR', 'CHAR', 'TEXT') and p.CHARACTER_MAXIMUM_LENGTH is not null then
                    CONCAT('(', p.CHARACTER_MAXIMUM_LENGTH, ')')
                    else
                    ''
                    end
                    )
                    order by p.ORDINAL_POSITION
                    separator ', '
                    ) as param_list
                    from
                    information_schema.PARAMETERS p
                    where
                    UPPER(p.SPECIFIC_SCHEMA) = UPPER('@schemaname')
                    and UPPER(p.SPECIFIC_NAME) = UPPER('@name')
                    group by
                    p.SPECIFIC_SCHEMA,
                    p.SPECIFIC_NAME) as params
                    on
                    r.ROUTINE_SCHEMA = params.SPECIFIC_SCHEMA
                    and r.ROUTINE_NAME = params.SPECIFIC_NAME
                    where
                    UPPER(r.ROUTINE_SCHEMA) = UPPER('@schemaname')
                    -- Replace with your schema name
                    and UPPER(r.ROUTINE_NAME) = UPPER('@name')
                    -- Replace with your procedure name
                    and r.ROUTINE_TYPE = 'PROCEDURE'
                    order by
                    r.ROUTINE_NAME
                </DefinitionQuery>

            </Procedure>
            <Function>
                <ListQuery>
                    SELECT ROUTINE_NAME as FUNCTION_NAME,'VALID' as STATUS
                    FROM information_schema.ROUTINES
                    WHERE ROUTINE_TYPE = 'FUNCTION'
                    AND UPPER(ROUTINE_SCHEMA) = UPPER('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CASE
                        WHEN param_list IS NOT NULL AND param_list != '' THEN
                            CONCAT(
                                'CREATE FUNCTION ', r.ROUTINE_SCHEMA, '.', r.ROUTINE_NAME,
                                ' (', param_list, ') RETURNS ',
                                CONCAT(
                                    r.DATA_TYPE,
                                    CASE
                                        WHEN upper(r.DATA_TYPE) IN ('VARCHAR', 'CHAR', 'TEXT') AND r.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN
                                            CONCAT('(', r.CHARACTER_MAXIMUM_LENGTH, ')')
                                        WHEN upper(r.DATA_TYPE) IN ('DECIMAL', 'NUMERIC', 'FLOAT') AND r.NUMERIC_PRECISION IS NOT NULL THEN
                                            CONCAT('(', r.NUMERIC_PRECISION,
                                                CASE
                                                    WHEN r.NUMERIC_SCALE IS NOT NULL THEN CONCAT(',', r.NUMERIC_SCALE)
                                                    ELSE ''
                                                END,
                                            ')')
                                        WHEN upper(r.DATA_TYPE) = 'DOUBLE' THEN
                                            '' -- No length required for DOUBLE
                                        ELSE
                                            ''
                                    END
                                ),
                                ' DETERMINISTIC\n',
                                r.ROUTINE_DEFINITION, ';\n'
                            )
                        ELSE
                            CONCAT(
                                'CREATE FUNCTION ', r.ROUTINE_SCHEMA, '.', r.ROUTINE_NAME,
                                ' RETURNS ',
                                CONCAT(
                                    r.DATA_TYPE,
                                    CASE
                                        WHEN upper(r.DATA_TYPE) IN ('VARCHAR', 'CHAR', 'TEXT') AND r.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN
                                            CONCAT('(', r.CHARACTER_MAXIMUM_LENGTH, ')')
                                        WHEN upper(r.DATA_TYPE) IN ('DECIMAL', 'NUMERIC', 'FLOAT') AND r.NUMERIC_PRECISION IS NOT NULL THEN
                                            CONCAT('(', r.NUMERIC_PRECISION,
                                                CASE
                                                    WHEN r.NUMERIC_SCALE IS NOT NULL THEN CONCAT(',', r.NUMERIC_SCALE)
                                                    ELSE ''
                                                END,
                                            ')')
                                        WHEN upper(r.DATA_TYPE) = 'DOUBLE' THEN
                                            '' -- No length required for DOUBLE
                                        ELSE
                                            ''
                                    END
                                ),
                                ' DETERMINISTIC\n',
                                r.ROUTINE_DEFINITION, ';\n'
                            )
                    END AS function_ddl
                FROM
                    information_schema.ROUTINES r
                LEFT JOIN
                    (SELECT
                        p.SPECIFIC_SCHEMA, p.SPECIFIC_NAME,
                        GROUP_CONCAT(
                            CONCAT(
                                p.PARAMETER_NAME, ' ',
                                p.DATA_TYPE,
                                CASE
                                    WHEN upper(p.DATA_TYPE) IN ('VARCHAR', 'CHAR', 'TEXT') AND p.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN
                                        CONCAT('(', p.CHARACTER_MAXIMUM_LENGTH, ')')
                                    WHEN upper(p.DATA_TYPE) IN ('DECIMAL', 'NUMERIC', 'FLOAT') AND p.NUMERIC_PRECISION IS NOT NULL THEN
                                        CONCAT('(', p.NUMERIC_PRECISION,
                                            CASE
                                                WHEN p.NUMERIC_SCALE IS NOT NULL THEN CONCAT(',', p.NUMERIC_SCALE)
                                                ELSE ''
                                            END,
                                        ')')
                                    WHEN upper(p.DATA_TYPE) = 'DOUBLE' THEN
                                        '' -- No length required for DOUBLE
                                    ELSE
                                        ''
                                END
                            )
                            ORDER BY p.ORDINAL_POSITION
                            SEPARATOR ', '
                        ) AS param_list
                    FROM
                        information_schema.PARAMETERS p
                    WHERE
                        UPPER(p.SPECIFIC_SCHEMA) = UPPER('@schemaname')
                        AND UPPER(p.SPECIFIC_NAME) = UPPER('@name')
                    GROUP BY
                        p.SPECIFIC_SCHEMA, p.SPECIFIC_NAME) AS params
                ON
                    r.ROUTINE_SCHEMA = params.SPECIFIC_SCHEMA
                    AND r.ROUTINE_NAME = params.SPECIFIC_NAME
                WHERE
                    UPPER(r.ROUTINE_SCHEMA) = UPPER('@schemaname')
                    AND UPPER(r.ROUTINE_NAME) = UPPER('@name')
                    AND r.ROUTINE_TYPE = 'FUNCTION'
                ORDER BY
                    r.ROUTINE_NAME

                </DefinitionQuery>
            </Function>
            <Trigger>
                <ListQuery>
                    select
                    TRIGGER_NAME as Trigger_name,
                    'VALID' as status
                    from
                    information_schema.TRIGGERS
                    where
                    UPPER(TRIGGER_SCHEMA) = UPPER('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    select
                    CONCAT(
                    'CREATE TRIGGER ', TRIGGER_SCHEMA, '.', TRIGGER_NAME, ' \n', ACTION_TIMING, ' ', EVENT_MANIPULATION,
                    ' ON ', EVENT_OBJECT_SCHEMA, '.', EVENT_OBJECT_TABLE,
                    '\nFOR EACH ', ACTION_ORIENTATION, ' ',
                    '\n', ACTION_STATEMENT, ';'
                    ) as TRIGGER_DDL
                    from
                    information_schema.TRIGGERS
                    where
                    UPPER(TRIGGER_SCHEMA) = UPPER('@schemaname')
                    and UPPER(TRIGGER_NAME) = UPPER('@name')

                </DefinitionQuery>

            </Trigger>
            <Event>
                <ListQuery>
                    select
                    event_name,
                    'VALID' as Status
                    from
                    information_schema.EVENTS
                    where
                    UPPER(EVENT_SCHEMA) = UPPER('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'CREATE EVENT ', EVENT_SCHEMA, '.', EVENT_NAME, '\n',
                    'ON SCHEDULE ',
                    CASE
                    WHEN EVENT_TYPE = 'ONE TIME' THEN CONCAT('AT ', EXECUTE_AT)
                    WHEN EVENT_TYPE = 'RECURRING' THEN CONCAT('EVERY ', INTERVAL_VALUE, ' ', INTERVAL_FIELD)
                    END, '\n',
                    'ON COMPLETION ', ON_COMPLETION, '\n',
                    'DO ', EVENT_DEFINITION, ';'
                    ) AS EVENT_DDL
                    FROM
                    information_schema.EVENTS
                    WHERE
                    UPPER(EVENT_SCHEMA) = UPPER('@schemaname')
                    and UPPER(EVENT_NAME) = UPPER('@name')
                </DefinitionQuery>
            </Event>
        </Code_Objects>
        <Storage_Objects>
            <!--            <Type></Type>-->
            <!--            <sequence></sequence>-->
            <Table>
                <ListQuery>
                    select TABLE_NAME,'VALID' as STATUS FROM information_schema.tables where table_type = 'BASE TABLE'
                    and CREATE_OPTIONS != 'partitioned' and UPPER(table_schema)= UPPER('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    select
                    CONCAT('CREATE TABLE ',
                    c.TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(CONCAT(COLUMN_NAME, ' ', COLUMN_TYPE, if(EXTRA != '', CONCAT(' ', EXTRA), '')) order by ORDINAL_POSITION separator ', '),
                    ');') as create_table_ddl
                    from
                    information_schema.columns c
                    join information_schema.tables t on c.TABLE_SCHEMA = t.TABLE_SCHEMA and c.TABLE_NAME = t.TABLE_NAME
                    where
                    c.TABLE_SCHEMA = '@schemaname'
                    and c.TABLE_NAME = lower('@name')
                    and t.TABLE_TYPE = 'BASE TABLE'
                    and not exists (
                    select
                    1
                    from
                    information_schema.partitions p
                    where
                    TABLE_SCHEMA = '@schemaname'
                    and PARTITION_NAME is not null
                    and p.table_name = t.TABLE_NAME)
                    group by c.TABLE_NAME
                </DefinitionQuery>
            </Table>
            <Partition>
                <ListQuery>
                    select
                    distinct TABLE_NAME,'VALID' as STATUS
                    from
                    information_schema.partitions p
                    where
                    TABLE_SCHEMA = '@schemaname'
                    and PARTITION_NAME is not null
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'CREATE TABLE ', TABLE_SCHEMA, '.', TABLE_NAME, ' (',
                    GROUP_CONCAT(
                    CONCAT(
                    COLUMN_NAME, ' ', COLUMN_TYPE,
                    IF(IS_NULLABLE = 'NO', ' NOT NULL', ''),
                    IF(COLUMN_DEFAULT IS NOT NULL, CONCAT(' DEFAULT ', COLUMN_DEFAULT), ''),
                    IF(EXTRA != '', CONCAT(' ', EXTRA), '')
                    ) ORDER BY ORDINAL_POSITION SEPARATOR ', '
                    ),
                    ') ',
                    IFNULL((SELECT CONCAT(
                    'PARTITION BY ', PARTITION_METHOD, '(', PARTITION_EXPRESSION, ') (',
                    GROUP_CONCAT(
                    CONCAT(
                    'PARTITION ', PARTITION_NAME,
                    ' VALUES ',
                    CASE
                    WHEN PARTITION_METHOD = 'RANGE' THEN CONCAT('LESS THAN (', PARTITION_DESCRIPTION, ')')
                    WHEN PARTITION_METHOD = 'LIST' THEN CONCAT('IN (', PARTITION_DESCRIPTION, ')')
                    ELSE ''
                    END
                    ) SEPARATOR ', '
                    ), ')'
                    ) FROM information_schema.PARTITIONS
                    WHERE TABLE_SCHEMA = COLUMNS.TABLE_SCHEMA
                    AND TABLE_NAME = COLUMNS.TABLE_NAME
                    AND PARTITION_NAME IS NOT NULL
                    GROUP BY TABLE_SCHEMA, TABLE_NAME, PARTITION_METHOD, PARTITION_EXPRESSION), '') , ';'
                    ) AS Create_table_Partition_DDL
                    FROM
                    information_schema.COLUMNS COLUMNS
                    WHERE
                    TABLE_SCHEMA = '@schemaname' AND TABLE_NAME = '@name'
                    GROUP BY
                    TABLE_SCHEMA, TABLE_NAME
                </DefinitionQuery>
            </Partition>
            <Not_Null_Constraint>
                <ListQuery>
                     select
                    concat(c.TABLE_NAME, '-', c.COLUMN_NAME),
                    'VALID' as STATUS
                    from
                    information_schema.columns c
                    inner join information_schema.tables t on
                    c.TABLE_NAME = t.TABLE_NAME
                    and c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    where
                    UPPER(c.TABLE_SCHEMA) = UPPER('@schemaname')
                    and t.TABLE_TYPE = 'BASE TABLE'
                    and c.IS_NULLABLE = 'NO'
                    order by
                    c.TABLE_NAME,
                    c.COLUMN_NAME
                </ListQuery>
                <DefinitionQuery>
                    select
                    concat('ALTER TABLE ', TABLE_SCHEMA, '.', TABLE_NAME, ' modify COLUMN ', COLUMN_NAME, ' ',
                    COLUMN_TYPE, ' NOT NULL;') as NotNull
                    from
                    information_schema.COLUMNS
                    where
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    and upper(concat(TABLE_NAME,'-',COLUMN_NAME)) = upper('@name')
                    order by
                    TABLE_NAME,
                    COLUMN_NAME
                </DefinitionQuery>

            </Not_Null_Constraint>
            <Primary_Key>
                <ListQuery>
                    select
                    concat(TABLE_NAME,'-',CONSTRAINT_NAME),'VALID' as STATUS
                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    AND CONSTRAINT_NAME = 'PRIMARY'
                    GROUP BY
                    TABLE_NAME
                </ListQuery>
                <DefinitionQuery>
                    select CONCAT( 'ALTER TABLE ', TABLE_SCHEMA , '.', TABLE_NAME, ' ADD CONSTRAINT PRIMARY KEY (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION SEPARATOR ', '), ');' ) AS PrimaryKey FROM
                    information_schema.KEY_COLUMN_USAGE WHERE upper(concat(TABLE_NAME,'-',CONSTRAINT_NAME)) =
                    upper('@name') AND CONSTRAINT_NAME = 'PRIMARY' and UPPER(TABLE_SCHEMA) = UPPER('@schemaname') GROUP
                    BY TABLE_SCHEMA,TABLE_NAME
                </DefinitionQuery>

            </Primary_Key>
            <Unique_Constraint>
                <ListQuery>
                    select
                    concat(TC.TABLE_NAME,'-',TC.CONSTRAINT_NAME),'VALID' as STATUS
                    FROM
                    information_schema.TABLE_CONSTRAINTS TC
                    JOIN
                    information_schema.KEY_COLUMN_USAGE KCU
                    ON TC.CONSTRAINT_NAME = KCU.CONSTRAINT_NAME
                    AND TC.TABLE_NAME = KCU.TABLE_NAME
                    AND TC.TABLE_SCHEMA = KCU.TABLE_SCHEMA
                    WHERE
                    TC.CONSTRAINT_TYPE = 'UNIQUE'
                    AND UPPER(TC.TABLE_SCHEMA) = UPPER('@schemaname')
                    GROUP BY
                    TC.TABLE_SCHEMA, TC.TABLE_NAME, TC.CONSTRAINT_NAME
                    ORDER BY
                    TC.TABLE_NAME, TC.CONSTRAINT_NAME

                </ListQuery>
                <DefinitionQuery>
                    select
                    CONCAT(
                    'ALTER TABLE ',
                    TC.TABLE_SCHEMA,'.',
                    TC.TABLE_NAME,
                    ' ADD CONSTRAINT ',
                    TC.CONSTRAINT_NAME,
                    ' UNIQUE (',
                    GROUP_CONCAT(KCU.COLUMN_NAME ORDER BY KCU.ORDINAL_POSITION),
                    ');'
                    ) AS ddl_statement
                    FROM
                    information_schema.TABLE_CONSTRAINTS TC
                    JOIN
                    information_schema.KEY_COLUMN_USAGE KCU
                    ON TC.CONSTRAINT_NAME = KCU.CONSTRAINT_NAME
                    AND TC.TABLE_NAME = KCU.TABLE_NAME
                    AND TC.TABLE_SCHEMA = KCU.TABLE_SCHEMA
                    WHERE
                    upper(concat(TC.TABLE_NAME,'-',TC.CONSTRAINT_NAME)) = upper('@name')
                    AND UPPER(TC.TABLE_SCHEMA) = UPPER('@schemaname')
                    GROUP BY
                    TC.TABLE_SCHEMA, TC.TABLE_NAME, TC.CONSTRAINT_NAME
                    ORDER BY
                    TC.TABLE_NAME, TC.CONSTRAINT_NAME
                </DefinitionQuery>
            </Unique_Constraint>
            <Foreign_Key>
                <ListQuery>
                    SELECT
                    concat(TABLE_NAME,'-',CONSTRAINT_NAME),'VALID' as STATUS
                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    and REFERENCED_TABLE_NAME IS NOT NULL
                    GROUP BY
                    TABLE_NAME, CONSTRAINT_NAME
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'ALTER TABLE ',
                    TABLE_SCHEMA ,
                    '.',
                    TABLE_NAME,
                    ' ADD CONSTRAINT ',
                    CONSTRAINT_NAME,
                    ' FOREIGN KEY (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION SEPARATOR ', '),
                    ') REFERENCES ',
                    REFERENCED_TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(REFERENCED_COLUMN_NAME ORDER BY POSITION_IN_UNIQUE_CONSTRAINT SEPARATOR ', '),
                    ');'
                    ) AS ForeignKey
                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    and upper(concat(TABLE_NAME,'-',CONSTRAINT_NAME)) = upper('@name')
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                    GROUP BY
                    TABLE_SCHEMA,TABLE_NAME, CONSTRAINT_NAME,REFERENCED_TABLE_NAME
                </DefinitionQuery>
            </Foreign_Key>
            <Default_Constraint>
                <ListQuery>
                    select TABLE_NAME AS VIEW_NAME,'VALID' as STATUS FROM information_schema.tables where table_type =
                    'VIEW' and UPPER(table_schema)= UPPER('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT CONCAT( 'CREATE VIEW ', REPLACE(REPLACE(TABLE_SCHEMA, '`', ''), '`', ''), '.',
                    REPLACE(REPLACE(TABLE_NAME, '`', ''), '`', ''), ' AS ', REPLACE(VIEW_DEFINITION, '`', ''), ';' ) AS
                    view_DDL FROM INFORMATION_SCHEMA.VIEWS WHERE UPPER(TABLE_SCHEMA) = UPPER('@schemaname') and
                    UPPER(table_name )= UPPER('@name')
                </DefinitionQuery>
            </Default_Constraint>
            <Check_Constraint>
                <ListQuery>
                    SELECT CONCAT(tc.TABLE_NAME, '-', tc.CONSTRAINT_NAME) AS constraint_info, 'VALID' AS STATUS FROM
                    information_schema.TABLE_CONSTRAINTS tc JOIN information_schema.CHECK_CONSTRAINTS cc ON
                    tc.CONSTRAINT_NAME = cc.CONSTRAINT_NAME AND tc.CONSTRAINT_SCHEMA = cc.CONSTRAINT_SCHEMA WHERE
                    UPPER(tc.CONSTRAINT_SCHEMA) = UPPER('@schemaname') AND tc.CONSTRAINT_TYPE = 'CHECK' ORDER BY
                    tc.CONSTRAINT_SCHEMA, tc.TABLE_NAME
                </ListQuery>
                <DefinitionQuery>
                    SELECT CONCAT( 'ALTER TABLE ', cc.CONSTRAINT_SCHEMA, '.', tc.TABLE_NAME, ' ADD CONSTRAINT ',
                    cc.CONSTRAINT_NAME, ' CHECK (', cc.CHECK_CLAUSE, ');' ) AS CheckConstraints FROM
                    information_schema.CHECK_CONSTRAINTS cc JOIN information_schema.TABLE_CONSTRAINTS tc ON
                    cc.CONSTRAINT_NAME = tc.CONSTRAINT_NAME AND cc.CONSTRAINT_SCHEMA = tc.CONSTRAINT_SCHEMA WHERE
                    UPPER(CONCAT(tc.TABLE_NAME, '-', tc.CONSTRAINT_NAME)) = UPPER('@name') AND
                    UPPER(cc.CONSTRAINT_SCHEMA) = UPPER('@schemaname') AND tc.CONSTRAINT_TYPE = 'CHECK' ORDER BY
                    cc.CONSTRAINT_SCHEMA, tc.TABLE_NAME
                </DefinitionQuery>
            </Check_Constraint>
            <Index>
                <ListQuery>
                    select concat(TABLE_NAME,'-',INDEX_NAME),'VALID' as STATUS FROM information_schema.STATISTICS WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname') and INDEX_NAME != 'PRIMARY' GROUP BY INDEX_NAME,
                    TABLE_NAME, NON_UNIQUE, INDEX_TYPE
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'CREATE ',
                    IF(NON_UNIQUE = 0, 'UNIQUE ', ''),
                    'INDEX ',
                    INDEX_NAME,
                    ' ON ',
                    TABLE_SCHEMA,
                    '.',
                    TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX SEPARATOR ', '),
                    ');'
                    ) AS IndexDef
                    FROM
                    information_schema.STATISTICS
                    WHERE
                    upper(concat(TABLE_NAME,'-',INDEX_NAME)) = upper('@name') and
                    upper(TABLE_SCHEMA) = upper('@schemaname')
                    GROUP BY
                    INDEX_NAME, TABLE_NAME, NON_UNIQUE, INDEX_TYPE,TABLE_SCHEMA
                </DefinitionQuery>
            </Index>
            <View>
                <ListQuery>
                    select TABLE_NAME AS VIEW_NAME,'VALID' as STATUS FROM information_schema.tables where table_type =
                    'VIEW' and UPPER(table_schema)= UPPER('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'CREATE VIEW ',
                    REPLACE(REPLACE(TABLE_SCHEMA, '`', ''), '`', ''),
                    '.',
                    REPLACE(REPLACE(TABLE_NAME, '`', ''), '`', ''),
                    ' AS ',
                    REPLACE(VIEW_DEFINITION, '`', ''),
                    ';'
                    ) AS view_DDL
                    FROM
                    INFORMATION_SCHEMA.VIEWS

                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname') and UPPER(table_name )= UPPER('@name')

                </DefinitionQuery>
            </View>
        </Storage_Objects>
    </Extraction_Queries>
    <Database_Exclusion_Query>
        SELECT table_name AS object_name
        FROM information_schema.tables
        WHERE table_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')
        AND table_type = 'BASE TABLE'

        UNION ALL

        SELECT column_name AS object_name
        FROM information_schema.columns
        WHERE table_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT routine_name AS object_name
        FROM information_schema.routines
        WHERE routine_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT index_name AS object_name
        FROM information_schema.statistics
        WHERE table_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')
        AND index_name NOT LIKE 'PRIMARY'
        AND index_name NOT LIKE 'idx%'
        AND index_name NOT LIKE 'sys%'

        UNION ALL

        SELECT parameter_name AS object_name
        FROM information_schema.parameters
        WHERE specific_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT trigger_name AS object_name
        FROM information_schema.triggers
        WHERE trigger_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT partition_name AS object_name
        FROM information_schema.partitions
        WHERE partition_name IS NOT NULL
        AND table_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT event_name AS object_name
        FROM information_schema.events
        WHERE event_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

    </Database_Exclusion_Query>
    <Source_Schemas>
       SELECT
            table_schema AS schema_name,
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS schema_size_mb
        FROM
            information_schema.tables
        WHERE
            table_schema NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys')
        GROUP BY
            table_schema
        ORDER BY
            table_schema;
    </Source_Schemas>
    <Target_Schemas>
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys')
    </Target_Schemas>
    <Deployment_File>
        <Table>
            SELECT
            ('CREATE TABLE ' || table_schema || '.' || table_name || ' (' ||
            array_to_string(
            ARRAY(
            SELECT
            ' ' || column_name || ' ' || data_type ||
            CASE
            WHEN character_maximum_length IS NOT NULL THEN '(' || character_maximum_length || ')'
            ELSE ''
            END ||
            CASE
            WHEN is_nullable = 'NO' THEN ' NOT NULL'
            ELSE ''
            END
            FROM
            information_schema.columns
            WHERE
            columns.table_schema = tables.table_schema
            AND columns.table_name = tables.table_name
            ORDER BY
            ordinal_position
            ),
            ', '
            ) || ');')
            FROM
            information_schema.tables
            WHERE
            table_schema NOT IN ('pg_catalog', 'information_schema')
            AND table_type = 'BASE TABLE'
            and upper(table_schema)='@schemaname'
        </Table>
        <Primary_Key>
            SELECT

            'ALTER TABLE ' || table_schema || '.' || table_name ||

            ' ADD CONSTRAINT ' || constraint_name ||

            ' PRIMARY KEY (' || column_list || ');'

            FROM (

            SELECT

            tc.table_schema,

            tc.table_name,

            tc.constraint_name,

            string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list

            FROM information_schema.table_constraints AS tc

            JOIN information_schema.key_column_usage AS kcu

            ON tc.constraint_name = kcu.constraint_name

            AND tc.table_schema = kcu.table_schema

            AND tc.table_name = kcu.table_name

            WHERE upper(tc.constraint_type) = 'PRIMARY KEY'

            AND upper(tc.table_schema) = upper('@schemaname')

            GROUP BY tc.table_schema, tc.table_name, tc.constraint_name

            ) AS primary_keys
        </Primary_Key>
        <Unique_Constraint>
            SELECT

            'ALTER TABLE ' || table_schema || '.' || table_name ||

            ' ADD CONSTRAINT ' || constraint_name ||

            ' UNIQUE (' || column_list || ');'

            FROM (

            SELECT

            tc.table_schema,

            tc.table_name,

            tc.constraint_name,

            string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list

            FROM information_schema.table_constraints AS tc

            JOIN information_schema.key_column_usage AS kcu

            ON tc.constraint_name = kcu.constraint_name

            AND tc.table_schema = kcu.table_schema

            AND tc.table_name = kcu.table_name

            WHERE upper(tc.constraint_type) = 'UNIQUE'

            AND upper(tc.table_schema) = upper('@schemaname')
            GROUP BY tc.table_schema, tc.table_name, tc.constraint_name

            ) AS unique_constraints
        </Unique_Constraint>
        <Foreign_Key>
            WITH cte AS (
            SELECT
            conname AS constraint_name,
            ns.nspname AS schema_name,
            tbl.relname AS table_name,
            a.attname AS column_name,
            nsf.nspname AS referenced_schema,
            reftbl.relname AS referenced_table,
            fa.attname AS referenced_column
            FROM
            pg_constraint AS c
            JOIN
            pg_class AS tbl ON c.conrelid = tbl.oid
            JOIN
            pg_namespace AS ns ON tbl.relnamespace = ns.oid
            JOIN
            pg_attribute AS a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid
            JOIN
            pg_class AS reftbl ON c.confrelid = reftbl.oid
            JOIN
            pg_namespace AS nsf ON reftbl.relnamespace = nsf.oid
            JOIN
            pg_attribute AS fa ON fa.attnum = ANY(c.confkey) AND fa.attrelid = c.confrelid
            )
            SELECT
            'ALTER TABLE ' || schema_name || '.' || table_name || ' ADD CONSTRAINT ' || constraint_name ||
            ' FOREIGN KEY (' || column_name || ') REFERENCES ' || referenced_schema || '.' || referenced_table ||
            ' (' || referenced_column || ');'
            FROM
            cte where upper(schema_name) = upper('@schemaname')
        </Foreign_Key>
        <Not_Null_Constraint>

            SELECT
            'ALTER TABLE ' || table_schema || '.' || table_name || ' ALTER COLUMN ' || column_name || ' SET NOT NULL;'

            FROM information_schema.columns

            WHERE upper(is_nullable) = 'NO'

            AND upper(table_schema) = upper('@schemaname')

        </Not_Null_Constraint>
        <Check_Constraint>
            with cte as(select
            n.nspname AS schema_name,
            conname AS constraint_name,
            conrelid::regclass AS table_name,
            pg_get_constraintdef(c.oid) AS check_expression
            FROM pg_constraint c
            JOIN pg_namespace n ON n.oid = c.connamespace
            WHERE confrelid = 0 AND contype = 'c'
            )select 'alter table '||''||table_name||''||' add constraint '||''||constraint_name||' check '||''||
            check_expression ||''||';'
            from cte where upper(cte.schema_name) = upper('@schemaname')
        </Check_Constraint>
        <Default_Constraint>
            SELECT
            'alter table ' ||table_schema||'.'||table_name||' ALTER COLUMN '||column_name||' SET DEFAULT '||
            column_default ||''||';'
            FROM information_schema.columns
            WHERE column_default IS NOT null
            and upper(table_schema)=upper('@schemaname')
        </Default_Constraint>
        <Index>
            SELECT
            indexdef||';' AS index_definition
            FROM
            pg_indexes
            WHERE
            schemaname NOT IN ('pg_catalog', 'information_schema')
            and upper(schemaname)=upper('@schemaname')

        </Index>
        <View>
            SELECT

            'create view'||' '||schemaname||'.'||viewname||' '||' as'||' ' ||definition AS view_definition
            FROM
            pg_views
            WHERE
            schemaname NOT IN ('pg_catalog', 'information_schema')
            and upper(schemaname)= upper('@schemaname')
        </View>
        <Materialized_View>
            SELECT
            'create materialized view'||' '||schemaname||'.'||matviewname||' '||' as'||' ' || definition AS
            materialized_view_definition
            FROM
            pg_matviews
            WHERE
            schemaname NOT IN ('pg_catalog', 'information_schema')
            and upper(schemaname)=upper('@schemaname')
        </Materialized_View>
        <Procedure>
            SELECT
            pg_get_functiondef(p.oid) AS ddl
            FROM
            pg_proc p
            JOIN
            pg_namespace ns ON p.pronamespace = ns.oid
            WHERE
            ns.nspname NOT IN ('pg_catalog', 'information_schema')
            AND p.prokind = 'p'
            and ns.nspname=upper('@schemaname')

        </Procedure>
        <Function>
            SELECT
            pg_get_functiondef(p.oid) AS ddl
            FROM
            pg_proc p
            JOIN
            pg_namespace ns ON p.pronamespace = ns.oid
            WHERE
            ns.nspname NOT IN ('pg_catalog', 'information_schema')
            AND p.prokind = 'f'
            and upper(ns.nspname) = upper('@schemaname')
        </Function>
    </Deployment_File>
    <Server_Parameters>
        <CPU_Usage>
            WITH
            cpu_cores AS (
            SELECT value AS num_cores
            FROM v$osstat
            WHERE stat_name = 'NUM_CPUS'
            ),
            cpu_usage AS (
            SELECT SUM(value) AS total_cpu_usage
            FROM v$sys_time_model
            WHERE stat_name IN ('DB CPU', 'background cpu time')
            ),
            elapsed_time AS (
            SELECT value AS elapsed_seconds
            FROM v$sys_time_model
            WHERE stat_name = 'DB time'
            )
            SELECT
            (SELECT num_cores FROM cpu_cores) AS cpu_cores,
            ROUND(
            (SELECT total_cpu_usage FROM cpu_usage) /
            ((SELECT num_cores FROM cpu_cores) * (SELECT elapsed_seconds FROM elapsed_time)) * 100,
            2
            ) AS
            cpu_utilization_percentage
            FROM
            dual
        </CPU_Usage>
        <Memory_Allocated>
            WITH
            sga_memory AS (
            SELECT SUM(bytes) AS total_sga
            FROM v$sgastat
            ),
            pga_memory AS (
            SELECT SUM(value) AS total_pga
            FROM v$pgastat
            WHERE name = 'total PGA allocated'
            ),
            total_physical_memory AS (
            SELECT value AS
            total_memory
            FROM v$osstat
            WHERE stat_name = 'PHYSICAL_MEMORY_BYTES'
            ),
            sga_parameters AS (
            SELECT
            name,
            value/(1024*1024) AS value
            FROM
            v$parameter
            WHERE
            name IN ('sga_target', 'sga_max_size')
            )
            SELECT
            ROUND((SELECT total_sga FROM sga_memory) / (1024 * 1024), 2) AS sga_total_mb,
            ROUND((SELECT total_pga FROM pga_memory) / (1024 * 1024), 2) AS pga_total_mb,
            ROUND(((SELECT total_sga FROM sga_memory) + (SELECT total_pga FROM pga_memory)) / (1024 * 1024), 2) AS
            db_total_utlizn_mem_mb,
            ROUND((SELECT total_memory FROM total_physical_memory) / (1024 * 1024), 2) AS system_total_memory_mb,
            ROUND(((SELECT total_sga FROM sga_memory) + (SELECT total_pga FROM pga_memory)) / (SELECT total_memory FROM
            total_physical_memory) * 100, 2) AS db_vs_sys_mem_util_percentg,
            ROUND((((SELECT total_sga FROM sga_memory)/(1024 * 1024) + (SELECT total_pga FROM pga_memory)/(1024 * 1024))
            / ((SELECT value FROM sga_parameters WHERE name = 'sga_max_size') + (SELECT value FROM sga_parameters WHERE
            name = 'sga_target'))) * 100, 2) AS db_vs_total_db_mem_util_perctg,
            (SELECT value FROM sga_parameters WHERE name = 'sga_target') +
            (SELECT value FROM sga_parameters WHERE name = 'sga_max_size') AS Db_totoal_Memory
            FROM
            dual
        </Memory_Allocated>
        <DB_Size>
            SELECT
            (SELECT SUM(bytes) / 1024 / 1024 / 1024 FROM dba_data_files) +
            (SELECT SUM(bytes) / 1024 / 1024 / 1024 FROM dba_temp_files)
            AS total_size_in_gb FROM
            dual
        </DB_Size>
        <TableSpace_Size>
            SELECT
            tablespace_name,
            SUM(bytes) / 1024 / 1024 / 1024 AS size_in_gb
            FROM
            dba_data_files
            GROUP BY
            tablespace_name
            ORDER BY
            tablespace_name
        </TableSpace_Size>
        <PhysicalwritetotalIOrequest>
            WITH Snapshots AS (
            SELECT
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID AS BEGIN_SNAP_ID,
            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,
            s2.SNAP_ID AS END_SNAP_ID,
            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME
            FROM
            DBA_HIST_SNAPSHOT s1
            JOIN
            DBA_HIST_SNAPSHOT s2
            ON
            s2.SNAP_ID = s1.SNAP_ID + 1
            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER -- Ensure consecutive snapshots in the same instance
            WHERE
            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') -- Adjust as needed
            )
            SELECT
            s.INSTANCE_NUMBER,
            s.BEGIN_SNAP_ID,
            s.BEGIN_INTERVAL_TIME,
            s.END_SNAP_ID,
            s.END_INTERVAL_TIME,
            -- (ss2.VALUE - ss1.VALUE),
            (ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))) AS phy_wrt_IO_reqs_per_sec
            FROM
            Snapshots s
            JOIN
            DBA_HIST_SYSSTAT ss1
            ON
            ss1.SNAP_ID = s.BEGIN_SNAP_ID
            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss1.STAT_NAME = 'physical write total IO requests'
            JOIN
            DBA_HIST_SYSSTAT ss2
            ON
            ss2.SNAP_ID = s.END_SNAP_ID
            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss2.STAT_NAME = 'physical write total IO requests'
            ORDER BY
            BEGIN_SNAP_ID,INSTANCE_NUMBER, phy_wrt_IO_reqs_per_sec DESC
        </PhysicalwritetotalIOrequest>
        <PhysicalreadtotalIOrequests>
            WITH Snapshots AS (
            SELECT
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID AS BEGIN_SNAP_ID,
            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,
            s2.SNAP_ID AS END_SNAP_ID,
            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME
            FROM
            DBA_HIST_SNAPSHOT s1
            JOIN
            DBA_HIST_SNAPSHOT s2
            ON
            s2.SNAP_ID = s1.SNAP_ID + 1
            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER -- Ensure consecutive snapshots in the same instance
            WHERE
            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') -- Adjust as needed
            )
            SELECT
            s.INSTANCE_NUMBER,
            s.BEGIN_SNAP_ID,
            s.BEGIN_INTERVAL_TIME,
            s.END_SNAP_ID,
            s.END_INTERVAL_TIME,
            -- (ss2.VALUE - ss1.VALUE),
            (ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))) AS phy_read_IO_reqs_per_sec
            FROM
            Snapshots s
            JOIN
            DBA_HIST_SYSSTAT ss1
            ON
            ss1.SNAP_ID = s.BEGIN_SNAP_ID
            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss1.STAT_NAME = 'physical read total IO requests'
            JOIN
            DBA_HIST_SYSSTAT ss2
            ON
            ss2.SNAP_ID = s.END_SNAP_ID
            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss2.STAT_NAME = 'physical read total IO requests'
            ORDER BY
            BEGIN_SNAP_ID,INSTANCE_NUMBER, phy_read_IO_reqs_per_sec DESC
        </PhysicalreadtotalIOrequests>
        <Physicalwritebytes>
            WITH Snapshots AS (
            SELECT
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID AS BEGIN_SNAP_ID,
            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,
            s2.SNAP_ID AS END_SNAP_ID,
            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME
            FROM
            DBA_HIST_SNAPSHOT s1
            JOIN
            DBA_HIST_SNAPSHOT s2
            ON
            s2.SNAP_ID = s1.SNAP_ID + 1
            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER -- Ensure consecutive snapshots in the same instance
            WHERE
            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') -- Adjust as needed
            )
            SELECT
            s.INSTANCE_NUMBER,
            s.BEGIN_SNAP_ID,
            s.BEGIN_INTERVAL_TIME,
            s.END_SNAP_ID,
            s.END_INTERVAL_TIME,
            ((ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024) AS
            phy_write_per_sec_IN_MB
            FROM
            Snapshots s
            JOIN
            DBA_HIST_SYSSTAT ss1
            ON
            ss1.SNAP_ID = s.BEGIN_SNAP_ID
            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss1.STAT_NAME = 'physical write bytes'
            JOIN
            DBA_HIST_SYSSTAT ss2
            ON
            ss2.SNAP_ID = s.END_SNAP_ID
            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss2.STAT_NAME = 'physical write bytes'
            ORDER BY
            BEGIN_SNAP_ID,INSTANCE_NUMBER, phy_write_per_sec_IN_MB DESC
        </Physicalwritebytes>
        <Physicalreadbytes>
            WITH Snapshots AS (
            SELECT
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID AS BEGIN_SNAP_ID,
            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,
            s2.SNAP_ID AS END_SNAP_ID,
            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME
            FROM
            DBA_HIST_SNAPSHOT s1
            JOIN
            DBA_HIST_SNAPSHOT s2
            ON
            s2.SNAP_ID = s1.SNAP_ID + 1
            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER -- Ensure consecutive snapshots in the same instance
            WHERE
            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') -- Adjust as needed
            )
            SELECT
            s.INSTANCE_NUMBER,
            s.BEGIN_SNAP_ID,
            s.BEGIN_INTERVAL_TIME,
            s.END_SNAP_ID,
            s.END_INTERVAL_TIME,
            ((ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024) AS
            phy_read_per_sec_IN_MB
            FROM
            Snapshots s
            JOIN
            DBA_HIST_SYSSTAT ss1
            ON
            ss1.SNAP_ID = s.BEGIN_SNAP_ID
            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss1.STAT_NAME = 'physical read bytes'
            JOIN
            DBA_HIST_SYSSTAT ss2
            ON
            ss2.SNAP_ID = s.END_SNAP_ID
            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss2.STAT_NAME = 'physical read bytes'
            ORDER BY
            BEGIN_SNAP_ID,INSTANCE_NUMBER, phy_read_per_sec_IN_MB DESC
        </Physicalreadbytes>
        <ToIdentifythetimeinterval>
            SELECT
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID AS BEGIN_SNAP_ID,
            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,
            s2.SNAP_ID AS END_SNAP_ID,
            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME
            FROM
            DBA_HIST_SNAPSHOT s1
            JOIN
            DBA_HIST_SNAPSHOT s2
            ON
            s2.SNAP_ID = s1.SNAP_ID + 1
            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER -- Ensure consecutive snapshots in the same instance
            WHERE
            ROWNUM = 1
            ORDER BY
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID,
            s1.BEGIN_INTERVAL_TIME,
            s2.SNAP_ID,
            s2.BEGIN_INTERVAL_TIME
        </ToIdentifythetimeinterval>
        <PhysicalwritetotalIOrequestsMAXandAVG>
            WITH Snapshots AS (
            SELECT
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID AS BEGIN_SNAP_ID,
            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,
            s2.SNAP_ID AS END_SNAP_ID,
            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME
            FROM
            DBA_HIST_SNAPSHOT s1
            JOIN
            DBA_HIST_SNAPSHOT s2
            ON
            s2.SNAP_ID = s1.SNAP_ID + 1
            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER -- Ensure consecutive snapshots in the same instance
            WHERE
            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') -- Adjust as needed
            )
            SELECT
            -- s.BEGIN_SNAP_ID,
            -- s.END_SNAP_ID,
            max(sum((ss2.VALUE - ss1.VALUE) /((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) *
            24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))) AS max_phy_wrt_IO_reqs_per_sec,
            avg(sum((ss2.VALUE - ss1.VALUE) /((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) *
            24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))) AS avg_phy_wrt_IO_reqs_per_sec
            FROM
            Snapshots s
            JOIN
            DBA_HIST_SYSSTAT ss1
            ON
            ss1.SNAP_ID = s.BEGIN_SNAP_ID
            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss1.STAT_NAME = 'physical write total IO requests'
            JOIN
            DBA_HIST_SYSSTAT ss2
            ON
            ss2.SNAP_ID = s.END_SNAP_ID
            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss2.STAT_NAME = 'physical write total IO requests'
            group by BEGIN_SNAP_ID,END_SNAP_ID
        </PhysicalwritetotalIOrequestsMAXandAVG>
        <PhysicalreadtotalIOrequestsMAXandAVG>
            WITH Snapshots AS (
            SELECT
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID AS BEGIN_SNAP_ID,
            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,
            s2.SNAP_ID AS END_SNAP_ID,
            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME
            FROM
            DBA_HIST_SNAPSHOT s1
            JOIN
            DBA_HIST_SNAPSHOT s2
            ON
            s2.SNAP_ID = s1.SNAP_ID + 1
            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER -- Ensure consecutive snapshots in the same instance
            WHERE
            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') -- Adjust as needed
            )
            SELECT
            max((ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)))) AS max_phy_read_IO_reqs_per_sec,
            avg((ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)))) AS avg_phy_read_IO_reqs_per_sec
            FROM
            Snapshots s
            JOIN
            DBA_HIST_SYSSTAT ss1
            ON
            ss1.SNAP_ID = s.BEGIN_SNAP_ID
            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss1.STAT_NAME = 'physical read total IO requests'
            JOIN
            DBA_HIST_SYSSTAT ss2
            ON
            ss2.SNAP_ID = s.END_SNAP_ID
            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss2.STAT_NAME = 'physical read total IO requests'
        </PhysicalreadtotalIOrequestsMAXandAVG>
        <PhysicalwritebytesMAXandAVG>
            WITH Snapshots AS (
            SELECT
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID AS BEGIN_SNAP_ID,
            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,
            s2.SNAP_ID AS END_SNAP_ID,
            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME
            FROM
            DBA_HIST_SNAPSHOT s1
            JOIN
            DBA_HIST_SNAPSHOT s2
            ON
            s2.SNAP_ID = s1.SNAP_ID + 1
            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER -- Ensure consecutive snapshots in the same instance
            WHERE
            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') -- Adjust as needed
            )
            SELECT
            max(((ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024)) AS
            max_phy_write_per_sec_IN_MB,
            avg(((ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024)) AS
            avg_write_per_sec_IN_MB

            FROM
            Snapshots s
            JOIN
            DBA_HIST_SYSSTAT ss1
            ON
            ss1.SNAP_ID = s.BEGIN_SNAP_ID
            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss1.STAT_NAME = 'physical write bytes'
            JOIN
            DBA_HIST_SYSSTAT ss2
            ON
            ss2.SNAP_ID = s.END_SNAP_ID
            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss2.STAT_NAME = 'physical write bytes'
        </PhysicalwritebytesMAXandAVG>
        <PhysicalreadbytesForAVGandMAX>
            WITH Snapshots AS (
            SELECT
            s1.INSTANCE_NUMBER,
            s1.SNAP_ID AS BEGIN_SNAP_ID,
            s1.BEGIN_INTERVAL_TIME AS BEGIN_INTERVAL_TIME,
            s2.SNAP_ID AS END_SNAP_ID,
            s2.BEGIN_INTERVAL_TIME AS END_INTERVAL_TIME
            FROM
            DBA_HIST_SNAPSHOT s1
            JOIN
            DBA_HIST_SNAPSHOT s2
            ON
            s2.SNAP_ID = s1.SNAP_ID + 1
            AND s2.INSTANCE_NUMBER = s1.INSTANCE_NUMBER -- Ensure consecutive snapshots in the same instance
            WHERE
            s1.BEGIN_INTERVAL_TIME >= TO_DATE('2024-01-01', 'YYYY-MM-DD') -- Adjust as needed
            )
            SELECT
            max(((ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024)) AS
            max_phy_read_per_sec_IN_MB,
            avg(((ss2.VALUE - ss1.VALUE) /
            ((EXTRACT(DAY FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 24 * 60 * 60) +
            (EXTRACT(HOUR FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60 * 60) +
            (EXTRACT(MINUTE FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME)) * 60) +
            EXTRACT(SECOND FROM (s.END_INTERVAL_TIME - s.BEGIN_INTERVAL_TIME))))/ (1024 * 1024)) AS
            avg_phy_read_per_sec_IN_MB
            FROM
            Snapshots s
            JOIN
            DBA_HIST_SYSSTAT ss1
            ON
            ss1.SNAP_ID = s.BEGIN_SNAP_ID
            AND ss1.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss1.STAT_NAME = 'physical read bytes'
            JOIN
            DBA_HIST_SYSSTAT ss2
            ON
            ss2.SNAP_ID = s.END_SNAP_ID
            AND ss2.INSTANCE_NUMBER = s.INSTANCE_NUMBER
            AND ss2.STAT_NAME = 'physical read bytes'
        </PhysicalreadbytesForAVGandMAX>
        <OracleDBVersion>
            select * from v$version
        </OracleDBVersion>
        <Check_DB_Statistics>
            SELECT name, value FROM v$parameter WHERE name in ('statistics_level','control_management_pack_access')
        </Check_DB_Statistics>
    </Server_Parameters>
    <Conversion>
        <Table>
            WITH REFERENCED_SCHEMAS AS (
            SELECT DISTINCT REFERENCED_OWNER
            FROM ALL_DEPENDENCIES
            WHERE OWNER = '@schemaname'
            AND REFERENCED_OWNER NOT IN ('SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER',
            'CTXSYS',
            'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS', 'DVSYS', 'GSMADMIN_INTERNAL',
            'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB',
            'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT',
            'SYSMAN', 'SCOTT')
            ),
            FULL_NAMES AS (
            SELECT DISTINCT d.OWNER, d.NAME AS TABLE_NAME
            FROM ALL_DEPENDENCIES d
            JOIN REFERENCED_SCHEMAS r
            ON d.OWNER = r.REFERENCED_OWNER
            WHERE d.TYPE = 'TABLE'
            )
            SELECT /*parrallel(8) */
            t.OWNER AS Schema_Name,
            'TABLE' AS Object_Type,
            t.TABLE_NAME AS Object_Name,
            t.COLUMN_NAME AS Input_Variable,
            t.DATA_TYPE AS Data_Type
            FROM ALL_TAB_COLUMNS t
            JOIN FULL_NAMES fn
            ON t.OWNER = fn.OWNER AND t.TABLE_NAME = fn.TABLE_NAME
            WHERE EXISTS (
            SELECT 1
            FROM DBA_USERS u
            WHERE u.USERNAME = t.OWNER AND u.ACCOUNT_STATUS = 'OPEN'
            )
            UNION
            SELECT t1.OWNER AS Schema_Name,
            'TABLE' AS Object_Type,
            t1.TABLE_NAME AS Object_Name,
            t1.COLUMN_NAME AS Input_Variable,
            t1.DATA_TYPE AS Data_Type
            FROM ALL_TAB_COLUMNS t1
            WHERE t1.OWNER='@schemaname'
            ORDER BY Schema_Name,Object_Name
        </Table>
        <Temporary_Table>
            SELECT DISTINCT
            t1.owner AS Schema_name,
            'Temporary_Table' AS Object_Type,
            t1.table_name AS Table_name,
            c.column_name AS Column_name,
            c.data_type AS Data_type
            FROM all_tables t1 JOIN
            all_tab_columns c ON
            t1.owner = c.owner AND t1.table_name = c.table_name
            WHERE t1.temporary = 'Y' AND t1.table_name IN (
            SELECT t.table_name FROM all_tables t
            WHERE t.temporary = 'Y'
            AND t.owner NOT IN (
            'SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS',
            'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS', 'DVSYS', 'GSMADMIN_INTERNAL',
            'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB',
            'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT',
            'SYSMAN', 'SCOTT', 'ADT')
            AND t.owner IN (
            SELECT DISTINCT d.referenced_owner AS referenced_schema
            FROM all_dependencies d WHERE d.owner = '@schemaname'
            AND d.referenced_owner NOT IN (
            'SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS',
            'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS', 'DVSYS', 'GSMADMIN_INTERNAL',
            'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB',
            'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT',
            'SYSMAN', 'SCOTT')))
            ORDER BY t1.owner, t1.table_name, c.column_name
        </Temporary_Table>
        <Code_Objects>
            WITH REFERENCED_SCHEMAS AS (
            SELECT DISTINCT REFERENCED_OWNER
            FROM ALL_DEPENDENCIES
            WHERE OWNER = '@schemaname'
            AND REFERENCED_OWNER NOT IN ('SYSTEM', 'SYS', 'APPQOSSYS', 'REMOTE_SCHEDULER_AGENT', 'DBSFWUSER', 'CTXSYS',
            'SI_INFORMTN_SCHEMA', 'PUBLIC', 'AUDSYS', 'OJVMSYS', 'DVSYS', 'GSMADMIN_INTERNAL',
            'ORDPLUGINS', 'ORDDATA', 'MDSYS', 'LBACSYS', 'OLAPSYS', 'OUTLN', 'ORACLE_OCM', 'XDB',
            'WMSYS', 'ORDSYS', 'DBSNMP', 'DVF', 'APEX_030200', 'EXFSYS', 'OWBSYS', 'OWBSYS_AUDIT',
            'SYSMAN', 'SCOTT')
            ),
            PROCEDURE_FUNCTIONS AS (
            SELECT DISTINCT
            d.OWNER || '.' || d.NAME AS FULL_NAME,
            d.OWNER,
            d.NAME,
            d.TYPE
            FROM ALL_DEPENDENCIES d
            JOIN REFERENCED_SCHEMAS r
            ON d.OWNER = r.REFERENCED_OWNER
            WHERE d.TYPE IN ('PROCEDURE', 'FUNCTION')
            ),
            PROCEDURE_FUNCTIONS_ALT AS (
            SELECT DISTINCT
            d.OWNER || '.' || d.NAME AS FULL_NAME,
            d.OWNER,
            d.NAME,
            d.TYPE
            FROM ALL_DEPENDENCIES d
            WHERE d.TYPE IN ('PROCEDURE', 'FUNCTION')
            AND d.OWNER = '@schemaname'
            ) SELECT
            p.OWNER AS Schema_Name,
            CASE
            WHEN p.TYPE = 'PROCEDURE' AND a.PACKAGE_NAME IS NOT NULL AND a.PACKAGE_NAME LIKE '%.%' THEN
            'PACKAGE_PROCEDURE'
            WHEN p.TYPE = 'FUNCTION' AND a.PACKAGE_NAME IS NOT NULL AND a.PACKAGE_NAME LIKE '%.%' THEN
            'PACKAGE_FUNCTION'
            WHEN p.TYPE = 'PROCEDURE' THEN 'PROCEDURE'
            WHEN p.TYPE = 'FUNCTION' THEN 'FUNCTION'
            ELSE 'PACKAGE'
            END AS Object_Type,
            CASE
            WHEN p.TYPE IN ('PROCEDURE', 'FUNCTION') AND a.OBJECT_NAME IS NOT NULL THEN
            CASE
            WHEN p.OWNER || '.' || p.NAME = a.OWNER || '.' || a.OBJECT_NAME THEN a.OBJECT_NAME
            ELSE p.NAME
            END
            WHEN a.PACKAGE_NAME IS NOT NULL THEN
            a.PACKAGE_NAME || '.' || a.OBJECT_NAME
            ELSE p.NAME
            END AS Object_Name,
            a.argument_name AS Input_Variable,
            a.data_type AS Data_Type
            FROM ALL_ARGUMENTS a
            JOIN PROCEDURE_FUNCTIONS p
            ON UPPER(a.OWNER || '.' || a.OBJECT_NAME) = UPPER(p.FULL_NAME)
            WHERE a.IN_OUT IN ('IN', 'INOUT')
            AND a.ARGUMENT_NAME IS NOT NULL
            UNION ALL
            SELECT
            p.OWNER AS Schema_Name,
            CASE
            WHEN p.TYPE = 'PROCEDURE' AND a.PACKAGE_NAME IS NOT NULL AND a.PACKAGE_NAME LIKE '%.%' THEN
            'PACKAGE_PROCEDURE'
            WHEN p.TYPE = 'FUNCTION' AND a.PACKAGE_NAME IS NOT NULL AND a.PACKAGE_NAME LIKE '%.%' THEN
            'PACKAGE_FUNCTION' -- Only add 'PACKAGE_FUNCTION' for functions in a package
            WHEN p.TYPE = 'PROCEDURE' THEN 'PROCEDURE'
            WHEN p.TYPE = 'FUNCTION' THEN 'FUNCTION'
            ELSE 'PACKAGE'
            END AS Object_Type,
            CASE
            WHEN p.TYPE IN ('PROCEDURE', 'FUNCTION') AND a.OBJECT_NAME IS NOT NULL THEN
            CASE
            WHEN p.OWNER || '.' || p.NAME = a.OWNER || '.' || a.OBJECT_NAME THEN a.OBJECT_NAME
            ELSE p.NAME
            END
            WHEN a.PACKAGE_NAME IS NOT NULL THEN
            a.PACKAGE_NAME || '.' || a.OBJECT_NAME
            ELSE p.NAME
            END AS Object_Name,
            a.argument_name AS Input_Variable,
            a.data_type AS Data_Type
            FROM ALL_ARGUMENTS a
            JOIN PROCEDURE_FUNCTIONS_ALT p
            ON UPPER(a.OWNER || '.' || a.OBJECT_NAME) = UPPER(p.FULL_NAME)
            WHERE a.IN_OUT IN ('IN', 'INOUT') -- Include IN and INOUT parameters
            AND a.ARGUMENT_NAME IS NOT NULL
            UNION ALL
            SELECT
            OWNER AS Schema_Name,
            CASE
            WHEN PACKAGE_NAME IS NOT NULL THEN 'PACKAGE_PROCEDURE'
            ELSE 'PROCEDURE'
            END AS Object_Type,
            CASE
            WHEN PACKAGE_NAME IS NOT NULL AND OBJECT_NAME IS NOT NULL THEN PACKAGE_NAME || '.' || OBJECT_NAME
            ELSE OBJECT_NAME
            END AS Object_Name,
            ARGUMENT_NAME AS Input_Variable,
            DATA_TYPE AS Data_Type
            FROM ALL_ARGUMENTS
            WHERE OWNER = '@schemaname' AND ARGUMENT_NAME IS NOT NULL
            ORDER BY Schema_Name, Object_Type, Object_Name, Input_Variable
        </Code_Objects>
    </Conversion>
    <User_Roles_Permissions>
        <Users>
            SELECT username,'CREATE USER ' || username || ' IDENTIFIED BY ' || username || ';'
            FROM dba_users
            WHERE account_status = 'OPEN'
            AND oracle_maintained = 'N'
            ORDER BY username
        </Users>
        <Roles>
            select
            role,
            'CREATE ROLE ' || role || ';'
            from
            dba_roles
            where
            oracle_maintained = 'N'
            -- Excludes system-maintained roles
            order by
            role
        </Roles>
        <Roles_Privilages>
            SELECT
            owner,
            grantee,
            object_name,
            privilege,
            TYPE,
            'GRANT ' || privilege || ' ON ' || owner || '.' || object_name || ' TO ' || grantee || ';' AS

            grant_statement
            FROM
            (
            -- System Privileges
            SELECT
            NULL AS owner,
            grantee,
            NULL AS object_name,
            privilege,
            NULL AS TYPE
            FROM
            dba_sys_privs
            WHERE
            grantee IN (
            SELECT
            ROLE
            FROM
            dba_roles
            WHERE
            oracle_maintained = 'N'
            -- Excludes system-maintained roles

            )
            UNION
            -- Table Privileges
            SELECT
            owner,
            grantee,
            table_name AS object_name,
            privilege,
            TYPE
            FROM
            dba_tab_privs
            WHERE
            grantee IN (
            SELECT
            ROLE
            FROM
            dba_roles
            WHERE
            oracle_maintained = 'N'
            -- Excludes system-maintained roles

            )

            )


        </Roles_Privilages>
        <User_Privilages>
            SELECT

            GRANTEE,
            PRIVILEGE, -- The privilege granted (system or role)
            PRIV_TYPE, -- The type of privilege (system or role)
            'GRANT ' || PRIVILEGE || ' TO ' || GRANTEE || ';' AS GRANT_SCRIPT
            FROM (
            -- System Privileges for user-created users
            SELECT
            GRANTEE,
            PRIVILEGE,
            'SYSTEM_PRIVILEGE' AS PRIV_TYPE
            FROM DBA_SYS_PRIVS
            WHERE GRANTEE IN (SELECT USERNAME FROM DBA_USERS WHERE ORACLE_MAINTAINED = 'N')
            AND grantee NOT in('GGDB2','TEST_SCHEMA')

            UNION ALL

            -- Role Privileges for user-created users
            SELECT
            GRANTEE,
            GRANTED_ROLE AS PRIVILEGE,
            'ROLE_PRIVILEGE' AS PRIV_TYPE
            FROM DBA_ROLE_PRIVS
            WHERE GRANTEE IN (SELECT USERNAME FROM DBA_USERS WHERE ORACLE_MAINTAINED = 'N')
            AND grantee NOT in('GGDB2','TEST_SCHEMA')
            )
            ORDER BY GRANTEE, PRIV_TYPE, PRIVILEGE
        </User_Privilages>
        <Storage_Objects_Grants>
            SELECT /*+ PARALLEL(4) */
            OWNER,
            GRANTEE,
            TABLE_NAME,
            PRIVILEGE,
            TYPE,
            'GRANT ' || PRIVILEGE || ' ON ' || OWNER || '.' || TABLE_NAME || ' TO ' || GRANTEE || ';' AS GRANT_STATEMENT
            FROM DBA_TAB_PRIVS
            WHERE NOT EXISTS (
            SELECT 1 FROM DBA_ROLES WHERE ROLE = DBA_TAB_PRIVS.GRANTEE
            )
            AND OWNER NOT IN ('ORDDATA','DVF','OLAPSYS',
            'SYSTEM', 'PUBLIC', 'SELECT_CATALOG_ROLE', 'GSMADMIN_INTERNAL', 'WMSYS', 'DBA',
            'AQ_ADMINISTRATOR_ROLE', 'WM_ADMIN_ROLE', 'PDB_DBA', 'SYS', 'SYSUMF_ROLE',
            'GSMUSER_ROLE', 'CTXSYS', 'XDB', 'LBACSYS', 'DVSYS', 'DV_SECANALYST',
            'ORACLE_OCM', 'DBSFWUSER', 'SYSRAC', 'IMP_FULL_DATABASE', 'AUDSYS',
            'GATHER_SYSTEM_STATISTICS', 'OPTIMIZER_PROCESSING_RATE', 'MDSYS', 'SYSBACKUP',
            'EXP_FULL_DATABASE', 'ORDSYS', 'DATAPATCH_ROLE', 'AVTUNE_PKG_ROLE',
            'EXECUTE_CATALOG_ROLE', 'APPQOSSYS', 'DGPDB_INT', 'AUDIT_ADMIN',
            'AUDIT_VIEWER', 'SYSKM', 'GSMCATUSER', 'DV_MONITOR', 'APPLICATION_TRACE_VIEWER',
            'CDB_DBA', 'DV_ACCTMGR', 'OLAP_XS_ADMIN', 'ACCHK_READ', 'ORDPLUGINS',
            'LOGSTDBY_ADMINISTRATOR', 'RECOVERY_CATALOG_OWNER', 'RECOVERY_CATALOG_USER',
            'RECOVERY_CATALOG_OWNER_VPD', 'GGSYS', 'DBFS_ROLE', 'GSMADMIN_ROLE',
            'GSM_POOLADMIN_ROLE', 'AQ_USER_ROLE', 'SYS$UMF', 'EM_EXPRESS_BASIC',
            'XS_CACHE_ADMIN', 'CAPTURE_ADMIN', 'PPLB_ROLE', 'XS_SESSION_ADMIN',
            'EM_EXPRESS_ALL', 'DBMS_MDX_INTERNAL', 'DBSNMP', 'OEM_MONITOR', 'SYSDG',
            'DATAPUMP_IMP_FULL_DATABASE', 'GSMROOTUSER_ROLE', 'GDS_CATALOG_SELECT',
            'BDSQL_ADMIN', 'BDSQL_USER', 'OUTLN', 'REMOTE_SCHEDULER_AGENT',
            'ADM_PARALLEL_EXECUTE_TASK', 'MAINTPLAN_APP', 'HS_ADMIN_SELECT_ROLE',
            'HS_ADMIN_EXECUTE_ROLE', 'XDBADMIN', 'SODA_APP', 'DATAPUMP_EXP_FULL_DATABASE',
            'CTXAPP', 'ORDADMIN', 'OLAP_USER', 'OLAP_DBA', 'LBAC_DBA', 'DIP',
            'DV_ADMIN', 'DV_AUDIT_CLEANUP', 'DV_POLICY_OWNER', 'DV_OWNER',
            'DV_STREAMS_ADMIN', 'DV_DATAPUMP_NETWORK_LINK'
            ) and upper(TYPE) not in ('PROCEDURE','FUNCTION','PACKAGE')
            ORDER BY OWNER, TABLE_NAME

        </Storage_Objects_Grants>
        <Code_Objects_Grants>
            SELECT /*+ PARALLEL(4) */
            OWNER,
            GRANTEE,
            TABLE_NAME,
            PRIVILEGE,
            TYPE,
            'GRANT ' || PRIVILEGE || ' ON ' || OWNER || '.' || TABLE_NAME || ' TO ' || GRANTEE || ';' AS GRANT_STATEMENT
            FROM DBA_TAB_PRIVS
            WHERE NOT EXISTS (
            SELECT 1 FROM DBA_ROLES WHERE ROLE = DBA_TAB_PRIVS.GRANTEE
            )
            AND OWNER NOT IN ('ORDDATA','DVF','OLAPSYS',
            'SYSTEM', 'PUBLIC', 'SELECT_CATALOG_ROLE', 'GSMADMIN_INTERNAL', 'WMSYS', 'DBA',
            'AQ_ADMINISTRATOR_ROLE', 'WM_ADMIN_ROLE', 'PDB_DBA', 'SYS', 'SYSUMF_ROLE',
            'GSMUSER_ROLE', 'CTXSYS', 'XDB', 'LBACSYS', 'DVSYS', 'DV_SECANALYST',
            'ORACLE_OCM', 'DBSFWUSER', 'SYSRAC', 'IMP_FULL_DATABASE', 'AUDSYS',
            'GATHER_SYSTEM_STATISTICS', 'OPTIMIZER_PROCESSING_RATE', 'MDSYS', 'SYSBACKUP',
            'EXP_FULL_DATABASE', 'ORDSYS', 'DATAPATCH_ROLE', 'AVTUNE_PKG_ROLE',
            'EXECUTE_CATALOG_ROLE', 'APPQOSSYS', 'DGPDB_INT', 'AUDIT_ADMIN',
            'AUDIT_VIEWER', 'SYSKM', 'GSMCATUSER', 'DV_MONITOR', 'APPLICATION_TRACE_VIEWER',
            'CDB_DBA', 'DV_ACCTMGR', 'OLAP_XS_ADMIN', 'ACCHK_READ', 'ORDPLUGINS',
            'LOGSTDBY_ADMINISTRATOR', 'RECOVERY_CATALOG_OWNER', 'RECOVERY_CATALOG_USER',
            'RECOVERY_CATALOG_OWNER_VPD', 'GGSYS', 'DBFS_ROLE', 'GSMADMIN_ROLE',
            'GSM_POOLADMIN_ROLE', 'AQ_USER_ROLE', 'SYS$UMF', 'EM_EXPRESS_BASIC',
            'XS_CACHE_ADMIN', 'CAPTURE_ADMIN', 'PPLB_ROLE', 'XS_SESSION_ADMIN',
            'EM_EXPRESS_ALL', 'DBMS_MDX_INTERNAL', 'DBSNMP', 'OEM_MONITOR', 'SYSDG',
            'DATAPUMP_IMP_FULL_DATABASE', 'GSMROOTUSER_ROLE', 'GDS_CATALOG_SELECT',
            'BDSQL_ADMIN', 'BDSQL_USER', 'OUTLN', 'REMOTE_SCHEDULER_AGENT',
            'ADM_PARALLEL_EXECUTE_TASK', 'MAINTPLAN_APP', 'HS_ADMIN_SELECT_ROLE',
            'HS_ADMIN_EXECUTE_ROLE', 'XDBADMIN', 'SODA_APP', 'DATAPUMP_EXP_FULL_DATABASE',
            'CTXAPP', 'ORDADMIN', 'OLAP_USER', 'OLAP_DBA', 'LBAC_DBA', 'DIP',
            'DV_ADMIN', 'DV_AUDIT_CLEANUP', 'DV_POLICY_OWNER', 'DV_OWNER',
            'DV_STREAMS_ADMIN', 'DV_DATAPUMP_NETWORK_LINK'
            ) and upper(TYPE) in ('PROCEDURE','FUNCTION','PACKAGE')
            ORDER BY OWNER, TABLE_NAME

        </Code_Objects_Grants>
        <Summary>
            SELECT
            OWNER,Grantor,GRANTEE,
            TYPE, Privilege ,
            COUNT(*) AS TOTAL_GRANTS
            FROM dba_tab_privs
            WHERE GRANTEE NOT IN ('ORDDATA','DVF','OLAPSYS',
            'SYSTEM', 'PUBLIC', 'SELECT_CATALOG_ROLE', 'GSMADMIN_INTERNAL', 'WMSYS', 'DBA',
            'AQ_ADMINISTRATOR_ROLE', 'WM_ADMIN_ROLE', 'PDB_DBA', 'SYS', 'SYSUMF_ROLE',
            'GSMUSER_ROLE', 'CTXSYS', 'XDB', 'LBACSYS', 'DVSYS', 'DV_SECANALYST',
            'ORACLE_OCM', 'DBSFWUSER', 'SYSRAC', 'IMP_FULL_DATABASE', 'AUDSYS',
            'GATHER_SYSTEM_STATISTICS', 'OPTIMIZER_PROCESSING_RATE', 'MDSYS', 'SYSBACKUP',
            'EXP_FULL_DATABASE', 'ORDSYS', 'DATAPATCH_ROLE', 'AVTUNE_PKG_ROLE',
            'EXECUTE_CATALOG_ROLE', 'APPQOSSYS', 'DGPDB_INT', 'AUDIT_ADMIN',
            'AUDIT_VIEWER', 'SYSKM', 'GSMCATUSER', 'DV_MONITOR', 'APPLICATION_TRACE_VIEWER',
            'CDB_DBA', 'DV_ACCTMGR', 'OLAP_XS_ADMIN', 'ACCHK_READ', 'ORDPLUGINS',
            'LOGSTDBY_ADMINISTRATOR', 'RECOVERY_CATALOG_OWNER', 'RECOVERY_CATALOG_USER',
            'RECOVERY_CATALOG_OWNER_VPD', 'GGSYS', 'DBFS_ROLE', 'GSMADMIN_ROLE',
            'GSM_POOLADMIN_ROLE', 'AQ_USER_ROLE', 'SYS$UMF', 'EM_EXPRESS_BASIC',
            'XS_CACHE_ADMIN', 'CAPTURE_ADMIN', 'PPLB_ROLE', 'XS_SESSION_ADMIN',
            'EM_EXPRESS_ALL', 'DBMS_MDX_INTERNAL', 'DBSNMP', 'OEM_MONITOR', 'SYSDG',
            'DATAPUMP_IMP_FULL_DATABASE', 'GSMROOTUSER_ROLE', 'GDS_CATALOG_SELECT',
            'BDSQL_ADMIN', 'BDSQL_USER', 'OUTLN', 'REMOTE_SCHEDULER_AGENT',
            'ADM_PARALLEL_EXECUTE_TASK', 'MAINTPLAN_APP', 'HS_ADMIN_SELECT_ROLE',
            'HS_ADMIN_EXECUTE_ROLE', 'XDBADMIN', 'SODA_APP', 'DATAPUMP_EXP_FULL_DATABASE',
            'CTXAPP', 'ORDADMIN', 'OLAP_USER', 'OLAP_DBA', 'LBAC_DBA', 'DIP',
            'DV_ADMIN', 'DV_AUDIT_CLEANUP', 'DV_POLICY_OWNER', 'DV_OWNER',
            'DV_STREAMS_ADMIN', 'DV_DATAPUMP_NETWORK_LINK'
            )
            AND OWNER NOT IN (
            'SYSTEM', 'PUBLIC', 'SELECT_CATALOG_ROLE', 'GSMADMIN_INTERNAL', 'WMSYS', 'DBA',
            'AQ_ADMINISTRATOR_ROLE', 'WM_ADMIN_ROLE', 'PDB_DBA', 'SYS', 'SYSUMF_ROLE',
            'GSMUSER_ROLE', 'CTXSYS', 'XDB', 'LBACSYS', 'DVSYS', 'DV_SECANALYST',
            'ORACLE_OCM', 'DBSFWUSER', 'SYSRAC', 'IMP_FULL_DATABASE', 'AUDSYS',
            'GATHER_SYSTEM_STATISTICS', 'OPTIMIZER_PROCESSING_RATE', 'MDSYS', 'SYSBACKUP',
            'EXP_FULL_DATABASE', 'ORDSYS', 'DATAPATCH_ROLE', 'AVTUNE_PKG_ROLE',
            'EXECUTE_CATALOG_ROLE', 'APPQOSSYS', 'DGPDB_INT', 'AUDIT_ADMIN',
            'AUDIT_VIEWER', 'SYSKM', 'GSMCATUSER', 'DV_MONITOR', 'APPLICATION_TRACE_VIEWER',
            'CDB_DBA', 'DV_ACCTMGR', 'OLAP_XS_ADMIN', 'ACCHK_READ', 'ORDPLUGINS',
            'LOGSTDBY_ADMINISTRATOR', 'RECOVERY_CATALOG_OWNER', 'RECOVERY_CATALOG_USER',
            'RECOVERY_CATALOG_OWNER_VPD', 'GGSYS', 'DBFS_ROLE', 'GSMADMIN_ROLE',
            'GSM_POOLADMIN_ROLE', 'AQ_USER_ROLE', 'SYS$UMF', 'EM_EXPRESS_BASIC',
            'XS_CACHE_ADMIN', 'CAPTURE_ADMIN', 'PPLB_ROLE', 'XS_SESSION_ADMIN',
            'EM_EXPRESS_ALL', 'DBMS_MDX_INTERNAL', 'DBSNMP', 'OEM_MONITOR', 'SYSDG',
            'DATAPUMP_IMP_FULL_DATABASE', 'GSMROOTUSER_ROLE', 'GDS_CATALOG_SELECT',
            'BDSQL_ADMIN', 'BDSQL_USER', 'OUTLN', 'REMOTE_SCHEDULER_AGENT',
            'ADM_PARALLEL_EXECUTE_TASK', 'MAINTPLAN_APP', 'HS_ADMIN_SELECT_ROLE',
            'HS_ADMIN_EXECUTE_ROLE', 'XDBADMIN', 'SODA_APP', 'DATAPUMP_EXP_FULL_DATABASE',
            'CTXAPP', 'ORDADMIN', 'OLAP_USER', 'OLAP_DBA', 'LBAC_DBA', 'DIP',
            'DV_ADMIN', 'DV_AUDIT_CLEANUP', 'DV_POLICY_OWNER', 'DV_OWNER',
            'DV_STREAMS_ADMIN', 'DV_DATAPUMP_NETWORK_LINK'
            )
            GROUP BY OWNER,Grantor,GRANTEE, TYPE,Privilege
            ORDER BY OWNER,Grantor,GRANTEE, TYPE,Privilege
        </Summary>
    </User_Roles_Permissions>
</Queries>