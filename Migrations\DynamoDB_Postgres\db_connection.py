import psycopg2, boto3

def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def target_DB_connection(db_data):
    try:
        connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                      host=db_data['host'], database=db_data['db_name'],
                                      port=db_data['port'])
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near target database connection", e)
    return connection,error


def DB_connection(database_data):
    host_name = database_data['host']
    access_key = database_data['name']
    secret_access_key = database_data['password']
    try:
        session = boto3.Session(
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_access_key,
            region_name=database_data['port']
        )
        dynamo_resource = session.resource(database_data['service_name'], endpoint_url=host_name)
        dynamo_client = session.client(database_data['service_name'], endpoint_url=host_name)
        error = ''
    except Exception as e:
        dynamo_resource = None
        dynamo_client = None
        error = str(e)
        print("Issue found near source database connection", e)
    return dynamo_resource,dynamo_client,error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except psycopg2.DatabaseError as e:
        print("Issue found near target database query", e)
        data = None
    except Exception as e:
        print("Issue found near source database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
