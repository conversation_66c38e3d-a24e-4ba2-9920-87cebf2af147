import psycopg2, cx_Oracle

def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def target_DB_connection(db_data):
    try:
        connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                      host=db_data['host'], database=db_data['db_name'],
                                      port=db_data['port'])
        error = ''
    except psycopg2.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near target database connection", e)
    return connection,error


def DB_connection(database_data):
    host_name = database_data['host']
    port = database_data['port']
    service_name = database_data['service_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        dsn = cx_Oracle.makedsn(host=host_name, port=port, service_name=service_name)
        connection = cx_Oracle.Connection(user=user_name, password=password, dsn=dsn)
        error = ''
    except cx_Oracle.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near source database connection", e)
    return connection,error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        if cursor.description:
            data = cursor.fetchall()
            data = [[str(value) if isinstance(value, cx_Oracle.LOB) else value for value in row] for row in data]
        else:
            data = []
    except cx_Oracle.DatabaseError as e:
        print("Issue found near source database query", e)
        data = None
    except psycopg2.DatabaseError as e:
        print("Issue found near target database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
