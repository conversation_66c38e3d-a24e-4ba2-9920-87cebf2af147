import psycopg2, boto3
from azure.cosmos import CosmosClient, exceptions, PartitionKey


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def target_DB_connection(db_data):
    uri = db_data['name']
    key = db_data['password']
    try:
        client_conn = CosmosClient(uri, key, connection_verify=False)
        error = ''
    except exceptions.CosmosHttpResponseError as e:
        client_conn = None
        error = str(e.message)
        print("Issue found near target database connection", str(e.message))
    return client_conn, error


def DB_connection(database_data):
    host_name = database_data['host']
    access_key = database_data['name']
    secret_access_key = database_data['password']
    try:
        session = boto3.Session(
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_access_key,
            region_name=database_data['port']
        )
        dynamo_resource = session.resource(database_data['service_name'], endpoint_url=host_name)
        dynamo_client = session.client(database_data['service_name'], endpoint_url=host_name)
        error = ''
    except Exception as e:
        dynamo_resource = None
        dynamo_client = None
        error = str(e)
        print("Issue found near source database connection", e)
    return dynamo_resource, dynamo_client, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except exceptions.CosmosHttpResponseError as e:
        print("Issue found near target database query", str(e.message))
        data = None
    except Exception as e:
        print("Issue found near source database query", str(e))
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
