import re, subprocess
from openpyxl.utils import get_column_letter
from openpyxl import load_workbook


def adjust_column_width(excel_path):
    workbook = load_workbook(excel_path)
    for sheet_name in workbook.sheetnames:
        worksheet = workbook[sheet_name]
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)  # Get the column name
            header = str(worksheet.cell(row=1, column=column[0].column).value)

            header_length = len(header)
            max_length = max(max_length, header_length)

            for cell in column:
                try:
                    if cell.value is not None:
                        max_length = max(max_length, len(str(cell.value)))
                except:
                    pass
            if header not in ['Statement', 'Object_Estimate_Expression']:
                adjusted_width = max_length + 2  # Add some padding to the width
            else:
                adjusted_width = 30
            worksheet.column_dimensions[column_letter].width = adjusted_width

    workbook.save(excel_path)


class DualStream:
    def __init__(self, file_obj, stream):
        self.file = file_obj
        self.stream = stream

    def write(self, message):
        self.stream.write(message)
        self.file.write(message)
        self.file.flush()

    def flush(self):
        self.stream.flush()
        self.file.flush()


def pg_formatter(data):
    try:
        pg_format_path = 'pg_format'
        result = subprocess.run(
            [pg_format_path],
            input=data,
            capture_output=True,
            text=True
        )
        if result.returncode != 0:
            print(f"Error occurred at PG_Formatter: {result.stderr}")
            return data
        return result.stdout
    except Exception as e:
        print(f"Error occurred at PG_Formatter: {e}")
        return data


def deploy_object(connection,converted_data):
    cursor = connection.cursor()
    try:
        cursor.execute(converted_data)
        connection.commit()
        error = ''
    except Exception as err:
        error = str(err)
    finally:
        cursor.close()
    return error


def deploy_file(command,output_file_path):
    error = ''
    try:
        command += f' > {output_file_path} 2>&1'
        subprocess.run(command, shell=True)
    except Exception as e:
        error = str(e)
    return error

def drop_table(connection, query):
    cursor = connection.cursor()
    try:
        cursor.execute(query)
        print('Object dropped successfully')
    except Exception as err:
        print("error", err)
    finally:
        connection.commit()
        cursor.close()


def split_parts(source_string):
    parts = []
    bracket_level = 0
    current = []
    for c in (source_string + ","):
        if c == "," and bracket_level == 0:
            parts.append("".join(current))
            current = []
        else:
            if c == "(":
                bracket_level += 1
            elif c == ")":
                bracket_level -= 1
            current.append(c)
    return parts


def sql_server_semicolon_identification(source_data):
    # print(source_data, '------------------1------------------')
    source_data = source_data.replace('\t', ' ').replace('\n', ' ').replace('(', '( ')
    source_data = re.sub(r' +', ' ', source_data)

    ignore_identifiers = ['int', 'bigint', 'smallint', 'numeric', 'date', 'datetime', 'text', 'char',
                          'varchar', 'zone', 'bytea', 'as', 'begin', 'loop', 'table', 'else', '(']

    three_identifier_words = ['declare', 'set', 'open', 'update', 'insert', 'raise', 'delete', 'fetch', 'truncate',
                              'return', 'end loop;', 'if', 'while', 'drop table']
    for identifier in three_identifier_words:
        if re.search(rf'\b{identifier}\b', source_data, flags=re.IGNORECASE):
            before_words = re.findall(rf'[\w.(.*?)]+\s\b{identifier}\b', source_data,
                                      flags=re.DOTALL | re.IGNORECASE)
            before_words = list(set(before_words))
            for word in before_words:
                before_word = re.sub(rf'\b{identifier}\b', '', word, flags=re.IGNORECASE)
                if identifier.lower().strip() in ['set', 'declare']:
                    ignore_identifiers = ['as', 'begin', 'loop', 'table', 'else', '(']
                else:
                    ignore_identifiers = ignore_identifiers
                if before_word.strip().lower() not in ignore_identifiers:
                    if ' ' in identifier:
                        identifier_format = identifier.replace(' ', '\s')
                    else:
                        identifier_format = identifier
                    modified_word = re.sub(rf'\b{identifier_format}\b', ';\n' + identifier, word,
                                           flags=re.IGNORECASE)
                    modified_word = modified_word.replace(' ;', ';')

                    if ' ' in word:
                        word = word.replace(' ', '\s')
                    if ')' in word:
                        word = word.replace(')', '\)')
                    source_data = re.sub(rf'{word.strip()}\b', modified_word, source_data,
                                         flags=re.IGNORECASE | re.DOTALL)
                else:
                    if ' ' in identifier:
                        identifier_format = identifier.replace(' ', '\s')
                    else:
                        identifier_format = identifier
                    modified_word = re.sub(rf'\b{identifier_format}\b', '\n' + identifier, word,
                                           flags=re.IGNORECASE)
                    if ' ' in word:
                        word = word.replace(' ', '\s')
                    if ')' in word:
                        word = word.replace(')', '\)')
                    source_data = re.sub(rf'{word.strip()}\b', modified_word, source_data,
                                         flags=re.IGNORECASE | re.DOTALL)
    # Handling semicolon between update and set
    source_data = re.sub(r' +', ' ', source_data)
    update_set_data = re.findall(r'\bupdate\b.*?\bset\b', source_data, flags=re.IGNORECASE | re.DOTALL)
    for update_sentence in update_set_data:
        modified_sentence = update_sentence.replace(';', '')
        source_data = source_data.replace(update_sentence, modified_sentence)

    # select keyword handling
    source_data = source_data.replace('(', ' (')
    source_data = re.sub(r' +', ' ', source_data)
    if re.search(r'\bselect\b', source_data, flags=re.IGNORECASE):
        before_words = re.findall(r'[\w.(.*?)]+\s\bselect\b', source_data, flags=re.DOTALL | re.IGNORECASE)
        before_words = list(set(before_words))
        for word in before_words:
            before_word = re.sub(r'\bselect\b', '', word, flags=re.IGNORECASE)
            if before_word.strip().lower() not in ['as', 'begin', 'loop', 'union', 'all', '(', 'for']:
                modified_word = re.sub(r'\bselect\b', ';\nselect', word, flags=re.IGNORECASE)
                modified_word = modified_word.replace(' ;', ';')
                if ' ' in word:
                    word = word.replace(' ', '\s')
                if ')' in word:
                    word = word.replace(')', '\)')
                source_data = re.sub(rf'{word.strip()}\b', modified_word, source_data,
                                     flags=re.IGNORECASE | re.DOTALL)
            else:
                modified_word = re.sub(r'\bselect\b', '\nselect', word, flags=re.IGNORECASE)
                if '(' in word:
                    word = word.replace('(', '\(')
                source_data = re.sub(rf'{word.strip()}\b', modified_word, source_data,
                                     flags=re.IGNORECASE | re.DOTALL)

    # Handling semicolon between insert and select
    insert_next_word = re.findall(r'insert.*?\);\nselect', source_data, flags=re.IGNORECASE | re.DOTALL)
    for insert_statement in insert_next_word:
        if not re.search(r'\bvalues\b', insert_statement, flags=re.IGNORECASE):
            modified_insert_sentence = insert_statement.replace(';', '')
            source_data = source_data.replace(insert_statement, modified_insert_sentence)

    # Declare to semicolon handling
    declare_block = re.findall(r'\bdeclare\b(.*?);', source_data, flags=re.DOTALL | re.IGNORECASE)
    if declare_block:
        for declare_statement in declare_block:

            comma_split = split_parts(declare_statement)
            if comma_split:
                comma_split = [i.strip() for i in comma_split if i != '']
                new_declare_statement = ';\n'.join(comma_split)
                source_data = source_data.replace(declare_statement, ' ' + new_declare_statement)

    source_data = re.sub(r'\bend\b', ';\nend', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bend\sloop', '\nend loop;', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bend\b', 'end;', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = source_data.replace(';;', ';')
    source_data = re.sub(r'\bend;\sloop', 'end loop;', source_data, flags=re.IGNORECASE | re.DOTALL)

    source_data = re.sub(r'\bthan\b\s;', 'than;', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bthan;', 'than;', source_data, flags=re.IGNORECASE | re.DOTALL)

    drop_table_data = re.findall(r'\bdrop\stable\b\s[\w.(.*?)]+', source_data, flags=re.IGNORECASE | re.DOTALL)
    for word in drop_table_data:
        if not re.search(r'\bif\b', word, flags=re.IGNORECASE | re.DOTALL):
            source_data = source_data.replace(word, word + ' ;')
    source_data = source_data.replace(';;', ';')

    source_data = source_data.replace(') ;', ');').replace('; )', ';)')
    source_data = re.sub(r';\send', ';end', source_data, flags=re.DOTALL | re.IGNORECASE)
    source_data = re.sub(r';\nend', ';end', source_data, flags=re.DOTALL | re.IGNORECASE)
    source_data = re.sub(r'end\s;', 'end;', source_data, flags=re.DOTALL | re.IGNORECASE)
    source_data = re.sub(r'\);end;\)', ') \n end )', source_data, flags=re.DOTALL | re.IGNORECASE)

    source_data = re.sub(r'\bbegin\b', '\nbegin', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bset\b', '\nset', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bend\b', '\nend', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\belse\b', '\nelse', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bend\sloop', '\nend loop', source_data, flags=re.IGNORECASE | re.DOTALL)

    # Case When to end handling
    case_when_data = re.findall(r'case\swhen.*?end;', source_data, flags=re.IGNORECASE | re.DOTALL)
    for case_when_sentence in case_when_data:
        modified_case_when_sentence = case_when_sentence.replace(';', '')
        source_data = source_data.replace(case_when_sentence, modified_case_when_sentence)

    source_data = "".join([s for s in source_data.strip().splitlines(True) if s.strip()])
    source_data = source_data.replace('; ;', ';;').replace(';;', ';')
    source_data = re.sub(r'\bcreate\b', '\n\nCREATE', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r' +', ' ', source_data)

    begin_comment_select_matches = re.findall(r'\bBegin\b\s*comment_quad_marker_.*?_us\b\s*\;\s*select\b', source_data, re.DOTALL | re.IGNORECASE)
    for match in begin_comment_select_matches:
        updated_match = match.replace(';', '\n')
        source_data = source_data.replace(match, updated_match)

    comment_marker_statements = re.findall(r';\s*comment_quad_marker_.*?_us\s*;', source_data,flags=re.IGNORECASE | re.DOTALL)
    for marker in comment_marker_statements:
        cleaned_marker = marker.replace(';', '')
        source_data = source_data.replace(marker, ';' + cleaned_marker)
    return source_data


def create_sub_objects(source_data, object_path, rules_data, objects_data):
    object_path_rules = rules_data[(rules_data['Object_Path'].str.contains(pat=object_path)) & (
        rules_data['Object_Path'].apply(lambda x: len(x.split('/')) == len(object_path.strip().split('/'))))]

    object_path_data = objects_data[(objects_data['Object_Id'].str.contains(pat=object_path)) & (
        objects_data['Object_Id'].apply(lambda x: len(x.split('/')) == len(object_path.strip().split('/'))))]

    if not object_path_data.empty:
        if object_path_data.iloc[0]['Object_Category'] == 'Group':
            group_rules_data = rules_data[
                rules_data['Object_Path'].isin(objects_data[(objects_data['Object_Category'] == 'Group')]['Object_Id'])]
        else:
            group_rules_data = rules_data[rules_data['Object_Path'].isin(object_path_data['Object_Id'])]

        objects_identifier_set = set()
        if not group_rules_data.empty:
            for object_identifier in group_rules_data.iloc[:, 1]:
                if isinstance(object_identifier, str):
                    object_identifier_list = object_identifier.split('&')
                    for element in object_identifier_list:
                        start_key = re.sub(' +', ' ', element.lower().split('end:')[0].split('start:')[1].strip())
                        objects_identifier_set.add(start_key.lower())

    object_tuple_list =[]
    source_data = re.sub(r'\bas\b', '\nas', source_data, flags=re.I)
    source_data = re.sub(r'\bis\b', '\nis', source_data, flags=re.I)

    source_data_list = source_data.split('\n')

    for rules_index, rules_record in object_path_rules.iterrows():
        index_dictionary_list = []
        object_identifier = re.sub(' +', ' ', rules_record[1].lower())
        object_identifier_list = [i.strip() for i in object_identifier.split('&') if i != '']

        for element in object_identifier_list:
            index_dictionary = {}
            start_key = re.sub(' +', ' ', element.split('end:')[0].split('start:')[1].strip())
            end_key = re.sub(' +', ' ', element.split('end:')[1].strip())
            if '|' in start_key:
                start_index = next(
                    (i for i, line in enumerate(source_data_list) for word in start_key.split('|')
                     if re.search(rf'\\b{word}\\b', line.strip().lower())),
                    None
                )
                if start_index is not None:
                    if end_key.strip() != 'object-1':
                        end_index = next(
                            (index for index, data in enumerate(source_data_list)
                             if end_key.strip().lower() in data.strip().lower() and index > start_index),
                            None
                        )

                    else:
                        end_index = next(
                            (index - 1 for index, data1 in enumerate(source_data_list)
                             for element in list(objects_identifier_set)
                             if re.sub(' +', ' ',
                                       element).strip().lower() in data1.strip().lower() and index > start_index),
                            None
                        )

                    if end_index is not None and end_index > start_index:
                        index_dictionary[start_index] = end_index
                    else:
                        index_dictionary[start_index] = len(source_data_list) - 1

            elif ';+1'in start_key:
                for index, data in enumerate(source_data_list):
                    if ';' in data.strip().lower():

                        if end_key.strip() != 'object-1':
                            end_index = next((i for i in range(index + 1, len(source_data_list))
                                              if end_key.strip().lower() in source_data_list[i].strip().lower()), None)

                        else:
                            end_index = next((
                                index1 for index1, data1 in enumerate(source_data_list[index:], start=index)
                                if any(element in data1.strip().lower() for element in list(objects_identifier_set))),
                                None)
                            end_index = end_index - 1

                        if 'create or replace' not in source_data_list[index].strip().lower() and end_index is not None:
                            index_dictionary[index + 1] = end_index
                        else:
                            index_dictionary[index + 1] = len(source_data_list) - 1
            else:
                for index, data in enumerate(source_data_list):
                    if start_key.strip().lower() in data.strip().lower():
                        start_index = index
                        end_search_list = []
                        if end_key.strip() == 'object-1':
                            end_index = next(
                                index1 for index1, data1 in enumerate(source_data_list[start_index:], start=start_index)
                                if any(element in data1.strip().lower() for element in list(objects_identifier_set))
                            ) - 1
                            end_search_list.append(end_index)

                        elif '|' in end_key:
                            check_index_list = [
                                index for index, data in enumerate(source_data_list)
                                if any(re.search(rf'\b{word}\b', data.strip().lower()) for word in
                                       end_key.strip().split('|'))
                            ]
                            end_index = next((
                                index for index in check_index_list if index > start_index), None)

                            # if 'is|as' == end_key or 'as|is' == end_key:
                            #     end_index -= 1
                            if end_index >= start_index:
                                end_search_list.append(end_index)
                        else:
                            end_index = next((
                                index for index, data in enumerate(source_data_list)
                                if end_key.strip().lower() in data.strip().lower() and index > start_index
                            ),None)

                            end_search_list.append(end_index)

                        if end_search_list:
                            index_dictionary[start_index] = min(end_search_list)
                        else:
                            index_dictionary[start_index] = len(source_data_list) - 1

            index_dictionary_list.append(index_dictionary)

        set_index_dictionary_list = [
            {key: dict_i[key]}
            for dict_i in index_dictionary_list if len(dict_i) > 0
            for key in dict_i
        ]
        set_index_dictionary_list = list({frozenset(d.items()): d for d in set_index_dictionary_list}.values())
        index_dictionary_list = sorted(set_index_dictionary_list, key=lambda d: list(d.keys()))

        start_index_list = [key for dict_i in index_dictionary_list for key in dict_i]
        end_index_list = [value for dict_i in index_dictionary_list for value in dict_i.values()]

        if len(set(end_index_list)) == 1:
            index_dictionary_list = [{min(start_index_list): end_index_list[0]}]

        object_name = rules_record[4].split('/')[-1]
        for dict in index_dictionary_list:
            for key, value in dict.items():
                if key and value:
                    tuple_defination = '\n'.join([source_data_list[i] for i in range(key, value + 1)])
                    created_tuple = (object_name, str(tuple_defination))
                    object_tuple_list.append(created_tuple)
    return object_tuple_list
