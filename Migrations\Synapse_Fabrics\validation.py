import os, sys, shutil
import pandas as pd
import requests
from datetime import datetime
from msal import ConfidentialClientApplication
from import_file import import_file
from azure.identity import ClientSecretCredential
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.common_functions import adjust_column_width
from common_modules.stored_procedures import request_insert, request_update
import msal

def pipeline_validation_trigger(task_name, project_id, migration_name, iteration_id, object_category, schema_name, target_schema,
                       source_connection_id, target_connection_id, cloud_category):

    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    print(f'Performing {task_name} process')
    config_path = local_root_path + '/' + 'config.py'

    root_path = ''  
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')
        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        os.makedirs(working_directory_path, exist_ok=True)
    else:
        raise FileNotFoundError(f"config.py not found at {config_path}")

    # Load db_connection.py
    db_module_path = os.path.join(local_root_path, 'Migrations', migration_name, 'db_connection.py')
    if os.path.isfile(db_module_path):
        sys.path.append(db_module_path)
        import_object = import_file(db_module_path)

    db_object = import_file(db_module_path)
    connect_database = getattr(db_object, 'connect_database')

    token_data = api_authentication()
    source_DB_details = decrypt_database_details(token_data, project_id, 'Source', source_connection_id)
    tenant_id = source_DB_details.get('host')
    client_id = source_DB_details.get('port')
    client_secret = source_DB_details.get('password')
    synapse_workspace_name = source_DB_details.get('service_name')
    credential = ClientSecretCredential(tenant_id, client_id, client_secret)

    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)
    workspace_id = target_DB_details.get('db_name')
    synapse_base_url = f"https://{synapse_workspace_name}.dev.azuresynapse.net"

    def get_fabric_connections():
        app = msal.ConfidentialClientApplication(
            client_id=client_id,
            client_credential=client_secret,
            authority=f"https://login.microsoftonline.com/{tenant_id}"
        )
        token = app.acquire_token_for_client(scopes=["https://api.fabric.microsoft.com/.default"]).get("access_token")
        if not token:
            raise Exception("Failed to acquire Fabric token")
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get("https://api.fabric.microsoft.com/v1/connections", headers=headers)
        response.raise_for_status()
        return {conn["displayName"].lower(): conn for conn in response.json().get("value", [])}

    request_id = ''
    try:
        project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
        project_conn = connect_database(project_DB_details)
        request_id = request_insert(project_conn, None, source_connection_id, task_name, 'Pipeline',
                                    schema_name, 'All')[0]

        # -- Synapse Pipelines (Source)
        syn_token = credential.get_token("https://dev.azuresynapse.net/.default").token
        syn_headers = {"Authorization": f"Bearer {syn_token}"}
        syn_url = f"{synapse_base_url}/pipelines?api-version=2020-12-01"
        syn_response = requests.get(syn_url, headers=syn_headers)
        syn_response.raise_for_status()
        synapse_pipelines = {p['name'] for p in syn_response.json().get('value', [])}

        # -- Fabric Pipelines (Target)
        app = ConfidentialClientApplication(
            client_id=client_id,
            client_credential=client_secret,
            authority=f"https://login.microsoftonline.com/{tenant_id}"
        )
        result = app.acquire_token_for_client(scopes=["https://api.fabric.microsoft.com/.default"])
        fabric_token = result["access_token"]
        fabric_headers = {"Authorization": f"Bearer {fabric_token}"}
        fabric_url = f"https://api.fabric.microsoft.com/v1/workspaces/{workspace_id}/items"
        fabric_response = requests.get(fabric_url, headers=fabric_headers)
        fabric_response.raise_for_status()
        fabric_pipelines = {item['displayName'] for item in fabric_response.json().get('value', []) if item['type'] == 'DataPipeline'}

        # Compare pipelines
        matched = synapse_pipelines & fabric_pipelines
        source_only = synapse_pipelines - fabric_pipelines
        target_only = fabric_pipelines - synapse_pipelines

        result = []
        for name in matched:
            result.append((name, name, "Available in both Source and Target"))
        for name in source_only:
            result.append((name, '', "Available in Source only"))
        for name in target_only:
            result.append(('', name, "Available in Target only"))

        result_df = pd.DataFrame(result, columns=['Source_Pipeline', 'Target_Pipeline', 'Status'])

        # Save to Excel
        project_folder = f"PRJ{project_id}SRC"
        validation_reports_folder = root_path + '/' + project_folder + '/' + 'Reports' + '/' + 'Validation'
        if not os.path.exists(validation_reports_folder):
            os.makedirs(validation_reports_folder)

        timestamp = datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss')
        excel_name = f"{synapse_workspace_name}_Pipeline_Validation_{timestamp}.xlsx"
        excel_path = os.path.join(validation_reports_folder, excel_name)

        with pd.ExcelWriter(excel_path, engine="openpyxl") as writer:
            result_df.to_excel(writer, sheet_name="Pipeline_Validation", index=False)
            summary_data = {
                'Category': ['Matched', 'Only in Source', 'Only in Target'],
                'Count': [len(matched), len(source_only), len(target_only)]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name="Summary", index=False)

        adjust_column_width(excel_path)

        print(f"Pipeline validation written to: {excel_path}")
        request_update(project_conn, request_id, 'Completed', None)

    except Exception as e:
        print(f"Error during pipeline validation: {e}")
        if request_id:
            request_update(project_conn, request_id, 'Error', str(e))
