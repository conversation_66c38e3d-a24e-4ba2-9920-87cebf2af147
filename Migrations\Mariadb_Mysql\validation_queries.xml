<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
                <!--                <Sequence>-->
                <!--&lt;!&ndash;                    SELECT&ndash;&gt;-->
                <!--&lt;!&ndash;                    schema_name = SCHEMA_NAME(iss.schema_id),&ndash;&gt;-->
                <!--&lt;!&ndash;                    sequence_name = ss.name,&ndash;&gt;-->
                <!--&lt;!&ndash;                    current_value = cast(ss.current_value as int)&ndash;&gt;-->
                <!--&lt;!&ndash;                    FROM&ndash;&gt;-->
                <!--&lt;!&ndash;                    sys.sequences AS ss&ndash;&gt;-->
                <!--&lt;!&ndash;                    JOIN sys.schemas AS iss ON&ndash;&gt;-->
                <!--&lt;!&ndash;                    ss.schema_id = iss.schema_id&ndash;&gt;-->
                <!--&lt;!&ndash;                    where&ndash;&gt;-->
                <!--&lt;!&ndash;                    lower(iss.name) = lower('@schemaname')&ndash;&gt;-->
                <!--&lt;!&ndash;                    ORDER BY&ndash;&gt;-->
                <!--&lt;!&ndash;                    schema_name,&ndash;&gt;-->
                <!--&lt;!&ndash;                    sequence_name;&ndash;&gt;-->
                <!--                </Sequence>-->
                <Table>
                    select
                        TABLE_SCHEMA,
                        TABLE_NAME,
                        TABLE_TYPE
                    from
                        information_schema.tables t
                    where
                        TABLE_TYPE = 'BASE TABLE'
                        and table_schema = '@schemaname'
                        and not exists (
                        select
                            1
                        from
                            information_schema.partitions p
                        where
                            TABLE_SCHEMA = '@schemaname'
                            and PARTITION_NAME is not null
                            and p.table_name = t.TABLE_NAME);
                </Table>
                <Partition>
                    select
                    distinct TABLE_SCHEMA,
                    TABLE_NAME,
                    PARTITION_NAME
                    from
                    information_schema.partitions p
                    where
                    PARTITION_NAME is not null
                    and table_schema = '@schemaname'
                </Partition>
                <Primary_Key>
                    select TABLE_SCHEMA as OWNER,TABLE_NAME,CONSTRAINT_NAME,GROUP_CONCAT(COLUMN_NAME ORDER BY
                    ORDINAL_POSITION SEPARATOR ', ') col_list,
                    'Enabled' as status
                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    where
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    and constraint_name = 'PRIMARY'
                    GROUP BY
                    TABLE_NAME, CONSTRAINT_NAME;

                </Primary_Key>
                <Unique_Constraint>
                    select TC.TABLE_SCHEMA,TC.TABLE_NAME,TC.CONSTRAINT_NAME,KCU.COLUMN_NAME,'ENABLED' as status
                    FROM
                    information_schema.TABLE_CONSTRAINTS TC
                    JOIN
                    information_schema.KEY_COLUMN_USAGE KCU
                    ON TC.CONSTRAINT_NAME = KCU.CONSTRAINT_NAME
                    AND TC.TABLE_NAME = KCU.TABLE_NAME
                    AND TC.TABLE_SCHEMA = KCU.TABLE_SCHEMA
                    WHERE
                    TC.CONSTRAINT_TYPE = 'UNIQUE'
                    AND TC.TABLE_SCHEMA = '@schemaname'
                    GROUP BY
                    TC.TABLE_SCHEMA, TC.TABLE_NAME, TC.CONSTRAINT_NAME
                    ORDER BY
                    TC.TABLE_NAME, TC.CONSTRAINT_NAME;
                </Unique_Constraint>
                <Foreign_Key>
                    select TABLE_SCHEMA,TABLE_NAME,CONSTRAINT_NAME,
                    CONCAT(' FOREIGN KEY (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION SEPARATOR ', '),
                    ') REFERENCES ',
                    REFERENCED_TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(REFERENCED_COLUMN_NAME ORDER BY POSITION_IN_UNIQUE_CONSTRAINT SEPARATOR ', '),
                    ');'
                    ) AS ForeignKey,'Enabled' as status

                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                    GROUP BY
                    TABLE_NAME, CONSTRAINT_NAME;
                </Foreign_Key>
                <Not_Null_Constraint>
                    select
                    c.TABLE_SCHEMA,
                    c.TABLE_NAME,
                    c.COLUMN_NAME
                    from
                    information_schema.columns c
                    inner join information_schema.tables t on
                    c.TABLE_NAME = t.TABLE_NAME
                    and c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    where
                    UPPER(c.TABLE_SCHEMA) = UPPER('@schemaname')
                    and t.TABLE_TYPE = 'BASE TABLE'
                    and c.IS_NULLABLE = 'NO'
                    order by
                    c.TABLE_NAME,
                    c.COLUMN_NAME;
                </Not_Null_Constraint>
                <Default_Constraint>
                    select
                    c.TABLE_SCHEMA,
                    c.TABLE_NAME,
                    c.COLUMN_NAME,
                    c.COLUMN_DEFAULT
                    from
                    information_schema.columns c
                    join information_schema.tables t on
                    c.TABLE_NAME = t.TABLE_NAME
                    and c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    where
                    UPPER(c.TABLE_SCHEMA) = UPPER('@schemaname')
                    and t.TABLE_TYPE = 'BASE TABLE'
                    and COLUMN_DEFAULT is not null
                    order by
                    TABLE_NAME,
                    COLUMN_NAME;
                </Default_Constraint>
                <Check_Constraint>
                    select CONSTRAINT_SCHEMA,TABLE_NAME,CONSTRAINT_NAME,CHECK_CLAUSE, 'ENABLED' as status
                    from
                    information_schema.CHECK_CONSTRAINTS
                    where
                    UPPER(CONSTRAINT_SCHEMA) = UPPER('@schemaname')
                    order by
                    CONSTRAINT_SCHEMA,
                    TABLE_NAME;
                </Check_Constraint>
                <Index>
                    select TABLE_SCHEMA,TABLE_NAME,INDEX_NAME,
                    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX SEPARATOR ', ') index_cols,
                    IF(NON_UNIQUE = 0, 'UNIQUE ', '') uniqueness,
                    INDEX_TYPE,
                    CONCAT(
                    'CREATE ',
                    IF(NON_UNIQUE = 0, 'UNIQUE ', ''),
                    'INDEX ',
                    INDEX_NAME,
                    ' ON ',
                    TABLE_SCHEMA,
                    '.',
                    TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX SEPARATOR ', '),
                    ');'
                    ) AS IDX_DEF,
                    concat(TABLE_SCHEMA,',',TABLE_NAME,'-',INDEX_NAME) idx_concat
                    FROM
                    information_schema.STATISTICS
                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    and
                    INDEX_NAME != 'PRIMARY'
                    GROUP BY
                    INDEX_NAME, TABLE_NAME, NON_UNIQUE, INDEX_TYPE;

                </Index>
                <!--                <Synonym>-->
                <!--                    SELECT /*+ PARALLEL(@degree) */ viewname-->
                <!--                    FROM pg_views-->
                <!--                    WHERE schemaname = lower('@schemaname')-->
                <!--                    order by viewname-->
                <!--                </Synonym>-->
                <View>
                    select TABLE_SCHEMA,
                    table_name as View_name
                    from
                    information_schema.VIEWS
                    where
                    TABLE_SCHEMA = '@schemaname';
                </View>
                <Datatype>
                    select
                    c.TABLE_SCHEMA as Owner,
                    c.TABLE_NAME,
                    c.COLUMN_NAME,
                    c.DATA_TYPE,
                    case
                    when DATA_TYPE in ('int', 'bigint', 'decimal', 'tinyint')
                    then concat(cast(NUMERIC_PRECISION as char), ',',
                    cast(NUMERIC_SCALE as CHAR))
                    when DATA_TYPE in ('varchar', 'char', 'text' )
                    then CHARACTER_MAXIMUM_LENGTH
                    when DATA_TYPE in ('double') then cast(NUMERIC_PRECISION as char)
                    end as "COLUMN_SIZE" ,
                    ORDINAL_POSITION as COLUMN_ID,
                    'Table' as object_type
                    from
                    information_schema.columns c
                    inner join information_schema.tables t on
                    c.TABLE_NAME = t.TABLE_NAME
                    and c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    where
                    UPPER(c.table_schema) = UPPER('@schemaname')
                    and t.TABLE_TYPE = 'BASE TABLE';
                </Datatype>
            </Storage>
            <Code>
                <Procedure>
                    SELECT ROUTINE_TYPE as object_type, ROUTINE_NAME as Code_Object_Name
                    FROM information_schema.ROUTINES
                    WHERE ROUTINE_SCHEMA = '@schemaname'
                    and ROUTINE_TYPE = 'PROCEDURE'
                </Procedure>
                <Function>
                    SELECT ROUTINE_TYPE as object_type, ROUTINE_NAME as Code_Object_Name
                    FROM information_schema.ROUTINES
                    WHERE ROUTINE_SCHEMA = '@schemaname'
                    and ROUTINE_TYPE = 'FUNCTION'
                </Function>
                <Trigger>
                    SELECT TRIGGER_SCHEMA as table_owner,EVENT_OBJECT_TABLE as table_name, TRIGGER_NAME
                    FROM information_schema.TRIGGERS
                    WHERE TRIGGER_SCHEMA = '@schemaname';
                </Trigger>
                <!--                <Type>-->
                <!--                    select type_name from dba_types-->
                <!--                    where owner = upper('@schemaname')-->
                <!--                    order by type_name-->
                <!--                </Type>-->
<!--                <Event>-->
<!--                select EVENT_SCHEMA, EVENT_NAME-->
<!--                from-->
<!--                    information_schema.EVENTS-->
<!--                where-->
<!--                    EVENT_SCHEMA = '@schemaname';-->
<!--                </Event>-->
            </Code>
        </Source>
        <Target>
            <Storage>
                <!--                <Sequence>-->
                <!--                    select /*+ PARALLEL(@degree) */ DISTINCT sequence_owner,sequence_name, TO_NUMBER(LAST_NUMBER) - -->
                <!--                    TO_NUMBER(INCREMENT_BY) AS sequence_value-->
                <!--                    from all_sequences-->
                <!--                    where sequence_owner in upper('@schemaname')-->
                <!--                    and sequence_owner NOT LIKE '%$%'-->
                <!--                </Sequence>-->
                <Table>
                    select
	                TABLE_SCHEMA,
                        TABLE_NAME,
                        TABLE_TYPE
                    from
                        information_schema.tables t
                    where
                        TABLE_TYPE = 'BASE TABLE'
                        and table_schema = '@schemaname'
                        and not exists (
                        select
                            1
                        from
                            information_schema.partitions p
                        where
                            TABLE_SCHEMA = '@schemaname'
                            and PARTITION_NAME is not null
                            and p.table_name = t.TABLE_NAME);
                </Table>
                <Partition>
                    select
                    distinct TABLE_SCHEMA,TABLE_NAME, PARTITION_NAME
                    from
                    information_schema.partitions p
                    where PARTITION_NAME is not null
                    and table_schema = '@schemaname'
                </Partition>
                <Primary_Key>
                    select TABLE_SCHEMA,TABLE_NAME,CONSTRAINT_NAME,GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION
                    SEPARATOR ', ') col_list,
                    'Enabled' as status
                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    TABLE_SCHEMA = lower('@schemaname')
                    and constraint_name = 'PRIMARY'
                    GROUP BY
                    TABLE_NAME, CONSTRAINT_NAME;
                </Primary_Key>
                <Unique_Constraint>
                    select TC.TABLE_SCHEMA,TC.TABLE_NAME,TC.CONSTRAINT_NAME,KCU.COLUMN_NAME,'ENABLED' as status
                    FROM
                    information_schema.TABLE_CONSTRAINTS TC
                    JOIN
                    information_schema.KEY_COLUMN_USAGE KCU
                    ON TC.CONSTRAINT_NAME = KCU.CONSTRAINT_NAME
                    AND TC.TABLE_NAME = KCU.TABLE_NAME
                    AND TC.TABLE_SCHEMA = KCU.TABLE_SCHEMA
                    WHERE
                    TC.CONSTRAINT_TYPE = 'UNIQUE'
                    AND TC.TABLE_SCHEMA = '@schemaname'
                    GROUP BY
                    TC.TABLE_SCHEMA, TC.TABLE_NAME, TC.CONSTRAINT_NAME,KCU.COLUMN_NAME
                    ORDER BY
                    TC.TABLE_NAME, TC.CONSTRAINT_NAME;
                </Unique_Constraint>
                <Foreign_Key>
                    select TABLE_SCHEMA,TABLE_NAME,CONSTRAINT_NAME,
                    CONCAT(' FOREIGN KEY (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION SEPARATOR ', '),
                    ') REFERENCES ',
                    REFERENCED_TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(REFERENCED_COLUMN_NAME ORDER BY POSITION_IN_UNIQUE_CONSTRAINT SEPARATOR ', '),
                    ');'
                    ) AS ForeignKey,'Enabled' as status

                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    TABLE_SCHEMA = lower('@schemaname')
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                    GROUP BY
                    TABLE_NAME, CONSTRAINT_NAME,REFERENCED_TABLE_NAME
                </Foreign_Key>
                <Not_Null_Constraint>
                    select
                    c.TABLE_SCHEMA,
                    c.TABLE_NAME,
                    c.COLUMN_NAME
                    from
                    information_schema.columns c
                    inner join information_schema.tables t on
                    c.TABLE_NAME = t.TABLE_NAME
                    and c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    where
                    c.TABLE_SCHEMA = lower('@schemaname')
                    and t.TABLE_TYPE = 'BASE TABLE'
                    and c.IS_NULLABLE = 'NO'
                    order by
                    c.TABLE_NAME,
                    c.COLUMN_NAME;
                </Not_Null_Constraint>
                <Default_Constraint>
                    select
                    c.TABLE_SCHEMA,
                    c.TABLE_NAME,
                    c.COLUMN_NAME,
                    coalesce (c.COLUMN_DEFAULT,'NULL') COLUMN_DEFAULT
                    from
                    information_schema.columns c
                    join information_schema.tables t on
                    c.TABLE_NAME = t.TABLE_NAME
                    and c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    where
                    c.TABLE_SCHEMA = lower('@schemaname')
                    and t.TABLE_TYPE = 'BASE TABLE'
                    and c.IS_NULLABLE = 'YES'
                    order by
                    TABLE_NAME,
                    COLUMN_NAME;


                </Default_Constraint>
                <Check_Constraint>
                    select
                    cc.CONSTRAINT_SCHEMA,tc.TABLE_NAME,cc.CONSTRAINT_NAME,cc.CHECK_CLAUSE, 'ENABLED' as status
                    from
                    information_schema.CHECK_CONSTRAINTS cc
                    join information_schema.table_constraints tc
                    on cc.CONSTRAINT_SCHEMA = tc.CONSTRAINT_SCHEMA and cc.CONSTRAINT_NAME= tc.CONSTRAINT_NAME
                    where
                    cc.CONSTRAINT_SCHEMA = lower('@schemaname')
                    order by
                    CONSTRAINT_SCHEMA,
                    TABLE_NAME;
                </Check_Constraint>
                <Index>
                    select TABLE_SCHEMA,TABLE_NAME,INDEX_NAME,
                    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX SEPARATOR ', ') index_cols,
                    IF(NON_UNIQUE = 0, 'UNIQUE ', '') uniqueness,
                    INDEX_TYPE,
                    CONCAT(
                    'CREATE ',
                    IF(NON_UNIQUE = 0, 'UNIQUE ', ''),
                    'INDEX ',
                    INDEX_NAME,
                    ' ON ',
                    TABLE_SCHEMA,
                    '.',
                    TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX SEPARATOR ', '),
                    ');'
                    ) AS IDX_DEF,

                    concat(TABLE_SCHEMA,',',TABLE_NAME,'-',INDEX_NAME) idx_concat

                    FROM
                    information_schema.STATISTICS
                    WHERE
                    TABLE_SCHEMA = '@schemaname'
                    and
                    INDEX_NAME != 'PRIMARY'
                    GROUP BY
                    INDEX_NAME, TABLE_NAME, NON_UNIQUE, INDEX_TYPE;
                </Index>

                <!--                <Synonym>-->
                <!--                    select /*+ PARALLEL(@degree) */ SYNONYM_NAME,TABLE_NAME,TABLE_OWNER from dba_synonyms where-->
                <!--                    owner=upper('@schemaname')-->
                <!--                </Synonym>-->
                <View>
                    select TABLE_SCHEMA,
                    table_name as View_name
                    from
                    information_schema.VIEWS
                    where
                    TABLE_SCHEMA = '@schemaname';
                </View>
                <Datatype>
                    select
                    c.TABLE_SCHEMA,
                    c.TABLE_NAME,
                    c.COLUMN_NAME,
                    c.DATA_TYPE,
                    case
                    when DATA_TYPE in ('int', 'bigint', 'decimal', 'tinyint') then concat(cast(NUMERIC_PRECISION as
                    char), ',', cast(NUMERIC_SCALE as CHAR))
                    when DATA_TYPE in ('varchar', 'char', 'text' ) then CHARACTER_MAXIMUM_LENGTH
                    when DATA_TYPE in ('double') then cast(NUMERIC_PRECISION as char)
                    end as "COLUMN_SIZE" ,
                    ORDINAL_POSITION,
                    'Table' as object_type
                    from
                    information_schema.columns c
                    inner join information_schema.tables t on
                    c.TABLE_NAME = t.TABLE_NAME
                    and c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    where
                    UPPER(c.table_schema) = UPPER('@schemaname')
                    and t.TABLE_TYPE = 'BASE TABLE' ;
                </Datatype>
            </Storage>
            <Code>
                <Procedure>

                    SELECT ROUTINE_TYPE as object_type, ROUTINE_NAME as Code_Object_Name
                    FROM information_schema.ROUTINES
                    WHERE ROUTINE_SCHEMA = '@schemaname'
                    and ROUTINE_TYPE = 'PROCEDURE'
                </Procedure>
                <Function>
                    SELECT ROUTINE_TYPE as object_type, ROUTINE_NAME as Code_Object_Name
                    FROM information_schema.ROUTINES
                    WHERE ROUTINE_SCHEMA = '@schemaname'
                    and ROUTINE_TYPE = 'FUNCTION'
                </Function>
                <Trigger>
                    SELECT TRIGGER_SCHEMA as table_owner,EVENT_OBJECT_TABLE as table_name, TRIGGER_NAME
                        FROM information_schema.TRIGGERS
                        WHERE TRIGGER_SCHEMA = '@schemaname';

                </Trigger>
<!--                <Event>-->
<!--                    select EVENT_SCHEMA, EVENT_NAME-->
<!--                    from-->
<!--                        information_schema.EVENTS-->
<!--                    where-->
<!--                        EVENT_SCHEMA = '@schemaname';-->
<!--                </Event>-->
            </Code>
        </Target>
    </Validation_Queries>
</Queries>