import os

import psycopg2, pyodbc, pymssql, re


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def DB_connection(database_data):
    # path = 'Devart ODBC Driver for ASE'
    # path = 'Adaptive Server Enterprise'  # for Local
    # path = os.getenv('DRIVER_PATH')  # for Cloud
    path = 'Sybase'  # for Cloud
    host_name = database_data['host']
    port = database_data['port']
    db_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    try:
        conn_str = f'DRIVER={path};SERVER={host_name};PORT={port};DATABASE={db_name};UID={user_name};PWD={password};'
        connection = pyodbc.connect(conn_str)
        error = ''
    except Exception as e:
        connection = None
        error = str(e)
        print("Issue found near database connection", e)
    return connection, error


def target_DB_connection(database_data):
    host_name = database_data['host']
    db_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    port = database_data['port']
    try:
        connection = pymssql.connect(user=user_name, password=password,
                                     server=host_name,
                                     database=db_name, port=port)
        error = ''
    except pymssql.DatabaseError as e:
        connection = None
        error = str(e)
        print("Issue found near source database connection", e)
    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except Exception as e:
        print("Issue found near database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data


def execute_query_sybase(db_connection, query, db_name=None, filter=None):
    object_name = re.findall(r'\.(\w+)\'', query, flags=re.DOTALL | re.I)
    if db_name and object_name:
        db_name = db_name
        object_name = object_name[0]
    else:
        db_name = ''
        object_name = ''
    db_connection.autocommit = True
    cursor = db_connection.cursor()
    data = None
    try:
        sp_help = re.search(r'\bsp\_help\s+', query, flags=re.DOTALL | re.I)
        if 'EXEC' in query and sp_help:
            cursor.execute(f"USE {db_name.strip()}")
            cursor.execute(f" sp_help '{object_name}'")
            result_sets = []
            while True:
                rows = cursor.fetchall()
                if not rows:
                    break
                columns = [column[0] for column in cursor.description]
                result_set = [dict(zip(columns, row)) for row in rows]
                result_sets.append(result_set)
                if not cursor.nextset():
                    break
            final_result = []
            for i, result_set in enumerate(result_sets):
                for row in result_set:
                    if i + 1 == 2:
                        final_result.append(row)
            if final_result:
                data = final_result
        else:
            cursor.execute(query)
            data = cursor.fetchall()
            if 'EXEC' in query and not sp_help and not 'sp_helpindex' in query:
                data = [tuple(row) for row in data]
            if 'sp_helpindex' in query and not filter:
                data = [(row[0], row[1]) for row in data if row[2] == 'nonclustered']
            if 'sp_helpindex' in query and filter:
                data = [(row[0], row[1]) for row in data if row[2] == 'nonclustered, unique']
    except Exception as e:
        if "'CREATE TABLE' command is not allowed within a multi-statement transaction" in str(e):
            print("Ignoring specific error related to CREATE TABLE.", query)
        else:
            print("Issue found near database query:", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
