import os, sys, warnings
from import_file import import_file
import xml.etree.ElementTree as ET
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import request_insert, request_update, schema_insert

warnings.filterwarnings('ignore')

def extract_schemas_trigger(task_name, project_id, migration_name, target_connection_id, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    print(f'Performing {task_name} process')
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        if not os.path.exists(working_directory_path):
            os.makedirs(working_directory_path)

        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'extraction_queries.xml'

        tree = ET.parse(xml_path)
        root = tree.getroot()
        objects_tag = root.find('Extraction_Queries')
        objects_list = []
        for object_tag in objects_tag.iter():
            objects_list.append(object_tag.tag)

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            function_call = getattr(import_object, 'connect_database')
            target_function_call = getattr(import_object, 'target_DB_connection')
            execute_function_call = getattr(import_object, 'execute_query')
            request_id = ''
            project_DB_details = {}
            target_DB_details = {}
            try:
                token_data = api_authentication()
                project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')

                project_connection = function_call(project_DB_details)
                request_id = request_insert(project_connection, None, target_connection_id, task_name,
                                            'Target_Extract', 'All', 'All')[0]

                target_DB_details = decrypt_database_details(token_data, project_id, 'Target',
                                                             target_connection_id)

                project_folder = 'PRJ' + project_id + 'SRC'


                if not os.path.exists(root_path + '/' + project_folder):
                    os.makedirs(root_path + '/' + project_folder)

                schemas_tag = root.find('Target_Schemas')
                target_connection, error = target_function_call(target_DB_details)
                schema_output = execute_function_call(target_connection, schemas_tag.text)
                schema_list = [i[0].strip() for i in schema_output]
                print(f'Extracted schemas are {schema_list}')

                for schema in schema_list:
                    project_connection = function_call(project_DB_details)
                    schema_insert(project_connection, schema, target_connection_id)

                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Completed', None)
            except Exception as error:
                print(f"Error occurred at schema extraction: {error}")

                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Error', str(error))
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
