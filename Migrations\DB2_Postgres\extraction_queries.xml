<Queries>
    <Extraction_Queries>
        <Code_Objects>
            <Procedure>
                <ListQuery>
                    SELECT
                    ROUTINENAME AS function_name, 'VALID' AS STATUS
                    --TEXT AS function_definition
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'P'
                    AND VALID = 'Y';
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    --ROUTINENAME AS procedure_name,
                    TEXT AS procedure_definition
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'P'
                    AND VALID = 'Y' AND upper(ROUTINENAME) = upper('@name');
                </DefinitionQuery>
            </Procedure>
            <Function>
                <ListQuery>
                    SELECT
                    ROUTINENAME AS function_name, 'VALID' AS STATUS
                    --TEXT AS function_definition
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'F'
                    AND VALID = 'Y';
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    --ROUTINENAME AS function_name,
                    TEXT AS function_definition
                    FROM SYSCAT.ROUTINES
                    WHERE upper(ROUTINESCHEMA) = upper('@schemaname')
                    AND ROUTINETYPE = 'F'
                    AND VALID = 'Y'AND upper(ROUTINENAME) = upper('@name');
                </DefinitionQuery>
            </Function>
            <Trigger>
                <ListQuery>
                    SELECT TRIGNAME AS object_name, 'VALID' STATUS
                    FROM SYSCAT.TRIGGERS WHERE TRIGSCHEMA = upper('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT TEXT AS trigger_definition
                    FROM SYSCAT.TRIGGERS
                    WHERE upper(TRIGSCHEMA) = UPPER('@schemaname') AND VALID = 'Y' AND lower(TRIGNAME)=lower('@name')
                </DefinitionQuery>
            </Trigger>
        </Code_Objects>
        <Storage_Objects>
            <Table>
                <ListQuery>
                    SELECT DISTINCT TABNAME AS OBJECT_NAME, 'VALID' STATUS
                    FROM SYSCAT.TABLES A
                    WHERE NOT EXISTS (
                    SELECT 1
                    FROM SYSCAT.TABLES MV
                    WHERE MV.TABNAME = A.TABNAME
                    AND MV.TABSCHEMA = upper('@schemaname')
                    AND MV.TYPE = 'M'
                    )
                    AND A.TABSCHEMA = upper('@schemaname')
                    AND A.TABNAME NOT LIKE '%$%'
                    AND A.TYPE = 'T'
                    AND A.TABNAME NOT LIKE 'SYS%'
                    AND A.TABNAME NOT IN ('BLOB_TABLE','CUSTOMER',
                    'Customer','EMPLOYEE','EMPLOYEES','EMP_PHOTO','Employee','IMAGE_DETAILS','XML_NODES')
                    ORDER BY 1
                </ListQuery>
                <DefinitionQuery>
                    WITH TABLE_LIST AS (
                    SELECT
                        T.TABSCHEMA,
                        T.TABNAME
                    FROM
                        SYSCAT.TABLES T
                    WHERE
                        T.TABSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT', 'SYSIBMADM')
                        AND UPPER(T.TABSCHEMA) = UPPER('@schemaname')
                        AND upper(TABNAME) = UPPER('@name')
                        AND T.TYPE = 'T' )
                    SELECT
                    replace(
                        'CREATE TABLE ' || TL.TABSCHEMA || '.' || TL.TABNAME || ' (' || LISTAGG( '`' || C.COLNAME || '` ' || C.TYPENAME || CASE
                            WHEN C.TYPENAME IN ('CHAR', 'VARCHAR', 'CLOB', 'GRAPHIC', 'VARGRAPHIC', 'CHARACTER') THEN '(' || RTRIM(CHAR(C.LENGTH)) || ')'
                            WHEN C.TYPENAME = 'DECIMAL' THEN '(' || RTRIM(CHAR(C.LENGTH)) || ', ' || RTRIM(CHAR(C.SCALE)) || ')'
                            ELSE ''
                        END,
                        ', ' ) WITHIN GROUP (
                        ORDER BY C.COLNO) || ');' ,'`','"') AS ddl
                    FROM
                        SYSCAT.COLUMNS C
                    JOIN TABLE_LIST TL ON
                        C.TABSCHEMA = TL.TABSCHEMA
                        AND C.TABNAME = TL.TABNAME
                    GROUP BY
                        TL.TABSCHEMA,
                        TL.TABNAME
                </DefinitionQuery>
            </Table>
            <Sequence>
                <ListQuery>
                    SELECT
                    SEQNAME ,
                    'VALID' AS STATUS
                    FROM
                    SYSCAT.SEQUENCES
                    WHERE
                    SEQSCHEMA = upper('@schemaname')
                    AND SEQSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT', 'SYSIBMADM')
                </ListQuery>
                <DefinitionQuery>
                    select 'CREATE SEQUENCE ' || SEQSCHEMA || '.' || SEQNAME ||
                   ' START WITH ' || START ||
                   ' INCREMENT BY ' || INCREMENT ||
                   CASE -->
                    WHEN MINVALUE IS NOT NULL THEN ' MINVALUE ' || MINVALUE
                   ELSE ''
                  END ||
                    CASE
                   WHEN MAXVALUE IS NOT NULL THEN ' MAXVALUE ' || MAXVALUE
                    ELSE ''
                    END ||
                   CASE
                    WHEN CYCLE = 'Y' THEN ' CYCLE'
                    ELSE ' NO CYCLE'
                    END ||
                   ' CACHE ' || CACHE || ';' AS DDL
                    FROM SYSCAT.SEQUENCES
                     WHERE SEQSCHEMA = upper('@schemaname')
                    and SEQNAME = upper('@name')
                    and SEQSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM');
                </DefinitionQuery>
            </Sequence>
            <Not_Null_Constraint>
                <ListQuery>
                    SELECT
                    upper(C.TABNAME) || '-' || upper(C.COLNAME) AS NOTNULL_CONSTRAINT_NAME,
                    'ENABLED' AS STATUS
                    FROM
                    SYSCAT.COLUMNS C
                    JOIN SYSCAT.TABLES T ON
                    C.TABSCHEMA = T.TABSCHEMA
                    AND C.TABNAME = T.TABNAME
                    WHERE
                    C.NULLS = 'N'
                    AND C.TABSCHEMA = upper('@schemaname')
                    AND T.TYPE = 'T'
                    ORDER BY
                    C.TABSCHEMA,
                    C.TABNAME,
                    C.COLNAME

                </ListQuery>
                <DefinitionQuery>
                    SELECT
                        'ALTER TABLE ' || C.TABSCHEMA || '.' || C.TABNAME || ' ALTER COLUMN ' || C.COLNAME || ' SET not null;' AS DDL_STATEMENT
                    FROM
                        SYSCAT.COLUMNS C
                    JOIN SYSCAT.TABLES T ON
                        C.TABSCHEMA = T.TABSCHEMA
                        AND C.TABNAME = T.TABNAME
                    WHERE
                        C.NULLS = 'N'
                        AND C.TABSCHEMA = UPPER('@schemaname')
                        AND C.TABNAME || '-' || C.COLNAME  = UPPER('@name')
                </DefinitionQuery>


            </Not_Null_Constraint>
            <Primary_Key>
                <ListQuery>
                    SELECT
                    upper(ac.tabname) || '-' || upper(ac.constname) ,
                    'ENABLED' AS STATUS
                    FROM
                    syscat.tabconst ac
                    WHERE
                    ac.tabschema IN (UPPER('@schemaname'))
                    AND ac.type = 'P'
                    AND ac.constname NOT LIKE '%$%'
                    ORDER BY
                    ac.tabschema;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' || ac.tabschema || '.' || ac.tabname || ' ADD CONSTRAINT ' || ac.constname || ' PRIMARY KEY (' || ccl.col_list || ');'
                    FROM
                    syscat.tabconst ac
                    JOIN (
                    SELECT
                    acc.tabschema AS owner,
                    acc.tabname AS table_name,
                    acc.constname AS constraint_name,
                    LISTAGG(acc.colname,
                    ',') WITHIN GROUP (
                    ORDER BY acc.colseq) AS col_list
                    FROM
                    syscat.keycoluse acc
                    GROUP BY
                    acc.tabschema,
                    acc.tabname,
                    acc.constname ) ccl ON
                    ac.tabschema = ccl.owner
                    AND ac.tabname = ccl.table_name
                    AND ac.constname = ccl.constraint_name
                    WHERE
                    ac.tabschema IN (UPPER('@schemaname'))
                    AND upper(ac.tabname) || '-' || upper(ac.constname) = upper('@name')
                    AND ac.type = 'P'
                    AND ac.constname NOT LIKE '%$%'
                    ORDER BY
                    ac.tabschema;

                </DefinitionQuery>

            </Primary_Key>
            <Unique_Constraint>
                <ListQuery>
                    SELECT
                    ac.tabname || '-' || ac.constname ,
                    'VALID' AS Status
                    FROM
                    syscat.tabconst ac
                    JOIN (
                    SELECT
                    acc.tabschema AS owner,
                    acc.tabname AS table_name,
                    acc.constname AS constraint_name,
                    LISTAGG(acc.colname,
                    ',') WITHIN GROUP (
                    ORDER BY acc.colseq) AS col_list
                    FROM
                    syscat.keycoluse acc
                    GROUP BY
                    acc.tabschema,
                    acc.tabname,
                    acc.constname ) ccl ON
                    ac.tabschema = ccl.owner
                    AND ac.tabname = ccl.table_name
                    AND ac.constname = ccl.constraint_name
                    WHERE
                    ac.tabschema = UPPER('@schemaname')
                    AND ac.type = 'U'
                    AND ac.constname NOT LIKE '%$%'
                    ORDER BY
                    ac.tabschema
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' || ac.tabschema || '.' || ac.tabname || ' ADD CONSTRAINT ' || ac.constname || ' UNIQUE (' || ccl.col_list || ');'
                FROM
                    syscat.tabconst ac
                JOIN (
                    SELECT
                        acc.tabschema AS owner,
                        acc.tabname AS table_name,
                        acc.constname AS constraint_name,
                        LISTAGG(acc.colname,
                        ',') WITHIN GROUP (
                        ORDER BY acc.colseq) AS col_list
                    FROM
                        syscat.keycoluse acc
                    GROUP BY
                        acc.tabschema,
                        acc.tabname,
                        acc.constname ) ccl ON
                    ac.tabschema = ccl.owner
                    AND ac.tabname = ccl.table_name
                    AND ac.constname = ccl.constraint_name
                WHERE
                    ac.tabschema IN (UPPER('@schemaname'))
                    AND ac.tabname || '-' || ac.constname = upper('@name') ;
                    -- ac.tabname || '-' || ac.constname name AND ac.type = 'U' AND ac.constname NOT LIKE '%$%' ORDER BY ac.tabschema

                </DefinitionQuery>
            </Unique_Constraint>
            <Foreign_Key>
                <ListQuery>
                    WITH CTE AS (
                SELECT
                    tc.tabname AS table_name,
                    tc.constname AS constraint_name,
                    tc.enforced AS status,
                    tc.tabschema AS schema_name,
                    CAST('ALTER TABLE ' || tc.tabschema || '.' || tc.tabname || ' ADD CONSTRAINT ' || tc.constname || ' FOREIGN KEY (' AS VARCHAR(500)) || CAST((
                    SELECT
                        LISTAGG(kc.colname,
                        ',') WITHIN GROUP (
                        ORDER BY kc.colseq)
                    FROM
                        syscat.keycoluse kc
                    WHERE
                        kc.constname = tc.constname
                        AND kc.tabschema = tc.tabschema) AS VARCHAR(500)) || CAST(') REFERENCES ' AS VARCHAR(50)) || CAST(ref.tabschema AS VARCHAR(128)) || '.' || CAST(ref.reftabname AS VARCHAR(128)) || '(' || CAST((
                    SELECT
                        LISTAGG(kc2.colname,
                        ',') WITHIN GROUP (
                        ORDER BY kc2.colseq)
                    FROM
                        syscat.keycoluse kc2
                    WHERE
                        kc2.constname = ref.refkeyname
                        AND kc2.tabschema = ref.reftabschema) AS VARCHAR(500)) || ')' || ';' AS alter_statement
                FROM
                    syscat.tabconst tc
                JOIN syscat.references REF ON
                    tc.constname = ref.constname
                    AND tc.tabschema = ref.tabschema
                WHERE
                    tc.tabschema = UPPER('@schemaname')
                    AND tc.constname NOT LIKE '%$%'
                    AND tc.tabname NOT LIKE '%$%'
                    AND tc.type = 'F' )
                SELECT
                    DISTINCT upper(CTE.TABLE_NAME)|| '-' || upper(CTE.constraint_name) AS FOREIGN_CONSTRAINT_NAME,
                    status
                FROM
                    CTE
                WHERE
                    cte.schema_name = upper('@schemaname')

                </ListQuery>
                <DefinitionQuery>
                    WITH CTE AS (
                    SELECT
                        tc.tabname AS table_name,
                        tc.constname AS constraint_name,
                        tc.enforced AS status,
                        tc.tabschema AS schema_name,
                        CAST('ALTER TABLE ' || tc.tabschema || '.' || tc.tabname || ' ADD CONSTRAINT ' || tc.constname || ' FOREIGN KEY (' AS VARCHAR(500)) || CAST((
                        SELECT
                            LISTAGG(kc.colname,
                            ',') WITHIN GROUP (
                            ORDER BY kc.colseq)
                        FROM
                            syscat.keycoluse kc
                        WHERE
                            kc.constname = tc.constname
                            AND kc.tabschema = tc.tabschema) AS VARCHAR(500)) || CAST(') REFERENCES ' AS VARCHAR(50)) || CAST(ref.tabschema AS VARCHAR(128)) || '.' || CAST(ref.reftabname AS VARCHAR(128)) || '(' || CAST((
                        SELECT
                            LISTAGG(kc2.colname,
                            ',') WITHIN GROUP (
                            ORDER BY kc2.colseq)
                        FROM
                            syscat.keycoluse kc2
                        WHERE
                            kc2.constname = ref.refkeyname
                            AND kc2.tabschema = ref.reftabschema) AS VARCHAR(500)) || ')' || ';' AS alter_statement
                    FROM
                        syscat.tabconst tc
                    JOIN syscat.references REF ON
                        tc.constname = ref.constname
                        AND tc.tabschema = ref.tabschema
                    WHERE
                        tc.tabschema = UPPER('@schemaname')
                        AND tc.constname NOT LIKE '%$%'
                        AND tc.tabname NOT LIKE '%$%'
                        AND tc.type = 'F' )
                    SELECT
                        DISTINCT CTE.alter_statement
                    FROM
                        CTE
                    WHERE
                        cte.schema_name = upper('@schemaname')
                        AND upper(CTE.table_name) || '-' || upper(CTE.constraint_name) = upper('@name')
                </DefinitionQuery>
            </Foreign_Key>
            <Default_Constraint>
                <ListQuery>
                    SELECT
                        TABNAME || '-' || COLNAME,
                        'ENABLED' AS STATUS
                    FROM
                        SYSCAT.COLUMNS
                    WHERE
                        DEFAULT IS NOT NULL
                        AND TABSCHEMA = upper('@schemaname')

                </ListQuery>
                <DefinitionQuery>
                    SELECT
                        'ALTER TABLE ' || C.TABSCHEMA || '.' || C.TABNAME || ' ALTER COLUMN ' || COLNAME || ' SET DEFAULT ' ||
                        case when C.DEFAULT  = '' then NULL
                        else C.DEFAULT end ||
                        ';' AS alter_statement
                    FROM
                        SYSCAT.COLUMNS C
                    JOIN SYSCAT.TABLES T ON
                        C.TABSCHEMA = T.TABSCHEMA
                        AND C.TABNAME = T.TABNAME
                    WHERE
                        (C.DEFAULT IS NOT NULL)
                        AND T.TYPE = 'T'
                        AND C.TABSCHEMA = UPPER('@schemaname')
                        AND C.TABNAME || '-' || C.COLNAME  = UPPER('@name')
                </DefinitionQuery>
            </Default_Constraint>
            <Check_Constraint>
                <ListQuery>
                    SELECT
                        ac.tabname || '-' || ac.constname,
                        'ENABLED' AS STATUS
                    FROM
                        syscat.checks ac
                    JOIN syscat.colchecks acc ON
                        ac.tabschema = acc.tabschema
                        AND ac.tabname = acc.tabname
                        AND ac.constname = acc.constname
                    WHERE
                        ac.type = 'C'
                        AND ac.tabschema = UPPER('@schemaname')
                        AND (ac.tabname || '.' || acc.colname) NOT IN (
                        SELECT
                            DISTINCT tabname || '.' || colname
                        FROM
                            syscat.columns
                        WHERE
                            NULLS = 'N' )
                        AND ac.constname NOT LIKE '%$%'
                        AND ac.tabname NOT LIKE '%$%'
                    ORDER BY
                        ac.tabschema
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' || ac.tabschema || '.' || ac.tabname || ' ADD CONSTRAINT ' || ac.constname || ' CHECK (' || ac.text || ');'
                FROM
                    syscat.checks ac
                JOIN syscat.colchecks acc ON
                    ac.tabschema = acc.tabschema
                    AND ac.tabname = acc.tabname
                    AND ac.constname = acc.constname
                WHERE
                    ac.type = 'C'
                    AND ac.tabschema = UPPER('@schemaname')
                    AND ac.tabname || '-' || ac.constname = upper('@name')
                    AND (ac.tabname || '.' || acc.colname) NOT IN (
                    SELECT
                        DISTINCT tabname || '.' || colname
                    FROM
                        syscat.columns
                    WHERE
                        NULLS = 'N' )
                    AND ac.constname NOT LIKE '%$%'
                    AND ac.tabname NOT LIKE '%$%'
                ORDER BY
                    ac.tabschema
                </DefinitionQuery>
            </Check_Constraint>
            <Index>
                <ListQuery>
                    WITH cols AS (
                          SELECT
                            i.TABSCHEMA AS schema_name,
                            i.TABNAME   AS table_name,
                            i.INDNAME   AS index_name,
                            'ENABLED' as status,
                            CASE
                              WHEN i.UNIQUERULE = 'D' THEN 'NONUNIQUE'
                              ELSE 'UNIQUE'
                            END AS uniqueness,
                            RTRIM(
                              XMLSERIALIZE(
                                CONTENT XMLAGG(
                                  XMLTEXT(
                                    CASE
                                      WHEN ic.COLNAME LIKE 'SYS_N%' THEN ''  -- omit expression for functional indexes
                                      ELSE ic.COLNAME || (CASE WHEN ic.COLORDER = 'D' THEN ' DESC' ELSE '' END)
                                    END
                                  )
                                  ORDER BY ic.COLSEQ
                                )
                                AS CLOB(1024)
                              ),
                              ', '
                            ) AS index_cols
                          FROM SYSCAT.INDEXES i
                          JOIN SYSCAT.INDEXCOLUSE ic
                            ON i.INDNAME = ic.INDNAME
                           AND i.TABSCHEMA = ic.INDSCHEMA
                          WHERE i.TABSCHEMA = UPPER('@schemaname')
                            AND i.TABNAME NOT LIKE '%$%'
                          GROUP BY i.TABSCHEMA, i.TABNAME, i.INDNAME, i.UNIQUERULE
                        ),
                        tbl AS (
                          SELECT
                            t.TABSCHEMA,
                            t.TABNAME,
                            t.TYPE AS table_type
                          FROM SYSCAT.TABLES t
                          WHERE t.TABSCHEMA = UPPER('@schemaname')
                        ),
                        index_data AS (
                            SELECT
                                cols.schema_name,
                                cols.table_name,
                                CASE WHEN tbl.table_type = 'M' THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS table_type,
                                cols.index_cols,
                                cols.index_name,
                                cols.uniqueness,
                                cols.status
                            FROM
                                cols
                            LEFT JOIN tbl ON
                                cols.schema_name = tbl.TABSCHEMA
                                AND cols.table_name = tbl.TABNAME
                        )
                        SELECT

                        index_data.table_name  || '-' || index_data.index_name,
                         index_data.status

                        FROM index_data
                        ORDER BY index_data.schema_name, index_data.table_name;
                </ListQuery>
                <DefinitionQuery>
                    WITH cols AS (
                    SELECT
                    i.TABSCHEMA AS schema_name,
                    i.TABNAME   AS table_name,
                    i.INDNAME   AS index_name,
                    CASE
                    WHEN i.UNIQUERULE = 'D' THEN 'NONUNIQUE'
                    ELSE 'UNIQUE'
                    END AS uniqueness,
                    RTRIM(
                    XMLSERIALIZE(
                    CONTENT XMLAGG(
                    XMLTEXT(
                    CASE
                    WHEN ic.COLNAME LIKE 'SYS_N%' THEN ''  -- omit expression for functional indexes
                    ELSE ic.COLNAME || (CASE WHEN ic.COLORDER = 'D' THEN ' DESC' ELSE '' END)
                    END
                    )
                    ORDER BY ic.COLSEQ
                    )
                    AS CLOB(1024)
                    ),
                    ', '
                    ) AS index_cols
                    FROM SYSCAT.INDEXES i
                    JOIN SYSCAT.INDEXCOLUSE ic
                    ON i.INDNAME = ic.INDNAME
                    AND i.TABSCHEMA = ic.INDSCHEMA
                    WHERE i.TABSCHEMA = UPPER('@schemaname')
                    AND i.TABNAME NOT LIKE '%$%'
                    AND i.TABNAME || '-' || i.INDNAME = UPPER('@name')
                    GROUP BY i.TABSCHEMA, i.TABNAME, i.INDNAME, i.UNIQUERULE
                    ),
                    tbl AS (
                    SELECT
                    t.TABSCHEMA,
                    t.TABNAME,
                    t.TYPE AS table_type
                    FROM SYSCAT.TABLES t
                    WHERE t.TABSCHEMA = UPPER('@schemaname')
                    )
                    SELECT (
                    'CREATE ' ||
                    CASE WHEN c.uniqueness = 'NONUNIQUE' THEN 'INDEX ' ELSE 'UNIQUE INDEX ' END ||
                    c.index_name || ' ON ' || c.schema_name || '.' || c.table_name ||
                    ' (' || RTRIM(c.index_cols, ', ') || ')' ||
                    CASE WHEN c.uniqueness = 'NONUNIQUE' THEN '' ELSE ' NULLS NOT DISTINCT ' END ||
                    ';'
                    ) AS IDX_DEF
                    FROM cols c
                    LEFT JOIN tbl t
                    ON c.schema_name = t.TABSCHEMA
                    AND c.table_name   = t.TABNAME
                    ORDER BY c.schema_name, c.table_name;

                </DefinitionQuery>
            </Index>
            <View>
                <ListQuery>
                    SELECT
                        VIEWNAME,
                        'ENABLED' AS STATUS
                    FROM
                        SYSCAT.VIEWS
                    WHERE
                        VIEWSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT', 'SYSIBMADM')
                        AND VIEWSCHEMA = upper('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                        text
                    FROM
                        SYSCAT.VIEWS
                    WHERE
                        VIEWSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT', 'SYSIBMADM')
                        AND VIEWNAME = upper('@name')
                        AND VIEWSCHEMA = upper('@schemaname')
                </DefinitionQuery>
            </View>

        </Storage_Objects>
    </Extraction_Queries>
    <Database_Exclusion_Query>
        SELECT DISTINCT LOWER(COLNAME) AS name
        FROM SYSCAT.COLUMNS
        WHERE TABSCHEMA NOT LIKE 'SYS%'
        AND COLNAME IS NOT NULL
        UNION
        SELECT DISTINCT LOWER(PARMNAME) AS name
        FROM SYSCAT.ROUTINEPARMS
        WHERE
        ROUTINESCHEMA NOT LIKE 'SYS%'
        AND PARMNAME IS NOT NULL
        UNION
        SELECT DISTINCT LOWER(TABNAME) AS name
        FROM SYSCAT.TABLES
        WHERE TABSCHEMA NOT LIKE 'SYS%'
    </Database_Exclusion_Query>
    <Source_Schemas>
        SELECT
        TABSCHEMA AS SCHEMA_NAME,
        SUM((DATA_OBJECT_P_SIZE + INDEX_OBJECT_P_SIZE + LONG_OBJECT_P_SIZE + LOB_OBJECT_P_SIZE + XML_OBJECT_P_SIZE)/1024) AS SIZE_IN_MB
        FROM
        SYSIBMADM.ADMINTABINFO
        WHERE
        TABSCHEMA NOT LIKE 'SYS%'
        GROUP BY
        TABSCHEMA
        ORDER BY
        SIZE_IN_MB DESC
    </Source_Schemas>
    <Target_Schemas>
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('information_schema', 'pg_catalog')
        AND schema_name NOT LIKE 'pg_toast%'
        AND schema_name NOT LIKE 'pg_temp%'
    </Target_Schemas>
</Queries>