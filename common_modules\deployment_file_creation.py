import os, sys
import xml.etree.ElementTree as ET
from import_file import import_file
import pandas as pd
from datetime import datetime
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.stored_procedures import create_deployment_file, request_insert, request_update


def deployment_file_creation_trigger(task_name, project_id, migration_name, deploy_file_create_option, iteration_id,
                                     target_connection_id, target_schema, object_type, deploy_file_check,
                                     cloud_category):

    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            db_import_object = import_file(db_module_path)
            function_call = getattr(db_import_object, 'connect_database')

            project_DB_details = {}
            request_id = ''
            try:
                token_data = api_authentication()   
                project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
                project_connection = function_call(project_DB_details)

                request_id = request_insert(project_connection, None, None, task_name, task_name,
                                            target_schema, object_type)[0]

                deployment_file_path = root_path + '/' + 'PRJ' + project_id + 'SRC' + '/' + 'Single_File_Output' + '/' + 'User_Created_Files'
                if not os.path.exists(deployment_file_path):
                    os.makedirs(deployment_file_path)

                objects_list = object_type.split(',')

                deployment_file_name = ''
                if deploy_file_create_option == 'Database':
                    target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)
                    target_function_call = getattr(db_import_object, 'target_DB_connection')
                    target_connection, error = target_function_call(target_DB_details)
                    target_db_name = target_DB_details['db_name']
                    if deploy_file_check == 'True':
                        deployment_file_name = deployment_file_path + '/' + str(
                            target_db_name).capitalize() + '_~_' + target_schema.capitalize() + '_~_' + 'Single_File_Output_{}.sql'.format(
                            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                    for object_type_name in objects_list:
                        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'extraction_queries.xml'
                        tree = ET.parse(xml_path)
                        root = tree.getroot()
                        deplyment_file_query_tag = root.find('Deployment_File' + '/' + object_type_name)
                        deployment_file_query = deplyment_file_query_tag.text
                        deployment_file_query = deployment_file_query.replace('@schemaname', target_schema.upper()).replace(
                            '@order',
                            '')

                        connection_file_path = import_file(db_module_path)
                        function_call_list = getattr(connection_file_path, 'execute_query')
                        deployment_query_output = function_call_list(target_connection, deployment_file_query)

                        if deploy_file_check == 'False':
                            deployment_file_name = deployment_file_path + '/' + str(
                                target_db_name).capitalize() + '_~_' + target_schema.capitalize() + '_~_' + object_type_name + '_Single_File_Output_{}.sql'.format(
                                datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                        deployment_query_output_pd = pd.DataFrame(deployment_query_output)
                        if len(deployment_query_output_pd):
                            deployment_text_output_data = deployment_query_output_pd.loc[:, 0].tolist()
                            deployment_text_output_data = [i.replace(',', ',\n') for i in deployment_text_output_data]
                            target_output = '\n\n\n'.join(deployment_text_output_data)
                            if target_output != '':
                                with open(deployment_file_name, 'a') as f:
                                    f.write("\n\n\n{}".format(target_output))

                elif deploy_file_create_option == 'Iteration':
                    if deploy_file_check == 'True':
                        deployment_file_name = deployment_file_path + '/' + str(
                            iteration_id).capitalize() + '_~_' + target_schema.capitalize() + '_~_' + 'Single_File_Output_{}.sql'.format(
                            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                    for object_type_name in objects_list:
                        project_connection = function_call(project_DB_details)

                        if deploy_file_check == 'False':
                            deployment_file_name = deployment_file_path + '/' + str(
                                iteration_id).capitalize() + '_~_' + target_schema.capitalize() + '_~_' + object_type_name + '_Single_File_Output_{}.sql'.format(
                                datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                        deployment_query_output = create_deployment_file(project_connection, iteration_id, target_connection_id,
                                                                         target_schema, object_type_name)

                        deployment_query_output_pd = pd.DataFrame(deployment_query_output)
                        if len(deployment_query_output_pd):
                            deployment_text_output_data = deployment_query_output_pd.loc[:, 0].tolist()
                            deployment_text_output_data = [i.replace(',', ',\n') for i in deployment_text_output_data]
                            target_output = '\n\n\n'.join(deployment_text_output_data)
                            if target_output != '':
                                with open(deployment_file_name, 'a') as f:
                                    f.write("\n\n\n{}".format(target_output))

                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Completed', None)
            except Exception as error:
                print(f"Error occurred at deployment file creation : {str(error)}")

                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Error', str(error))
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')