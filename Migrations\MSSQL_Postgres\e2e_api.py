import os, sys, requests, json
from import_file import import_file
from common_modules.api import api_authentication


def e2e_api_trigger(process_type, project_id, migration_name, iteration_id, source_connection_id, dr_connection_id,
                    target_connection_id, schema_name, target_schema, table_name, mig_operation, data_load_type,
                    cdc_load_type, request_cpu, limit_cpu, file_name,cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    print(f'Performing E2E Api Call')
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        connection_url = getattr(import_object, 'Connection_URL')

        url = connection_url + 'Common/Assess/TriggerE2EMigration'
        payload = {
            "process_type": process_type,
            "task": mig_operation,
            "projectId": project_id,
            "mig_name": migration_name,
            "iteration_id": iteration_id,
            "object_category": 'Storage_Objects',
            "object_type": 'All',
            "source_connection_id": source_connection_id,
            "dr_connection_id": dr_connection_id,
            "target_connection_id": target_connection_id,
            "schema": schema_name,
            "target_schema": target_schema,
            "restart_flag": 'True',
            "mig_operation": mig_operation,
            "table_name": table_name,
            "request_cpu": request_cpu,
            "limit_cpu": limit_cpu,
            "data_load_type": data_load_type,
            "cdc_load_type": cdc_load_type,
            "file_name": file_name,
            "cloud_category": cloud_category,
        }
        token_data = api_authentication()
        headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        status_code = response.status_code
        print(status_code)
        if status_code == 200:
            print(f"{migration_name} E2E API successfully Triggered")
        else:
            raise Exception(f"{migration_name}: Request Failed while calling E2E Migration API")
