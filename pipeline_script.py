import os, requests, json, argparse

application_url = 'https://dvqcam.qmigrator.ai/api/v1/'


def api_authentication():
    url_login = str(application_url) + 'ServiceAccount/Login?'
    params = {
        "username": "qmig"
    }
    response = requests.post(url_login, params=params)
    status_code = response.status_code
    status_response = {'status_code': status_code, 'text': response.text}
    if status_response['status_code'] == 200:
        data = json.loads(status_response['text'])
        token_data = data['token']
        return token_data
    else:
        raise Exception("Invalid User Request Failed Please Try Again", status_response)


def api_encrypt(token_data, data, migid):
    url = str(application_url) + 'Client/EncryptForPython'
    payload = {
        'migid': str(migid),
        'plaintext': data
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    status_response = {'status_code': response.status_code, 'text': response.text}
    resp = json.loads(status_response['text'])['message']
    return resp


def api_decrypt(token_data, data, migid):
    url = str(application_url) + 'Client/DecryptForPython'
    payload = {
        'migid': str(migid),
        'encryptedtext': data
    }
    headers = {'content-type': 'application/json', 'Authorization': 'Bearer ' + token_data}
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    status_response = {'status_code': response.status_code, 'text': response.text}
    resp = json.loads(status_response['text'])['message']
    return resp


def encrypt_file(file_path, migid):
    with open(file_path, 'r') as original_file:
        original = original_file.read()
    encrypted = api_encrypt(token_data, original, migid)
    with open(file_path, 'w') as encrypted_file:
        encrypted_file.write(encrypted)


def decrypt_file(file_path, migid):
    with open(file_path, 'r') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = api_decrypt(token_data, encrypted, migid)
    local_root_path = os.path.dirname(os.path.realpath(__file__))
    with open(local_root_path + '/' + file_path, 'w') as decrypted_file:
        decrypted_file.write(decrypted)


parser = argparse.ArgumentParser()

parser.add_argument('-mig_name', '--mig_name',
                    help="Provide Migration name", nargs='?', const='', default='')
parser.add_argument('-migid', '--migid',
                    help="Provide Migration id", nargs='?', const='', default='')

args = parser.parse_args()
migration_name = args.mig_name
migid = args.migid

token_data = api_authentication()

mig_folder_path = 'Migrations' + '/' + migration_name
migration_files = ['object_conversion.py', 'validation.py','extract_schemas.py','e2e_api.py']
if os.path.exists(mig_folder_path):
    for file in migration_files:
        if os.path.isfile(mig_folder_path + '/' + file):
            encrypt_file(mig_folder_path + '/' + file, migid)

common_modules_folder_path = 'common_modules'
common_files = ['deploy_file.py', 'deploy_objects.py', 'deployment_file_creation.py', 'manual_conversion.py']
if os.path.exists(common_modules_folder_path):
    for common_file in common_files:
        if os.path.isfile(common_modules_folder_path + '/' + common_file):
            encrypt_file(common_modules_folder_path + '/' + common_file, migid)
