<Queries>
    <Extraction_Queries>
        <Code_Objects>
            <Procedure>
                <ListQuery>
                    SELECT
                    ROUTINENAME AS function_name, 'VALID' AS STATUS
                    --TEXT AS function_definition
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'P'
                    AND VALID = 'Y';
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    --ROUTINENAME AS procedure_name,
                    TEXT AS procedure_definition
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'P'
                    AND VALID = 'Y' AND upper(ROUTINENAME) = upper('@name');
                </DefinitionQuery>
            </Procedure>
            <Function>
                <ListQuery>
                    SELECT
                    ROUTINENAME AS function_name, 'VALID' AS STATUS
                    --TEXT AS function_definition
                    FROM SYSCAT.ROUTINES
                    WHERE ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'F'
                    AND VALID = 'Y';
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    --ROUTINENAME AS function_name,
                    TEXT AS function_definition
                    FROM SYSCAT.ROUTINES
                    WHERE upper(ROUTINESCHEMA) = upper('@schemaname')
                    AND ROUTINETYPE = 'F'
                    AND VALID = 'Y'AND upper(ROUTINENAME) = upper('@name');
                </DefinitionQuery>
            </Function>
            <Trigger>
                <ListQuery>
                    SELECT TRIGNAME AS object_name, 'VALID' STATUS
                    FROM SYSCAT.TRIGGERS WHERE TRIGSCHEMA = upper('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT TEXT AS trigger_definition
                    FROM SYSCAT.TRIGGERS
                    WHERE upper(TRIGSCHEMA) = UPPER('@schemaname') AND VALID = 'Y' AND lower(TRIGNAME)=lower('@name')
                </DefinitionQuery>
            </Trigger>
        </Code_Objects>
        <Storage_Objects>
<!--            <Sequence>-->
<!--                <ListQuery>-->
<!--                    SELECT  SEQNAME , 'VALID' AS STATUS   FROM SYSCAT.SEQUENCES-->
<!--                    WHERE SEQSCHEMA = upper('@schemaname')-->
<!--                    and SEQSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM');-->
<!--                </ListQuery>-->
<!--                <DefinitionQuery>-->
<!--                            SELECT-->
<!--                    'CREATE SEQUENCE ' || SEQSCHEMA || '.' || SEQNAME ||-->
<!--                    ' START WITH ' || START ||-->
<!--                    ' INCREMENT BY ' || INCREMENT ||-->
<!--                    CASE-->
<!--                    WHEN MINVALUE IS NOT NULL THEN ' MINVALUE ' || MINVALUE-->
<!--                    ELSE ''-->
<!--                    END ||-->
<!--                    CASE-->
<!--                    WHEN MAXVALUE IS NOT NULL THEN ' MAXVALUE ' || MAXVALUE-->
<!--                    ELSE ''-->
<!--                    END ||-->
<!--                    CASE-->
<!--                    WHEN CYCLE = 'Y' THEN ' CYCLE'-->
<!--                    ELSE ' NO CYCLE'-->
<!--                    END ||-->
<!--                    ' CACHE ' || CACHE || ';' AS DDL-->
<!--                    FROM SYSCAT.SEQUENCES-->
<!--                    WHERE SEQSCHEMA = upper('@schemaname')-->
<!--                    and SEQNAME = upper('@name')-->
<!--                    and SEQSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM');-->
<!--                </DefinitionQuery>-->

<!--            </Sequence>-->
            <Table>
                <ListQuery>
                    SELECT DISTINCT TABNAME AS OBJECT_NAME, 'VALID' STATUS
                    FROM SYSCAT.TABLES A
                    WHERE NOT EXISTS (
                    SELECT 1
                    FROM SYSCAT.TABLES MV
                    WHERE MV.TABNAME = A.TABNAME
                    AND MV.TABSCHEMA = upper('@schemaname')
                    AND MV.TYPE = 'M'
                    )
                    AND A.TABSCHEMA = upper('@schemaname')
                    AND A.TABNAME NOT LIKE '%$%'
                    AND A.TYPE = 'T'
                    AND A.TABNAME NOT LIKE 'SYS%'
                    ORDER BY 1 ;
                </ListQuery>
                <DefinitionQuery>
                    WITH TABLE_LIST AS (
                    SELECT T.TABSCHEMA, T.TABNAME
                    FROM SYSCAT.TABLES T
                    WHERE T.TABSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT', 'SYSIBMADM')
                    AND UPPER(T.TABSCHEMA) = UPPER('@schemaname')
                    AND upper(TABNAME) = UPPER('@name')
                    AND T.TYPE = 'T'
                    )
                    SELECT
                    'CREATE TABLE ' || TL.TABSCHEMA || '.' || TL.TABNAME || ' (' ||
                    LISTAGG(
                    '`' || C.COLNAME || '` ' || C.TYPENAME ||
                    CASE
                    WHEN C.TYPENAME IN ('CHAR', 'VARCHAR', 'CLOB', 'GRAPHIC', 'VARGRAPHIC', 'CHARACTER')
                    THEN '(' || RTRIM(CHAR(C.LENGTH)) || ')'
                    WHEN C.TYPENAME = 'DECIMAL'
                    THEN '(' || RTRIM(CHAR(C.LENGTH)) || ', ' || RTRIM(CHAR(C.SCALE)) || ')'
                    ELSE ''
                    END,
                    ', '
                    ) WITHIN GROUP (ORDER BY C.COLNO) || ');' AS ddl
                    FROM SYSCAT.COLUMNS C
                    JOIN TABLE_LIST TL
                    ON C.TABSCHEMA = TL.TABSCHEMA AND C.TABNAME = TL.TABNAME
                    GROUP BY TL.TABSCHEMA, TL.TABNAME;
                </DefinitionQuery>
            </Table>
            <Not_Null_Constraint>
                <ListQuery>
                    SELECT
                    upper(C.TABNAME) || '-' || upper(C.COLNAME) AS NOTNULL_CONSTRAINT_NAME,
                    'ENABLED' AS STATUS
                    FROM
                    SYSCAT.COLUMNS C
                    JOIN
                    SYSCAT.TABLES T
                    ON
                    C.TABSCHEMA = T.TABSCHEMA
                    AND C.TABNAME = T.TABNAME
                    WHERE
                    C.NULLS = 'N'
                    AND C.TABSCHEMA = upper('@schemaname')
                    AND T.TYPE = 'T'
                    ORDER BY
                    C.TABSCHEMA,
                    C.TABNAME,
                    C.COLNAME;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' || C.TABSCHEMA || '.' || C.TABNAME ||
                    ' ALTER COLUMN ' || C.COLNAME || ' ' || C.TYPENAME ||
                    CASE
                    WHEN C.TYPENAME IN ('CHARACTER', 'VARCHAR', 'GRAPHIC', 'VARGRAPHIC') THEN
                    '(' || C.LENGTH || ')'
                    WHEN C.TYPENAME IN ('DECIMAL', 'NUMERIC') THEN
                    '(' || C.LENGTH || ',' || C.SCALE || ')'
                    ELSE
                    ''
                    END ||
                    ' SET NOT NULL;' AS DDL_STATEMENT
                    FROM
                    SYSCAT.COLUMNS C
                    JOIN
                    SYSCAT.TABLES T
                    ON C.TABSCHEMA = T.TABSCHEMA
                    AND C.TABNAME = T.TABNAME
                    WHERE
                    C.NULLS = 'N'
                    AND C.TABSCHEMA = UPPER('@schemaname') -- Replace with your schema name
                    AND T.TYPE = 'T' -- Ensures only base tables are included
                    AND UPPER(C.TABNAME) || '-' || UPPER(C.COLNAME) = UPPER('@name');
                </DefinitionQuery>
            </Not_Null_Constraint>
            <Primary_Key>
                <ListQuery>
                    SELECT  upper(ac.tabname) || '-' || upper(ac.constname) ,'ENABLED' AS STATUS FROM syscat.tabconst ac
                    WHERE
                    ac.tabschema IN (UPPER('@schemaname'))
                    AND ac.type = 'P' AND
                    ac.constname NOT LIKE '%$%'
                    ORDER BY
                    ac.tabschema;
                </ListQuery>
                <DefinitionQuery>
                        SELECT
                    'ALTER TABLE ' || ac.tabschema || '.' || ac.tabname || ' ADD CONSTRAINT ' || ac.constname ||
                    ' PRIMARY KEY (' || ccl.col_list || ');'
                    FROM
                    syscat.tabconst ac
                    JOIN
                    (
                    SELECT
                    acc.tabschema AS owner,
                    acc.tabname AS table_name,
                    acc.constname AS constraint_name,
                    LISTAGG(acc.colname, ',') WITHIN GROUP (ORDER BY acc.colseq) AS col_list
                    FROM
                    syscat.keycoluse acc
                    GROUP BY
                    acc.tabschema, acc.tabname, acc.constname
                    ) ccl
                    ON
                    ac.tabschema = ccl.owner AND
                    ac.tabname = ccl.table_name AND
                    ac.constname = ccl.constraint_name
                    WHERE
                    ac.tabschema IN (UPPER('@schemaname')) AND
                    upper(ac.tabname) || '-' || upper(ac.constname) = upper('@name')
                    AND ac.type = 'P' AND
                    ac.constname NOT LIKE '%$%'
                    ORDER BY
                    ac.tabschema;
                </DefinitionQuery>
            </Primary_Key>
            <Unique_Constraint>
                <ListQuery>
                    SELECT     ac.tabname || '-' || ac.constname , 'VALID' AS Status
                    FROM
                    syscat.tabconst ac
                    JOIN
                    (
                    SELECT
                    acc.tabschema AS owner,
                    acc.tabname AS table_name,
                    acc.constname AS constraint_name,
                    LISTAGG(acc.colname, ',') WITHIN GROUP (ORDER BY acc.colseq) AS col_list
                    FROM
                    syscat.keycoluse acc
                    GROUP BY
                    acc.tabschema, acc.tabname, acc.constname
                    ) ccl
                    ON
                    ac.tabschema = ccl.owner AND
                    ac.tabname = ccl.table_name AND
                    ac.constname = ccl.constraint_name
                    WHERE
                    ac.tabschema = UPPER('@schemaname') AND
                    ac.type = 'U'
                    AND ac.constname NOT LIKE '%$%'
                    ORDER BY
                    ac.tabschema;
                </ListQuery>
                <DefinitionQuery>
                        SELECT  'ALTER TABLE ' || ac.tabschema || '.' || ac.tabname || ' ADD CONSTRAINT ' || ac.constname ||
                    ' UNIQUE (' || ccl.col_list ||  ');'
                    FROM
                    syscat.tabconst ac
                    JOIN
                    (
                    SELECT
                    acc.tabschema AS owner,
                    acc.tabname AS table_name,
                    acc.constname AS constraint_name,
                    LISTAGG(acc.colname, ',') WITHIN GROUP (ORDER BY acc.colseq) AS col_list
                    FROM
                    syscat.keycoluse acc
                    GROUP BY
                    acc.tabschema, acc.tabname, acc.constname
                    ) ccl
                    ON
                    ac.tabschema = ccl.owner AND
                    ac.tabname = ccl.table_name AND
                    ac.constname = ccl.constraint_name
                    WHERE
                    ac.tabschema IN (UPPER('@schemaname')) AND
                    ac.tabname || '-' || ac.constname = upper('@name')  --  ac.tabname || '-' || ac.constname name
                    AND ac.type = 'U' AND
                    ac.constname NOT LIKE '%$%'
                    ORDER BY
                    ac.tabschema;
                </DefinitionQuery>
            </Unique_Constraint>
            <Foreign_Key>
                <ListQuery>
                    WITH CTE AS (
                    SELECT
                    tc.tabname AS table_name,
                    tc.constname AS constraint_name,
                    tc.enforced AS status,
                    tc.tabschema as schema_name,
                    CAST('ALTER TABLE ' || tc.tabschema || '.' || tc.tabname || ' ADD CONSTRAINT ' || tc.constname ||
                    ' FOREIGN KEY (' AS VARCHAR(500)) ||
                    CAST((SELECT LISTAGG(kc.colname, ',') WITHIN GROUP (ORDER BY kc.colseq)
                    FROM syscat.keycoluse kc
                    WHERE kc.constname = tc.constname
                    AND kc.tabschema = tc.tabschema) AS VARCHAR(500)) ||
                    CAST(') REFERENCES ' AS VARCHAR(50)) ||
                    CAST(ref.tabschema AS VARCHAR(128)) || '.' || CAST(ref.reftabname AS VARCHAR(128)) ||
                    '(' ||
                    CAST((SELECT LISTAGG(kc2.colname, ',') WITHIN GROUP (ORDER BY kc2.colseq)
                    FROM syscat.keycoluse kc2
                    WHERE kc2.constname = ref.refkeyname
                    AND kc2.tabschema = ref.reftabschema) AS VARCHAR(500)) || ')' ||
                    ';' AS alter_statement
                    FROM
                    syscat.tabconst tc
                    JOIN
                    syscat.references ref ON tc.constname = ref.constname AND tc.tabschema = ref.tabschema
                    WHERE
                    tc.tabschema = UPPER('@schemaname')
                    AND tc.constname NOT LIKE '%$%'
                    AND tc.tabname NOT LIKE '%$%'
                    AND tc.type = 'F'
                    )
                    SELECT DISTINCT
                    upper(CTE.TABLE_NAME)||'-'||upper(CTE.constraint_name) AS FOREIGN_CONSTRAINT_NAME,status
                    FROM CTE
                    where  cte.schema_name = upper('@schemaname')


                </ListQuery>
                <DefinitionQuery>
                    WITH CTE AS (
                    SELECT
                    tc.tabname AS table_name,
                    tc.constname AS constraint_name,
                    tc.enforced AS status,
                    tc.tabschema as schema_name,
                    CAST('ALTER TABLE ' || tc.tabschema || '.' || tc.tabname || ' ADD CONSTRAINT ' || tc.constname ||
                    ' FOREIGN KEY (' AS VARCHAR(500)) ||
                    CAST((SELECT LISTAGG(kc.colname, ',') WITHIN GROUP (ORDER BY kc.colseq)
                    FROM syscat.keycoluse kc
                    WHERE kc.constname = tc.constname
                    AND kc.tabschema = tc.tabschema) AS VARCHAR(500)) ||
                    CAST(') REFERENCES ' AS VARCHAR(50)) ||
                    CAST(ref.tabschema AS VARCHAR(128)) || '.' || CAST(ref.reftabname AS VARCHAR(128)) ||
                    '(' ||
                    CAST((SELECT LISTAGG(kc2.colname, ',') WITHIN GROUP (ORDER BY kc2.colseq)
                    FROM syscat.keycoluse kc2
                    WHERE kc2.constname = ref.refkeyname
                    AND kc2.tabschema = ref.reftabschema) AS VARCHAR(500)) || ')' ||
                    ';' AS alter_statement
                    FROM
                    syscat.tabconst tc
                    JOIN
                    syscat.references ref ON tc.constname = ref.constname AND tc.tabschema = ref.tabschema
                    WHERE
                    tc.tabschema = UPPER('@schemaname')
                    AND tc.constname NOT LIKE '%$%'
                    AND tc.tabname NOT LIKE '%$%'
                    AND tc.type = 'F'
                    )
                    SELECT DISTINCT
                    CTE.alter_statement
                    FROM CTE
                    where  cte.schema_name = upper('@schemaname')
                    and upper(CTE.table_name) || '-' || upper(CTE.constraint_name) = upper('@name');

                </DefinitionQuery>


            </Foreign_Key>
            <Default_Constraint>
                <ListQuery>
                    SELECT TABNAME || '-' || COLNAME,'ENABLED' AS STATUS
                    FROM
                    SYSCAT.COLUMNS
                    WHERE
                    DEFAULT IS NOT NULL
                    AND TABSCHEMA = upper('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' || C.TABSCHEMA || '.' || C.TABNAME || ' MODIFY COLUMN ' || COLNAME || ' ' || TYPENAME || case
                    when C.TYPENAME in ('CHAR', 'VARCHAR', 'GRAPHIC', 'VARGRAPHIC','NUMERIC','DECIMAL','VARBINARY','BINARY') then '(' || C.LENGTH || ')'
                    else ''
                    end  ||
                    CASE
                    WHEN C.DEFAULT IS NOT NULL THEN ' DEFAULT ' ||
                    CASE
                    WHEN C.DEFAULT = 'CURRENT TIMESTAMP' THEN 'CURRENT_TIMESTAMP'
                    ELSE C.DEFAULT
                    END
                    ELSE ''
                    END ||
                    CASE
                    WHEN C.NULLS = 'N' THEN ' NOT NULL'
                    ELSE ''
                    END  ||
                    ';' AS alter_statement
                    FROM
                    SYSCAT.COLUMNS C
                    JOIN
                    SYSCAT.TABLES T
                    ON
                    C.TABSCHEMA = T.TABSCHEMA
                    AND C.TABNAME = T.TABNAME
                    WHERE
                    (C.DEFAULT IS NOT NULL)
                    AND T.TYPE = 'T' -- Exclude views
                    AND C.TABSCHEMA = UPPER('@schemaname')
                    AND C.TABNAME || '-' || COLNAME = upper('@name');
                </DefinitionQuery>
            </Default_Constraint>
            <Check_Constraint>
                <ListQuery>
                    SELECT  ac.tabname || '-' || ac.constname,'ENABLED' AS STATUS
                    FROM
                    syscat.checks ac
                    JOIN
                    syscat.colchecks acc
                    ON
                    ac.tabschema = acc.tabschema AND
                    ac.tabname = acc.tabname AND
                    ac.constname = acc.constname
                    WHERE
                    ac.type = 'C' AND
                    ac.tabschema = UPPER('@schemaname') AND
                    (ac.tabname || '.' || acc.colname) NOT IN (
                    SELECT DISTINCT
                    tabname || '.' || colname
                    FROM
                    syscat.columns
                    WHERE
                    nulls = 'N'
                    ) AND
                    ac.constname NOT LIKE '%$%' AND
                    ac.tabname NOT LIKE '%$%'
                    ORDER BY
                    ac.tabschema;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' || ac.tabschema || '.' || ac.tabname || ' ADD CONSTRAINT ' || ac.constname ||
                    ' CHECK (' || ac.text || ');'
                    FROM
                    syscat.checks ac
                    JOIN
                    syscat.colchecks acc
                    ON
                    ac.tabschema = acc.tabschema AND
                    ac.tabname = acc.tabname AND
                    ac.constname = acc.constname
                    WHERE
                    ac.type = 'C' AND
                    ac.tabschema = UPPER('@schemaname') AND
                    ac.tabname || '-' || ac.constname = upper('@name') AND
                    (ac.tabname || '.' || acc.colname) NOT IN (
                    SELECT DISTINCT
                    tabname || '.' || colname
                    FROM
                    syscat.columns
                    WHERE
                    nulls = 'N'
                    ) AND
                    ac.constname NOT LIKE '%$%' AND
                    ac.tabname NOT LIKE '%$%'
                    ORDER BY
                    ac.tabschema;
                </DefinitionQuery>
            </Check_Constraint>
            <View>
                <ListQuery>
                    SELECT VIEWNAME,'ENABLED' AS STATUS  FROM SYSCAT.VIEWS
                    WHERE VIEWSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM')
                    and VIEWSCHEMA = upper('@schemaname') ;
                </ListQuery>
                <DefinitionQuery>
                    SELECT text FROM SYSCAT.VIEWS
                    WHERE VIEWSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM')
                    and VIEWNAME = upper('@name')
                    and VIEWSCHEMA = upper('@schemaname') ;

                </DefinitionQuery>
            </View>


        </Storage_Objects>
    </Extraction_Queries>
    <Database_Exclusion_Query>
        SELECT DISTINCT LOWER(COLNAME) AS name
        FROM SYSCAT.COLUMNS
        WHERE TABSCHEMA NOT LIKE 'SYS%'
        AND COLNAME IS NOT NULL
        UNION
        SELECT DISTINCT LOWER(PARMNAME) AS name
        FROM SYSCAT.ROUTINEPARMS
        WHERE
        ROUTINESCHEMA NOT LIKE 'SYS%'
        AND PARMNAME IS NOT NULL
        UNION
        SELECT DISTINCT LOWER(TABNAME) AS name
        FROM SYSCAT.TABLES
        WHERE TABSCHEMA NOT LIKE 'SYS%'
    </Database_Exclusion_Query>
    <Source_Schemas>
        SELECT SCHEMANAME
        FROM SYSCAT.SCHEMATA WHERE SCHEMANAME NOT LIKE 'SYS%'
        ORDER BY SCHEMANAME;
    </Source_Schemas>
    <Target_Schemas>
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys')
    </Target_Schemas>
</Queries>