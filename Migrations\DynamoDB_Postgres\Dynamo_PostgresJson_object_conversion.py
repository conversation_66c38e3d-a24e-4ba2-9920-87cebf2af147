import re, os, sys, pytz, subprocess
import xml.etree.ElementTree as ET
import pandas as pd
from datetime import datetime
from import_file import import_file
from cryptography.fernet import Fernet
from Encryption_modules.encrypt_decrypt import decrypt_file, delete_files_in_directory, decrypt_conversion_modules
from Common_modules.deploy_functions import deploy_object
from Common_modules.api import decrypt_database_details, api_authentication
from Common_modules.stored_procedures import exe_logging, run_info_insert, request_insert, request_update, \
    create_schema, target_objects_insertion, deploy_status_insertion, drop_table
from Common_modules.comments_handling import comments_release, replace_singlequote_comments, replace_arrow_comments, \
    sql_server_semicolon_identification, create_and_append_sqlfile_multiple


def pg_formatter(data):
    try:
        pg_format_path = 'pg_format'
        result = subprocess.run(
            [pg_format_path],
            input=data,
            capture_output=True,
            text=True
        )
        if result.returncode != 0:
            print(f"Error occurred at PG_Formatter: {result.stderr}")
            return data
        return result.stdout
    except Exception as e:
        print(f"Error occurred at PG_Formatter: {e}")
        return data


def decrypt_conversion_file(file_path, file_encryption_key, working_directory_path):
    f = Fernet(file_encryption_key)
    with open(file_path, 'rb') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = f.decrypt(encrypted)

    if not os.path.exists(working_directory_path):
        os.makedirs(working_directory_path)
    file_name = file_path.split('/')[-1]
    with open(working_directory_path + '/' + file_name, 'wb') as decrypted_file:
        decrypted_file.write(decrypted)


def replace_singlequote_comments_lower(data, single_quote_comment_dictionary):
    if len(single_quote_comment_dictionary):
        for key, value in single_quote_comment_dictionary.items():
            # data = re.sub(rf'\b{key.strip()}\b',value,data,flags=re.I)
            data = data.replace(key.lower().strip(), value, 1)
    else:
        data = data
    return data


def feature_execution_and(source_data, schema, folder_path, value, storage_category, working_directory_path):
    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    if '&' in str(value[2]):
        excel_list_all = str(value[2]).split('&')
        list_all = []
        for key in excel_list_all:
            if '~' in key:
                est = re.findall(r'~\S+', key)[0]
                key = key.replace(est, '').strip()
                list_all.append(key)
            else:
                key = key.strip()
                list_all.append(key)
        list_all = [i for i in list_all if i != '']
        status_list = []
        for key in list_all:
            if '*' in key or '%' in key or '.' in key:
                key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace(
                    '..',
                    '.').replace(
                    '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
                key = key.replace('(', '\(').replace('%', '.*?').replace(')', '\)').replace('.', '\.')
                key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
            else:
                ignore_list = [':=', ':']
                check_ignore_item = ['True' for i in ignore_list if i in key]
                if check_ignore_item:
                    key_pattern = r'\s*' + key + r'\s*'
                else:
                    key_pattern = r'\b' + key + r'\b'
                key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
        if 0 not in status_list:
            try:
                encrypt_decrypt_path = folder_path + value[6] + '/' + str(value[1]).lower() + '.py'
                encrypt_key = decrypt_conversion_modules()
                decrypt_conversion_file(encrypt_decrypt_path, encrypt_key, working_directory_path)
                import_object = import_file(working_directory_path + '/' + str(value[1]).lower() + '.py')
                delete_files_in_directory(working_directory_path)
                function_name = [i for i in dir(import_object) if i.lower() == str(value[1]).lower()][0]
                function_call = getattr(import_object, function_name.strip())
                output = function_call(source_data, schema)
                source_data = output
            except Exception as e:
                print('Error in conversion ' + str(e) + ' at module ' + str(value[1]).lower())
                source_data = source_data
    return source_data


def feature_execution_pipe(source_data, schema, folder_path, value, storage_category, working_directory_path):
    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    excel_list_any = str(value[2]).split('|')
    list_any = []
    for key in excel_list_any:
        if '~' in key:
            est = re.findall(r'~\S+', key)[0]
            key = key.replace(est, '').strip()
            list_any.append(key)
        else:
            key = key.strip()
            list_any.append(key)
    list_any = [i for i in list_any if i != '']
    status_list = []
    for key in list_any:
        key = key.strip()
        if '*' in key or '%' in key or '.' in key:
            key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace('..',
                                                                                                                  '.').replace(
                '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
            key = key.replace('(', '\(').replace('%', '.*?').replace(')', '\)').replace('.', '\.')
            key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
        else:
            ignore_list = [':=', ':']
            check_ignore_item = ['True' for i in ignore_list if i in key]
            if check_ignore_item:
                key_pattern = r'\s*' + key + r'\s*'
            else:
                key_pattern = r'\b' + key + r'\b'
            key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
    if any(i > 0 for i in status_list):
        try:
            encrypt_decrypt_path = folder_path + value[6] + '/' + str(value[1]).lower() + '.py'
            encrypt_key = decrypt_conversion_modules()
            decrypt_conversion_file(encrypt_decrypt_path, encrypt_key, working_directory_path)
            import_object = import_file(working_directory_path + '/' + str(value[1]).lower() + '.py')
            delete_files_in_directory(working_directory_path)
            function_name = [i for i in dir(import_object) if i.lower() == str(value[1]).lower()][0]
            function_call = getattr(import_object, function_name.strip())
            output = function_call(source_data, schema)
            source_data = output
        except Exception as e:
            print('Error in conversion ' + str(e) + ' at module ' + str(value[1]).lower())
    return source_data


def feature_execution(source_data, schema, folder_path, excel_data, complete_excel_data, storage_category,
                      working_directory_path):
    feature_execution_list = []
    for index, value in excel_data.iterrows():
        if value[3] == 'No Predecessor':
            if value[1] not in feature_execution_list:
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, storage_category,
                                                        working_directory_path)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value, storage_category,
                                                         working_directory_path)
                feature_execution_list.append(value[1])
            else:
                source_data = source_data
                feature_execution_list = feature_execution_list
        else:
            if value[3] in feature_execution_list:
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, storage_category,
                                                        working_directory_path)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value, storage_category,
                                                         working_directory_path)
                feature_execution_list.append(value[1])
            else:
                parent_feature_name = value[3]
                pred_excel_data = complete_excel_data[complete_excel_data['Feature_Name'] == parent_feature_name]
                source_data = feature_execution(source_data, schema, folder_path, pred_excel_data, complete_excel_data,
                                                storage_category,
                                                working_directory_path)
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, storage_category,
                                                        working_directory_path)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value, storage_category,
                                                         working_directory_path)
                feature_execution_list.append(value[3])
                feature_execution_list.append(value[1])
    return source_data


def build_common_object_list(objects_excel_data, object_path):
    object_path_list = []

    object_excel_data = objects_excel_data[objects_excel_data['Object_Id'] == object_path]
    object_path_list.append(object_path)

    if not object_excel_data.empty:
        linked_objects_value = str(object_excel_data.iloc[0]['Linked_Objects'])
        if linked_objects_value != 'nan':
            linked_objects_split = object_excel_data.iloc[0]['Linked_Objects'].split(',')
            if linked_objects_split:
                for path in linked_objects_split:
                    temp_list = build_common_object_list(objects_excel_data, path)
                    if temp_list:
                        object_path_list.extend(temp_list)
    return object_path_list


def pre_features_execution(source_data, schema, path_modules, object_path, excel_data, objects_excel_data,
                           storage_category, working_directory_path):
    object_path_list = build_common_object_list(objects_excel_data, str(object_path + '/' + 'Pre').replace('//', '/'))

    pre_excel_data = excel_data[excel_data['Object_Path'].isin(object_path_list)]
    if len(pre_excel_data) <= 0:
        output = source_data
    else:
        output = feature_execution(source_data, schema, path_modules, pre_excel_data, pre_excel_data, storage_category,
                                   working_directory_path)
    return output


def post_features_execution(source_data, schema, path_modules, object_path, excel_data, objects_excel_data,
                            storage_category, working_directory_path):
    object_path_list = build_common_object_list(objects_excel_data, str(object_path + '/' + 'Post').replace('//', '/'))

    post_excel_data = excel_data[excel_data['Object_Path'].isin(object_path_list)]
    if len(post_excel_data) <= 0:
        output = source_data
    else:
        output = feature_execution(source_data, schema, path_modules, post_excel_data, post_excel_data,
                                   storage_category,
                                   working_directory_path)
    return output


def tuple_execution(tuple, migration_name, schema, object_path, excel_data, rules_excel_data, objects_excel_data,
                    local_root_path, storage_category, working_directory_path):
    path_modules = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/'
    sys.path.append(path_modules)

    support_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'support_modules.py'
    pre_output = pre_features_execution(tuple[1], schema, path_modules, object_path, excel_data, objects_excel_data,
                                        storage_category, working_directory_path)
    pre_output_split = pre_output.split('\n')
    pre_output_split = [re.sub(r' +', ' ', i) for i in pre_output_split]
    pre_output_split = [i.strip() for i in pre_output_split if i != '\n']
    pre_output_split = [i.strip() for i in pre_output_split if i != '']
    pre_output = '\n'.join(pre_output_split)

    current_objects_data = objects_excel_data[
        (objects_excel_data['Migration_Name'] == migration_name) & (
            objects_excel_data['Object_Id'].str.contains(pat=object_path))]

    if '/' in object_path:
        object_path_split = object_path.split('/')
        object_path_length = len(object_path_split)
        filtered_objects_data = pd.DataFrame(columns=current_objects_data.columns)
        for index, row in current_objects_data.iterrows():
            if len(row['Object_Id'].split('/')) == object_path_length:
                filtered_objects_data.loc[len(filtered_objects_data)] = row
    else:
        filtered_objects_data = current_objects_data[
            (current_objects_data['Migration_Name'] == migration_name) &
            (current_objects_data['Object_Id'] == object_path)]

    sys.path.append(support_module_path)
    import_object = import_file(support_module_path)
    create_object_tuples_function_call = getattr(import_object, 'create_object_tuples')

    comment_dict = {}
    if filtered_objects_data.iloc[0]['Object_Process_Style'] == 'Sequential':
        sub_object_names_list = []
        object_path = object_path + '/'
        objects_rules_data = objects_excel_data[
            (objects_excel_data['Migration_Name'] == migration_name) & (
                objects_excel_data['Object_Id'].str.contains(pat=object_path)) & ~(
                objects_excel_data['Object_Id'].str.contains(pat='Pre')) & ~(
                objects_excel_data['Object_Id'].str.contains(pat='Post'))]
        objects_rules_data = objects_rules_data.sort_values("Object_Execution_Order")

        object_path_split = object_path.split('/')
        object_path_split = [i for i in object_path_split if i != '']
        object_path_length = len(object_path_split)
        for index, row in objects_rules_data.iterrows():
            if len(row['Object_Id'].split('/')) == object_path_length + 1:
                sub_object_names_list.append(row['Object_Id'].split('/')[-1])

        for sub_object_name in sub_object_names_list:
            sub_object_path = object_path + sub_object_name
            sub_object_tuple_list, comment_dict, single_quote_comment_dictionary = create_object_tuples_function_call(
                pre_output,
                migration_name,
                sub_object_path,
                rules_excel_data,
                objects_excel_data)
            if sub_object_tuple_list:
                counter = 0
                for sub_tuple in sub_object_tuple_list:
                    sub_tuple_data = sub_tuple[1]
                    sub_tuple_data_split = sub_tuple_data.split('\n')
                    sub_tuple_data_split = [re.sub(r' +', ' ', i) for i in sub_tuple_data_split]
                    sub_tuple_data_split = [i.strip() for i in sub_tuple_data_split if i != '\n']
                    sub_tuple_data_split = [i.strip() for i in sub_tuple_data_split if i != '']
                    sub_tuple_data = '\n'.join(sub_tuple_data_split)

                    if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                        unique_marker = 'qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_US '
                        pre_output = pre_output.replace(sub_tuple_data, unique_marker, 1)
                        sub_output = tuple_execution(sub_tuple, migration_name, schema, sub_object_path,
                                                     excel_data, rules_excel_data, objects_excel_data, local_root_path,
                                                     storage_category, working_directory_path)
                        pre_output = str(pre_output).replace(unique_marker, '\n' + str(sub_output))
                    counter = counter + 1
                else:
                    pre_output = pre_output
            else:
                pre_output = pre_output

    elif filtered_objects_data.iloc[0]['Object_Process_Style'] == 'Mutually Exclusive':
        sub_object_path = object_path + '/'
        sub_object_tuple_list, comment_dict, single_quote_comment_dictionary = create_object_tuples_function_call(
            tuple[1],
            migration_name,
            sub_object_path,
            rules_excel_data,
            objects_excel_data)
        if sub_object_tuple_list:
            counter = 0
            for sub_tuple in sub_object_tuple_list:
                sub_tuple_data = sub_tuple[1]
                sub_tuple_data_split = sub_tuple_data.split('\n')
                sub_tuple_data_split = [re.sub(r' +', ' ', i) for i in sub_tuple_data_split]
                sub_tuple_data_split = [i.strip() for i in sub_tuple_data_split if i != '\n']
                sub_tuple_data_split = [i.strip() for i in sub_tuple_data_split if i != '']
                sub_tuple_data = '\n'.join(sub_tuple_data_split)
                if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                    unique_marker = 'qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_US '
                    pre_output = pre_output.replace(sub_tuple_data, unique_marker, 1)
                    current_object_path = object_path + '/' + str(sub_tuple[0])
                    sub_output = tuple_execution(sub_tuple, migration_name, schema, current_object_path, excel_data,
                                                 rules_excel_data, objects_excel_data, local_root_path,
                                                 storage_category, working_directory_path)
                    pre_output = pre_output.replace(unique_marker, '\n' + str(sub_output))
                counter = counter + 1
            else:
                pre_output = pre_output
        else:
            pre_output = pre_output
    else:
        pre_output = pre_output

    post_output = post_features_execution(pre_output, schema, path_modules, object_path, excel_data, objects_excel_data,
                                          storage_category, working_directory_path)
    post_output = comments_release(post_output, comment_dict)

    return post_output


def object_conversion(file_name, migration_name, schema, excel_data, rules_excel_data, rules_object_name,
                      object_name, objects_excel_data, local_root_path, storage_category, working_directory_path,
                      source_directory_path):
    object_name_file = file_name.split('/')[-1].strip().split('.')[0].strip().upper()
    with open(file_name, 'r') as file:
        source_data = file.read()
    source_data = source_data.replace('"', "'")  # for removing json double quotes with single quotes
    source_data = "".join([s for s in source_data.strip().splitlines(True) if s.strip()])

    source_data = re.sub(' +', ' ', source_data)

    support_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'support_modules.py'
    sys.path.append(support_module_path)
    import_object = import_file(support_module_path)
    create_object_tuples_function_call = getattr(import_object, 'create_object_tuples')

    db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
    sys.path.append(db_module_path)

    object_path = rules_object_name
    object_tuple_list = create_object_tuples_function_call(source_data,
                                                           migration_name,
                                                           object_path,
                                                           rules_excel_data,
                                                           objects_excel_data)

    final_output = ''
    for tuple in object_tuple_list:
        object_path = tuple[0]
        tuple_output = tuple_execution(tuple, migration_name, schema, object_path, excel_data, rules_excel_data,
                                       objects_excel_data, local_root_path, storage_category, working_directory_path)
        final_output = final_output + '\n' + tuple_output
    return final_output


def dynamo_postgresjson_conversion_trigger(migration_name, project_id, iteration_str, object_category,
                              object_type, storage_category,
                              schema, target_schema, source_connection_id, target_connection_id):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'Path_Config' + '/' + 'Config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, storage_category.capitalize() + '_Path')

        working_directory_path = getattr(import_object, storage_category.capitalize() + '_Directory_Path')
        sys.path.append(working_directory_path)

        objects_list = []
        code_objects_list = []
        storage_objects_list = []

        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'extraction_queries.xml'
        tree = ET.parse(xml_path)
        root = tree.getroot()
        code_objects_tags = root.find('Extraction_Queries/Code_Objects')
        if code_objects_tags != None:
            for code_tag in code_objects_tags.iter():
                code_objects_list.append(code_tag.tag)
        code_objects_list = [i for i in code_objects_list if
                             i not in ['Code_Objects', 'ListQuery', 'DefinitionQuery', 'Converted_Operational_Reports']]

        storage_objects_tags = root.find('Extraction_Queries/Storage_Objects')
        if storage_objects_tags != None:
            for storage_tag in storage_objects_tags.iter():
                storage_objects_list.append(storage_tag.tag)
        storage_objects_list = [i for i in storage_objects_list if
                                i not in ['Storage_Objects', 'ListQuery', 'DefinitionQuery',
                                          'Converted_Operational_Reports', 'QueryUpperVersion',
                                          'QueryLowerVersion']]

        if object_category == 'Code_Objects':
            objects_list = code_objects_list
        elif object_category == 'Storage_Objects':
            objects_list = storage_objects_list
        elif object_category == 'All':
            objects_list = storage_objects_list + code_objects_list

        if object_type.capitalize() == 'All':
            objects_list = objects_list
        else:
            objects_list = str(object_type).replace('"', '').replace("'", '').strip().split(',')

        if object_category.capitalize() == 'All':
            object_category_folder = 'All_Objects'
        else:
            object_category_folder = object_category

        path_excel = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '.csv'
        excel_data = pd.read_csv(path_excel)

        rules_excel = local_root_path + '/' + 'Dynamic_Rules' + '/' + migration_name + '/' + migration_name + '.csv'
        rules_excel_data = pd.read_csv(rules_excel)

        objects_excel = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '_objects_data.csv'
        objects_excel_data = pd.read_csv(objects_excel)

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        token_data = api_authentication()

        sys.path.append(db_module_path)
        import_object = import_file(db_module_path)

        project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
        application_function_call = getattr(import_object, 'connect_database')
        application_conn = application_function_call(project_DB_details)

        source_DB_details = decrypt_database_details(token_data, project_id, 'Source', source_connection_id)
        source_db_name = source_DB_details['db_name']
        target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)
        target_db_name = target_DB_details['db_name']
        target_function_call = getattr(import_object, 'target_DB_connection')

        request_data = request_insert(application_conn, source_connection_id, schema, 'Conversion', object_type,
                                      object_category, iteration_str)
        request_id = request_data[0][1]

        iteration_id = run_info_insert(application_conn, iteration_str, schema, 'All'.capitalize(), 'Conversion',
                                       object_category, source_connection_id)

        exe_logging(application_conn,
                    object_category_folder + ' Conversion started for ' + schema.capitalize() + ' Schema',
                    iteration_id)
        single_file_output_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + 'Single_File_Output'
        if not os.path.exists(single_file_output_path):
            os.makedirs(single_file_output_path)

        output_deployment_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + str(
            iteration_id) + '/' + 'Deployment_Logs'
        if not os.path.exists(output_deployment_path):
            os.makedirs(output_deployment_path)

        execution_log_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + str(
            iteration_id) + '/' + 'Execution_Logs' + '/' + 'Conversion'
        if not os.path.exists(execution_log_path):
            os.makedirs(execution_log_path)

        execution_file_path = execution_log_path + '/' + str(
            source_db_name).capitalize() + '_' + schema.capitalize() + '_' + str(
            object_category) + '_' + str(object_type) + '_execution_log_{}.txt'.format(
            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

        source_directory_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + str(
            iteration_id) + '/' + 'Source' + '/' + schema.capitalize()
        target_directory_path = root_path + '/' + 'PRJ' + str(project_id) + 'SRC' + '/' + str(
            iteration_id) + '/' + 'Conversion' + '/' + schema.capitalize()

        for object_name in objects_list:
            rules_object_name = object_name

            object_rules_data = rules_excel_data[(rules_excel_data['Migration_Name'] == migration_name) & (
                    rules_excel_data['Object_Path'] == rules_object_name)]
            if not object_rules_data.empty:
                source_path = source_directory_path + '/' + object_name
                if os.path.exists(source_path):
                    object_files_list = os.listdir(source_path)
                    object_files_list = [source_path + '/' + file_i for file_i in object_files_list]

                    if not os.path.exists(single_file_output_path + '/' + object_name):
                        os.makedirs(single_file_output_path + '/' + object_name)
                    single_file_output_file_path = single_file_output_path + '/' + object_name + '/' + str(
                        iteration_id) + '_' + str(
                        target_db_name).capitalize() + '_~_' + target_schema.capitalize() + '_~_' + object_name + '_Single_File_Output_{}.sql'.format(
                        datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                    if not os.path.exists(output_deployment_path + '/' + object_name):
                        os.makedirs(output_deployment_path + '/' + object_name)
                    output_deployment_file_path = output_deployment_path + '/' + object_name + '/' + str(
                        target_db_name).capitalize() + '_' + schema.capitalize() + '_' + object_name + '_Deploy_Log_{}.log'.format(
                        datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                    for file_name in object_files_list:
                        print(file_name)
                        try:
                            file_output = object_conversion(file_name, migration_name, schema, excel_data,
                                                            rules_excel_data,
                                                            rules_object_name, object_name, objects_excel_data,
                                                            local_root_path,
                                                            storage_category, working_directory_path,
                                                            source_directory_path)

                            conversion_message = 'Conversion completed for ' + object_name + ' ' + str(
                                file_name.split('/')[-1])
                            with open(execution_file_path, 'a') as f:
                                f.write("\n{}".format(conversion_message))

                            # Need to add replace
                            if target_schema != '':
                                file_output = re.sub(rf'{schema}\.', target_schema + '.', file_output,
                                                     flags=re.IGNORECASE | re.DOTALL)

                            # Need to comment when running in local
                            file_output = pg_formatter(file_output)
                        except Exception as e:
                            print('Error in file conversion' + str(e) + ' at file ' + file_name.split('/')[-1])

                            with open(execution_file_path, 'a') as f:
                                f.write("\n{}".format(
                                    'Error in file conversion' + str(e) + ' at file ' + file_name.split('/')[-1]))

                            application_conn = application_function_call(project_DB_details)
                            exe_logging(application_conn,
                                        'Issue occurred at file conversion: ' + str(e) + ' at file ' +
                                        file_name.split('/')[-1], iteration_id)
                            file_output = ''

                        object_name_create = file_name.split('/')[-2]
                        object_file_name = file_name.split('/')[-1]
                        output_path = target_directory_path + '/' + object_name_create.capitalize()
                        if not os.path.exists(output_path):
                            os.makedirs(output_path)
                        create_and_append_sqlfile_multiple(output_path + '/' + object_file_name, file_output)

                        original_file_name = file_name.split('/')[-1].split('.')[0]
                        target_connection, error = target_function_call(target_DB_details)
                        deploy_error = deploy_object(target_connection, file_output)
                        status_msgs = ['table can have only one primary key', 'existing object', 'already NOT NULL',
                                       'already indexed', 'already exists', 'column default value expression',
                                       'Duplicate key name ', 'Multiple primary key defined']
                        if deploy_error != '' and all(
                                str(i).lower() not in str(deploy_error).lower() for i in status_msgs):
                            print('Error occurred at deployment of object ' + file_name.split('/')[
                                -1] + ': ' + str(deploy_error))
                            deploy_message = '{0} Error occurred at deployment of object ' + file_name.split('/')[
                                -1] + ': ' + str(deploy_error).format(
                                datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                            target_objects_insertion(application_conn, iteration_id, schema, target_schema,
                                                     object_name,
                                                     original_file_name,
                                                     file_output, False, source_connection_id, target_connection_id)
                            deploy_status_insertion(application_conn, iteration_id, target_schema, object_name,
                                                    original_file_name, 'Undeployed', str(deploy_error),
                                                    target_connection_id, '', None, None)
                        else:
                            deploy_message = ('{0} Deployment completed successfully for object ' + \
                                              file_name.split('/')[-1]).format(
                                datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))
                            print('Deployment completed successfully for object ' + file_name.split('/')[-1])
                            target_objects_insertion(application_conn, iteration_id, schema, target_schema,
                                                     object_name,
                                                     original_file_name,
                                                     file_output, True, source_connection_id, target_connection_id)

                            with open(single_file_output_file_path, 'a') as f:
                                f.write("\n\n\n{}".format(file_output))

                        with open(output_deployment_file_path, 'a') as f:
                            f.write("\n\n{}".format(deploy_message))
            with open(execution_file_path, 'a') as f:
                f.write("\n{}\n".format("Conversion completed for " + object_name))
                f.close()
        application_conn = application_function_call(project_DB_details)
        exe_logging(application_conn,
                    object_category_folder + ' Conversion ended for ' + schema.capitalize() + ' schema',
                    iteration_id)
        request_update(application_conn, request_id)
    else:
        print("Config file not found")
