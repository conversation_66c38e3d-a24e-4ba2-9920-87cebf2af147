<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
                <!--                <Type>-->
                <!--                    SELECT TYPE_NAME from SYSIBM.SQLUDTS-->
                <!--                    where TYPE_SCHEM = Upper('@schemaname')-->
                <!--                    order by TYPE_NAME-->
                <!--                </Type>-->
                <Sequence>
                   SELECT SEQSCHEMA ,SEQNAME,SEQID AS sequence_value
                   FROM SYSCAT.SEQUENCES
                     WHERE SEQSCHEMA = upper('@schemaname')
                     and SEQSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM') -->
                    ORDER BY SEQNAME;
                </Sequence>
                <Table>
                    SELECT TABSCHEMA,TABNAME,TYPE FROM SYSCAT.tables
                    WHERE OWNER NOT IN ('SYSIBM') AND TABSCHEMA=nvl('@schemaname',TABSCHEMA) AND TYPE='T';
                </Table>
                <Primary_Key>
                   SELECT
                    OWNER,
                    TABNAME,
                    OWNER || '-' || TABNAME || '-' || col_list AS CONSTRAINT_DETAILS,
                    col_list || '-' || CONSTNAME AS col_list,
                    'ENABLED' AS STATUS
                FROM
                    (
                    SELECT
                        C.TABSCHEMA AS OWNER,
                        C.TABNAME,
                        C.CONSTNAME,
                        LISTAGG(CC.COLNAME,
                        ', ') WITHIN GROUP (
                    ORDER BY
                        CC.COLSEQ) AS COL_LIST
                    FROM
                        SYSCAT.TABCONST C
                    JOIN SYSCAT.KEYCOLUSE CC ON
                        C.CONSTNAME = CC.CONSTNAME
                        AND C.TABSCHEMA = CC.TABSCHEMA
                        AND C.TABNAME = CC.TABNAME
                    WHERE
                        C.TYPE = 'P'
                        -- Primary Key constraints
                        AND C.TABSCHEMA = upper('@schemaname')
                        AND C.CONSTNAME NOT LIKE '%$%'
                        AND C.TABNAME NOT LIKE '%$%'
                    GROUP BY
                        C.TABSCHEMA,
                        C.TABNAME,
                        C.CONSTNAME,
                        C.ENFORCED ) AS PRIMARY_KEY_CONSTRAINTS
                ORDER BY
                    OWNER,
                    TABNAME;


                </Primary_Key>
                <Unique_Constraint>
                  SELECT
                    c.TABSCHEMA,
                    c.TABNAME,
                    c.CONSTNAME AS "constraint_name",
                    LISTAGG(cc.COLNAME,
                    ', ') WITHIN GROUP (
                ORDER BY
                    cc.COLSEQ) "COL_LIST",
                    CASE
                        WHEN c.ENFORCED = 'Y' THEN 'ENABLED'
                        ELSE 'DISABLED'
                    END AS "STATUS"
                FROM
                    SYSCAT.TABCONST c
                JOIN SYSCAT.KEYCOLUSE cc ON
                    c.CONSTNAME = cc.CONSTNAME
                    AND c.TABSCHEMA = cc.TABSCHEMA
                    AND c.TABNAME = cc.TABNAME
                WHERE
                    c.TYPE = 'U'
                    AND c.TABSCHEMA = UPPER('@schemaname')
                    AND c.CONSTNAME NOT LIKE '%$%'
                    AND c.TABNAME NOT LIKE '%$%'
                GROUP BY
                    c.TABSCHEMA,
                    c.TABNAME,
                    c.CONSTNAME,
                    c.ENFORCED

                </Unique_Constraint>
                <Foreign_Key>
                    SELECT
						C.TABSCHEMA,
						C.TABNAME AS TableName,
						C.COLNAME AS PrimaryKeyColumn,
						K.CONSTNAME AS ConstraintName,
						'ENABLED'
					FROM
						SYSCAT.KEYCOLUSE C
					JOIN
						SYSCAT.TABCONST K
					ON
						C.TABSCHEMA = K.TABSCHEMA
						AND C.TABNAME = K.TABNAME
						AND C.CONSTNAME = K.CONSTNAME
					WHERE
						C.TABSCHEMA = upper('@schemaname')
						AND K.TYPE = 'F';
                </Foreign_Key>
                <Not_Null_Constraint>
                     SELECT
                        C.TABSCHEMA,
                        upper(C.TABNAME) || '-' || upper(C.COLNAME) AS CONSTRAINT_NAME,
                        'ENABLED' AS STATUS
                    FROM
                        SYSCAT.COLUMNS C
                    JOIN SYSCAT.TABLES T ON
                        C.TABSCHEMA = T.TABSCHEMA
                        AND C.TABNAME = T.TABNAME
                    WHERE
                        C.NULLS = 'N'
                        AND C.TABSCHEMA = upper('@schemaname')
                        AND T.TYPE = 'T'
                    ORDER BY
                        C.TABSCHEMA,
                        C.TABNAME,
                        C.COLNAME

                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT
                                        AC.TABSCHEMA,
                                        AC.TABNAME AS TableName,
                                        ACC.COLNAME AS ColumnName,
                                        ACC.DEFAULT AS DefaultConstraint
                    FROM
                                        SYSCAT.COLUMNS ACC
                    JOIN
                                        SYSCAT.TABLES AC
                                    ON
                                        ACC.TABSCHEMA = AC.TABSCHEMA
                    AND ACC.TABNAME = AC.TABNAME
                    WHERE
                                    ACC.DEFAULT IS NOT NULL
                    AND AC.TABSCHEMA = UPPER('@schemaname')

                </Default_Constraint>
                <Check_Constraint>
                    SELECT
                        COL.TABSCHEMA,
                        COL.TABNAME AS TableName,
                        COL.COLNAME AS ColumnName,
                        CHK.CONSTNAME AS CheckConstraintName,
                        CHK.TEXT AS CheckConstraintDefinition,
                        'ENABLED' AS status
                    FROM
                        SYSCAT.COLUMNS COL
                    JOIN
                        SYSCAT.CHECKS CHK
                    ON
                        COL.TABSCHEMA = CHK.TABSCHEMA
                    AND COL.TABNAME = CHK.TABNAME
                    AND POSITION(COL.COLNAME IN CHK.TEXT) > 0
                    WHERE
                        COL.TABSCHEMA = UPPER('@schemaname')
                </Check_Constraint>
                <!--                <Index>-->
                <!--                     SELECT INDSCHEMA,INDNAME FROM SYSCAT.indexes-->
                <!--						WHERE OWNER NOT IN ('SYSIBM')-->
                <!--						AND INDSCHEMA=nvl($INDSCHEMA,INDSCHEMA);-->
                <!--                </Index>-->

                <View>
                    SELECT VIEWSCHEMA,VIEWNAME FROM SYSCAT.VIEWS
                    WHERE VIEWSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSCATADM', 'SYSSTAT','SYSIBMADM') -->
                    and VIEWSCHEMA = upper('@schemaname') ;
                </View>
                <!--                <Datatype>-->
                <!--						SELECT-->
                <!--							cols.TABSCHEMA AS "OWNER",-->
                <!--							cols.TABNAME AS "TABLE_NAME",-->
                <!--							cols.COLNAME AS "COLUMN_NAME",-->
                <!--							cols.TYPENAME AS "DATA_TYPE",-->
                <!--							CASE-->
                <!--								WHEN cols.TYPENAME = 'INTEGER' THEN '(' || cols.LENGTH || ')'-->
                <!--								WHEN cols.TYPENAME IN ('DOUBLE', 'BIGINT') THEN '(' || cols.LENGTH || ')'-->
                <!--								WHEN cols.TYPENAME IN ('VARCHAR', 'CHAR') THEN '(' || cols.LENGTH || ')'-->
                <!--								WHEN cols.TYPENAME = 'DECIMAL' AND cols.SCALE IS NOT NULL THEN '(' || cols.LENGTH || ',' || cols.SCALE || ')'-->
                <!--								WHEN cols.TYPENAME = 'DECIMAL' AND cols.SCALE IS NULL THEN '[Default]'-->
                <!--								WHEN cols.TYPENAME IN ('DATE', 'TIMESTAMP', 'CLOB', 'BLOB', 'XML', 'VARGRAPHIC', 'GRAPHIC') THEN '( )'-->
                <!--							END AS "COLUMN_SIZE",-->
                <!--							cols.COLNO AS "ORDINAL_POSITION",-->
                <!--							tabs.TYPE AS "TABLE_TYPE"-->
                <!--						FROM-->
                <!--							SYSCAT.COLUMNS cols-->
                <!--						JOIN-->
                <!--							SYSCAT.TABLES tabs-->
                <!--							ON cols.TABSCHEMA = tabs.TABSCHEMA AND cols.TABNAME = tabs.TABNAME-->
                <!--						WHERE-->
                <!--							cols.TABSCHEMA IN ('@schemaname')-->
                <!--							cols.TABSCHEMA,-->
                <!--							cols.TABNAME,-->
                <!--							cols.COLNO;-->
                <!--                </Datatype>-->


            </Storage>
            <Code>
<!--                <Code_Objects>-->
<!--                    SELECT 'Function' AS object_type, ROUTINENAME AS Code_Object_Name-->
<!--                    FROM SYSCAT.ROUTINES-->
<!--                    WHERE ROUTINESCHEMA = upper('@schemaname')-->
<!--                    AND ROUTINETYPE = 'F'-->
<!--                    AND VALID = 'Y'-->
<!--                    UNION-->
<!--                    SELECT 'Procedure' AS object_type,ROUTINENAME AS Code_Object_Name-->
<!--                    FROM SYSCAT.ROUTINES-->
<!--                    WHERE ROUTINESCHEMA = upper('@schemaname')-->
<!--                    AND ROUTINETYPE = 'P'-->
<!--                    AND VALID = 'Y';-->
<!--                </Code_Objects>-->
                <Procedure>
                    SELECT
                        'Procedure' AS object_type,
                        ROUTINENAME AS Code_Object_Name
                    FROM
                        SYSCAT.ROUTINES
                    WHERE
                        ROUTINESCHEMA = upper('@schemaname')
                        AND ROUTINETYPE = 'P'
                        AND VALID = 'Y';
                </Procedure>
                <Function>
                    SELECT
                    'Function' AS object_type,
                    ROUTINENAME AS Code_Object_Name
                FROM
                    SYSCAT.ROUTINES
                WHERE
                    ROUTINESCHEMA = upper('@schemaname')
                    AND ROUTINETYPE = 'F'
                    AND VALID = 'Y'
                </Function>
                <Trigger>
                    SELECT
                    TRIGSCHEMA AS trigger_schema,
                    TRIGNAME AS trigger_name,
                    TABNAME as Table_Name
                    FROM SYSCAT.TRIGGERS
                    WHERE TRIGSCHEMA = upper('@schemaname')
                    AND VALID = 'Y'
                    GROUP BY TRIGSCHEMA, TRIGNAME, TABNAME,TRIGTIME,GRANULARITY
                    ORDER BY TABNAME, TRIGNAME;
                </Trigger>
            </Code>
        </Source>
        <Target>
            <Storage>
<!--                <Type>-->
<!--                    select user_defined_type_name from information_schema.user_defined_types-->
<!--                    where user_defined_type_schema = lower('@schemaname') order by user_defined_type_name-->
<!--                </Type>-->
                <Sequence>
                    SELECT
                        /*+ PARALLEL(@degree) */
                        DISTINCT schemaname AS SCHEMA,
                        sequencename AS SEQUENCE,
                        last_value - 1 AS sequence_value
                    FROM
                        pg_sequences
                    WHERE
                        schemaname = lower('@schemaname')
                </Sequence>
                <Table>
                    SELECT table_schema,table_name ,table_type FROM information_schema.tables WHERE table_schema =
                    '@schemaname' AND table_type = 'BASE TABLE'
                </Table>
                <Primary_Key>
                    SELECT
                    /*+ PARALLEL(@degree) */
                    table_schema OWNER,
                    table_name TABNAME,
                    table_schema ||'-' || table_name || '-' || column_list as CONSTRAINT_DETAILS ,
                    column_list || '-' || constraint_name col_list ,
                    'ENABLED' AS status
                FROM
                    (
                    SELECT
                        /*+ PARALLEL(@degree) */
                        tc.table_schema,
                        tc.table_name,
                        tc.constraint_name,
                        string_agg(kcu.column_name,
                        ', '
                    ORDER BY
                        kcu.ordinal_position) AS column_list,
                        'ENABLED'
                    FROM
                        information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu ON
                        tc.constraint_name = kcu.constraint_name
                        AND tc.table_schema = kcu.table_schema
                        AND tc.table_name = kcu.table_name
                    WHERE
                        upper(tc.constraint_type) = 'PRIMARY KEY'
                        AND tc.table_schema = lower('@schemaname')
                    GROUP BY
                        tc.table_schema,
                        tc.table_name,
                        tc.constraint_name ) AS primary_keys
                WHERE
                    (table_schema || '.' || table_name) NOT IN (
                    SELECT
                        (cn.nspname || '.' || child.relname) AS Table_name
                    FROM
                        pg_inherits
                    JOIN pg_class child ON
                        pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON
                        pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON
                        parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON
                        child.relnamespace = cn.oid
                    WHERE
                        pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY
                        pn.nspname,
                        parent.relname,
                        cn.nspname,
                        child.relname)


                </Primary_Key>
                <Unique_Constraint>
                   SELECT
                    /*+ PARALLEL(@degree) */
                    table_schema TABSCHEMA,
                    table_name TABNAME,
                    constraint_name,
                    column_list COL_LIST,
                    'ENABLED' AS status
                FROM
                    (
                    SELECT
                        /*+ PARALLEL(@degree) */
                        tc.table_schema,
                        tc.table_name,
                        tc.constraint_name,
                        string_agg(kcu.column_name,
                        ', '
                    ORDER BY
                        kcu.ordinal_position) AS column_list,
                        'ENABLED'
                    FROM
                        information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu ON
                        tc.constraint_name = kcu.constraint_name
                        AND tc.table_schema = kcu.table_schema
                        AND tc.table_name = kcu.table_name
                    WHERE
                        upper(tc.constraint_type) = 'UNIQUE'
                        AND tc.table_schema = lower('@schemaname')
                    GROUP BY
                        tc.table_schema,
                        tc.table_name,
                        tc.constraint_name ) AS unique_constraints
                WHERE
                    (table_schema || '.' || table_name) NOT IN (
                    SELECT
                        (cn.nspname || '.' || child.relname) AS Table_name
                    FROM
                        pg_inherits
                    JOIN pg_class child ON
                        pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON
                        pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON
                        parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON
                        child.relnamespace = cn.oid
                    WHERE
                        pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY
                        pn.nspname,
                        parent.relname,
                        cn.nspname,
                        child.relname)

                </Unique_Constraint>
                <Foreign_Key>
                   with cte as (
                select
                    /*+ PARALLEL(@degree) */
                    conname as constraint_name,
                    conrelid::regclass as table_name,
                    a.attname as column_name,
                    confrelid::regclass as referenced_table,
                    fa.attname as referenced_column
                from
                    pg_constraint as c
                join pg_attribute as a on
                    a.attnum = any(c.conkey)
                    and a.attrelid = c.conrelid
                join pg_attribute as fa on
                    fa.attnum = any(c.confkey)
                    and fa.attrelid = c.confrelid)
                select
                    /*+ PARALLEL(@degree) */
                    split_part(table_name::regclass::varchar,
                    '.',
                    1) TABSCHEMA,
                    split_part(table_name::regclass::varchar,
                    '.',
                    2) TableName,
                    column_name PrimaryKeyColumn,
                    constraint_name ConstraintName,
                --	'FOREIGN KEY' || ' (' || column_name || ') ' || 'REFERENCES' || ' ' || referenced_table || ' (' || referenced_column || ');',
                    'ENABLED' Status
                from
                    cte
                where
                    split_part(table_name::regclass::varchar,
                    '.',
                    1) = lower('@schemaname')
                    and
                    table_name::regclass::varchar not in (
                    select
                        (cn.nspname || '.' || child.relname) as Table_name
                    from
                        pg_inherits
                    join pg_class child on
                        pg_inherits.inhrelid = child.oid
                    join pg_class parent on
                        pg_inherits.inhparent = parent.oid
                    join pg_namespace pn on
                        parent.relnamespace = pn.oid
                    join pg_namespace cn on
                        child.relnamespace = cn.oid
                    where
                        pn.nspname not in ('pg_catalog', 'information_schema')
                    order by
                        pn.nspname,
                        parent.relname,
                        cn.nspname,
                        child.relname )

                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT
                    /*+ PARALLEL(@degree) */
                    table_schema TABSCHEMA,
                    upper(table_name) || '-' || upper(column_name) AS CONSTRAINT_NAME,
                    'ENABLED' AS STATUS
                FROM
                    information_schema.columns
                WHERE
                    upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    AND table_schema || '.' || table_name NOT IN (
                    SELECT
                        (cn.nspname || '.' || child.relname) AS Table_name
                    FROM
                        pg_inherits
                    JOIN pg_class child ON
                        pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON
                        pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON
                        parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON
                        child.relnamespace = cn.oid
                    WHERE
                        pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY
                        pn.nspname,
                        parent.relname,
                        cn.nspname,
                        child.relname)
                ORDER BY
                    table_schema,
                    table_name,
                    column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                   select
                    /*+ PARALLEL(@degree) */
                    table_schema TABSCHEMA,
                    table_name TableName,
                    column_name ColumnName,
                    column_default DefaultConstraint
                from
                    information_schema.columns
                where
                    table_schema = lower('@schemaname')
                    and column_default is not null
                    and table_schema || '.' || table_name not in (
                    select
                        (cn.nspname || '.' || child.relname) as Table_name
                    from
                        pg_inherits
                    join pg_class child on
                        pg_inherits.inhrelid = child.oid
                    join pg_class parent on
                        pg_inherits.inhparent = parent.oid
                    join pg_namespace pn on
                        parent.relnamespace = pn.oid
                    join pg_namespace cn on
                        child.relnamespace = cn.oid
                    where
                        pn.nspname not in ('pg_catalog', 'information_schema')
                    order by
                        pn.nspname,
                        parent.relname,
                        cn.nspname,
                        child.relname)

                </Default_Constraint>
                <Check_Constraint>
                    with cte as(
                    select
                        /*+ PARALLEL(@degree) */
                        n.nspname as schema_name,
                        conname as constraint_name,
                        conrelid::regclass as table_name,
                        a.attname AS column_name,
                        regexp_replace(pg_get_constraintdef(c.oid), '^CHECK \((.+)\)$', '\1') AS check_expression,
                        'ENABLED'
                     FROM pg_constraint c
                        JOIN pg_namespace n ON n.oid = c.connamespace
                        LEFT JOIN pg_attribute a
                            ON a.attrelid = c.conrelid
                            AND a.attnum = ANY (c.conkey)
                    where
                        confrelid = 0
                        and contype = 'c'
                        and n.nspname = lower('@schemaname')
                    )
                    select
                        /*+ PARALLEL(@degree) */
                        split_part(table_name::text,
                        '.',
                        1) as TABSCHEMA,
                        split_part(table_name::text,
                        '.',
                        2) as TableName,
                        column_name ColumnName,
                        constraint_name as CheckConstraintName,
                        check_expression as CheckConstraintDefinition,
                        'ENABLED' as status
                    from
                        cte
                    where
                        schema_name || '.' || table_name::text not in (
                        select
                            (cn.nspname || '.' || child.relname) as Table_name
                        from
                            pg_inherits
                        join pg_class child on
                            pg_inherits.inhrelid = child.oid
                        join pg_class parent on
                            pg_inherits.inhparent = parent.oid
                        join pg_namespace pn on
                            parent.relnamespace = pn.oid
                        join pg_namespace cn on
                            child.relnamespace = cn.oid
                        where
                            pn.nspname not in ('pg_catalog', 'information_schema')
                        order by
                            pn.nspname,
                            parent.relname,
                            cn.nspname,
                            child.relname)

                </Check_Constraint>
                <!--                <Index>-->
                <!--                    SELECT-->
                <!--                    schema_name,-->
                <!--                    case when table_name is null or table_name = '' then idx_schema_name else table_name end as-->
                <!--                    table_name,-->
                <!--                    index_name,-->
                <!--                    index_columns,-->
                <!--                    pkey,-->
                <!--                    index_definition,-->
                <!--                    table_name||'-'||index_name postgresconcat-->
                <!--                    FROM-->
                <!--                    (-->
                <!--                    SELECT-->
                <!--                    ns.nspname AS schema_name,-->
                <!--                    split_part(idx.indrelid :: REGCLASS :: text,'.',1) AS idx_schema_name,-->
                <!--                    split_part(idx.indrelid :: REGCLASS :: text,'.',2) AS table_name,-->
                <!--                    ARRAY(SELECT pg_get_indexdef(idx.indexrelid, k + 1, TRUE) from generate_subscripts(idx.indkey, 1) AS-->
                <!--                    k ORDER BY k) AS index_columns,-->
                <!--                    case when indisprimary=true then 'Primary Key'-->
                <!--                    when indisprimary=false then 'Non Primary Key'-->
                <!--                    END pkey,-->
                <!--                    pg_get_indexdef(idx.indexrelid::regclass) index_definition,-->
                <!--                    i.relname AS index_name,-->
                <!--                    idx.indisunique AS is_unique,-->
                <!--                    idx.indisprimary AS is_primary,-->
                <!--                    am.amname AS index_type,-->
                <!--                    (idx.indexprs IS NOT NULL) OR (idx.indkey::int[] @> array[0]) AS is_functional-->
                <!--                    FROM pg_index AS idx-->
                <!--                    JOIN pg_class AS i-->
                <!--                    ON i.oid = idx.indexrelid-->
                <!--                    JOIN pg_am AS am-->
                <!--                    ON i.relam = am.oid-->
                <!--                    JOIN pg_namespace AS NS ON i.relnamespace = NS.OID-->
                <!--                    JOIN pg_user AS U ON i.relowner = U.usesysid-->
                <!--                    WHERE NOT nspname LIKE 'pg%' &#45;&#45; Excluding system tables-->
                <!--                    and nspname = lower('@schemaname')-->
                <!--                    )ind_details-->
                <!--                    ORDER BY 1,2,3-->
                <!--                </Index>-->
                <!--                <Synonym>-->
                <!--                    SELECT viewname-->
                <!--                    FROM pg_views-->
                <!--                    WHERE schemaname = lower('@schemaname')-->
                <!--                    order by viewname-->
                <!--                </Synonym>-->
                <View>
                    SELECT
                    /*+ PARALLEL(@degree) */
                    schemaname,
                    viewname
                    FROM
                    pg_views
                    WHERE
                    schemaname = lower('@schemaname')
                    ORDER BY
                    viewname
                </View>
                <!--                <Datatype>-->
                <!--                  SELECT-->
                <!--                       isc.table_schema AS "OWNER",-->
                <!--                       isc.table_name AS "TABLE_NAME",-->
                <!--                       isc.column_name AS "COLUMN_NAME",-->
                <!--                       isc.data_type AS "DATA_TYPE",-->
                <!--                       CASE-->
                <!--                           WHEN isc.data_type = 'integer' THEN '(' || numeric_precision || ',' || isc.numeric_scale || ')'-->
                <!--                           WHEN isc.data_type IN ('double precision', 'bigint') THEN '(' || numeric_precision || ')'-->
                <!--                           WHEN isc.data_type IN ('character varying', 'numeric', 'character') THEN '(' || character_maximum_length || ')'-->
                <!--                           WHEN isc.data_type = 'numeric'-->
                <!--                           AND isc.numeric_precision IS NULL THEN '[Default]'-->
                <!--                           WHEN isc.data_type IN ('date', 'timestamp without time zone', 'text', 'timestamp with time zone', 'ARRAY', 'USER-DEFINED', 'bytea', 'xml', 'uuid', 'oid' ) THEN '(' || ')'-->
                <!--                       END AS "COLUMN_SIZE",-->
                <!--                       isc.ordinal_position,-->
                <!--                   &#45;&#45;	isc.udt_name AS "User_Defined_DataType",-->
                <!--                       ist.table_type-->
                <!--                       &#45;&#45; , isc.character_maximum_length, isc.numeric_precision, isc.numeric_precision_radix, isc.numeric_scale, isc.datetime_precision-->
                <!--                   FROM-->
                <!--                       information_schema.tables ist,-->
                <!--                       information_schema.columns isc-->
                <!--                   WHERE-->
                <!--                       ist.table_schema = isc.table_schema-->
                <!--                       AND ist.table_name = isc.table_name-->
                <!--                       AND ist.table_schema IN ('@schema_name')-->
                <!--                   ORDER BY-->
                <!--                       isc.table_schema,-->
                <!--                       isc.table_name,-->
                <!--                       isc.ordinal_position-->
                <!--                </Datatype>-->
            </Storage>
            <Code>
<!--                <Code_Objects>-->
<!--                    Select res.* from (select routine_type, routine_name as Code_Object_Name from-->
<!--                    information_schema.routines-->
<!--                    where routine_type in-->
<!--                    (-->
<!--                    'FUNCTION'-->
<!--                    ,'PROCEDURE'-->
<!--                    )-->
<!--                    and lower(specific_schema) = lower('@schemaname')-->
<!--                    except-->
<!--                    select routine_type, routine_name as Code_Object_Name from information_schema.routines-->
<!--                    where routine_type in-->
<!--                    (-->
<!--                    'FUNCTION'-->
<!--                    ,'PROCEDURE'-->
<!--                    )-->
<!--                    and lower(specific_schema) = lower('@schemaname')-->
<!--                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name-->
<!--                </Code_Objects>-->
                <Procedure>
                    SELECT
                        /*+ PARALLEL(@degree) */
                        res.*
                    FROM
                        (
                        SELECT
                            /*+ PARALLEL(@degree) */
                            routine_type object_type,
                            routine_name AS Code_Object_Name
                        FROM
                            information_schema.routines
                        WHERE
                            routine_type IN ('PROCEDURE')
                            AND lower(specific_schema) = lower('@schemaname')
                    EXCEPT
                        SELECT
                            /*+ PARALLEL(@degree) */
                            routine_type,
                            routine_name AS Code_Object_Name
                        FROM
                            information_schema.routines
                        WHERE
                            routine_type IN ( 'FUNCTION' , 'PROCEDURE' )
                            AND lower(specific_schema) = lower('@schemaname')
                            AND lower(data_type) = lower('trigger') ) res
                    ORDER BY
                        res.Code_Object_Name

                </Procedure>
                <Function>
                    SELECT
                        /*+ PARALLEL(@degree) */
                        res.*
                    FROM
                        (
                        SELECT
                            /*+ PARALLEL(@degree) */
                            routine_type object_type,
                            routine_name AS Code_Object_Name
                        FROM
                            information_schema.routines
                        WHERE
                            routine_type IN ('FUNCTION')
                            AND lower(specific_schema) = lower('@schemaname')
                    EXCEPT
                        SELECT
                            /*+ PARALLEL(@degree) */
                            routine_type,
                            routine_name AS Code_Object_Name
                        FROM
                            information_schema.routines
                        WHERE
                            routine_type IN ( 'FUNCTION' , 'PROCEDURE' )
                            AND lower(specific_schema) = lower('@schemaname')
                            AND lower(data_type) = lower('trigger') ) res
                    ORDER BY
                        res.Code_Object_Name
                </Function>
                <Trigger>
                    SELECT upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')
                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name;
                </Trigger>
            </Code>
        </Target>
    </Validation_Queries>
</Queries>