trigger:
- Test

resources:
- repo: self

variables:
  dockerRegistryServiceConnection: 'Conversion_Test'
  containerRegistry: 'qmigtest.azurecr.io'
  dockerfilePath: '**/Dockerfile_Test'
  tag: '$(Build.BuildId)'
  vmImageName: 'ubuntu-latest'
  MIG_ID_Default: 'default_value'
  imageRepository: ''  # Default empty value

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)
    steps:

    # Display MIG before entering logic
    - script: |
        echo "MIG Name: $(MIG)"
      displayName: 'Display MIG value'

    # Set MIG_ID and imageRepository based on MIG value in a single script
    - script: |
        if [[ "$(MIG)" == "Oracle_Postgres14" ]]; then
          echo "Setting MIG_ID to 20"
          echo "Setting imageRepository to conversion_oracle_postgres"
          echo "##vso[task.setvariable variable=MIG_ID]20"
          echo "##vso[task.setvariable variable=imageRepository]conversion_oracle_postgres"
        elif [[ "$(MIG)" == "MSSQL_Oracle" ]]; then
          echo "Setting MIG_ID to 30"
          echo "Setting imageRepository to conversion_mssql_oracle"
          echo "##vso[task.setvariable variable=MIG_ID]30"
          echo "##vso[task.setvariable variable=imageRepository]conversion_mssql_oracle"
        elif [[ "$(MIG)" == "DB2_Mysql" ]]; then
          echo "Setting MIG_ID to 37"
          echo "Setting imageRepository to conversion_db2_mysql"
          echo "##vso[task.setvariable variable=MIG_ID]37"
          echo "##vso[task.setvariable variable=imageRepository]conversion_db2_mysql"
        elif [[ "$(MIG)" == "DB2_Postgres" ]]; then
          echo "Setting MIG_ID to 24"
          echo "Setting imageRepository to conversion_db2_postgres"
          echo "##vso[task.setvariable variable=MIG_ID]24"
          echo "##vso[task.setvariable variable=imageRepository]conversion_db2_postgres"
        elif [[ "$(MIG)" == "DynamoDB_CosmosNosql" ]]; then
          echo "Setting MIG_ID to 35"
          echo "Setting imageRepository to conversion_dynamodb_cosmosnosql"
          echo "##vso[task.setvariable variable=MIG_ID]35"
          echo "##vso[task.setvariable variable=imageRepository]conversion_dynamodb_cosmosnosql"
        elif [[ "$(MIG)" == "DynamoDB_Postgres" ]]; then
          echo "Setting MIG_ID to 34"
          echo "Setting imageRepository to conversion_dynamodb_postgres"
          echo "##vso[task.setvariable variable=MIG_ID]34"
          echo "##vso[task.setvariable variable=imageRepository]conversion_dynamodb_postgres"
        elif [[ "$(MIG)" == "Mariadb_Mysql" ]]; then
          echo "Setting MIG_ID to 31"
          echo "Setting imageRepository to conversion_maria_mysql"
          echo "##vso[task.setvariable variable=MIG_ID]31"
          echo "##vso[task.setvariable variable=imageRepository]conversion_maria_mysql"
        elif [[ "$(MIG)" == "Oracle_SQL" ]]; then
          echo "Setting MIG_ID to 28"
          echo "Setting imageRepository to conversion_oracle_sql"
          echo "##vso[task.setvariable variable=MIG_ID]28"
          echo "##vso[task.setvariable variable=imageRepository]conversion_oracle_sql"
        elif [[ "$(MIG)" == "Postgres_Postgres" ]]; then
          echo "Setting MIG_ID to 38"
          echo "Setting imageRepository to conversion_postgres_postgres"
          echo "##vso[task.setvariable variable=MIG_ID]38"
          echo "##vso[task.setvariable variable=imageRepository]conversion_postgres_postgres"
        elif [[ "$(MIG)" == "Oracle_Oracle" ]]; then
          echo "Setting MIG_ID to 39"
          echo "Setting imageRepository to conversion_oracle_oracle"
          echo "##vso[task.setvariable variable=MIG_ID]39"
          echo "##vso[task.setvariable variable=imageRepository]conversion_oracle_oracle"
        elif [[ "$(MIG)" == "Oracle_Fabrics" ]]; then
          echo "Setting MIG_ID to 40"
          echo "Setting imageRepository to conversion_oracle_fabrics"
          echo "##vso[task.setvariable variable=MIG_ID]40"
          echo "##vso[task.setvariable variable=imageRepository]conversion_oracle_fabrics"
        elif [[ "$(MIG)" == "Synapse_Fabrics" ]]; then
          echo "Setting MIG_ID to 48"
          echo "Setting imageRepository to conversion_synapse_fabrics"
          echo "##vso[task.setvariable variable=MIG_ID]48"
          echo "##vso[task.setvariable variable=imageRepository]conversion_synapse_fabrics"
        else
          echo "Setting MIG_ID to default_value"
          echo "Setting imageRepository to default"
          echo "##vso[task.setvariable variable=MIG_ID]$(MIG_ID_Default)"
          echo "##vso[task.setvariable variable=imageRepository]default"
        fi
      displayName: 'Set MIG_ID and imageRepository based on MIG value'

    # Display the variables after setting them
    - script: |
        echo "MIG Name: $(MIG)"
        echo "MIG ID: $(MIG_ID)"
        echo "Image Repository: $(imageRepository)"
      displayName: 'Display MIG, MIG_ID, and ImageRepository'

    # Docker Build
    - task: Docker@2
      displayName: Build
      inputs:
        command: build
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
        arguments: '--build-arg MIG=$(MIG) --build-arg MIG_ID=$(MIG_ID) --build-arg STG_ACC=$(STG_ACC) --build-arg STG_KEY=$(STG_KEY) --build-arg STG_SRC=$(STG_SRC) --build-arg STG_PATH_CONVERSIONFILES="Conversion_Modules/$(MIG)/*" --build-arg STG_PATH_DYNAMICRULES="Dynamic_Rules/$(MIG)/*" --build-arg GID=$(GID) --build-arg UID=$(UID)'

    # - task: AquaSecurityOfficial.trivy-official.custom-build-release-task.trivy@1
    #   displayName: Image Scan
    #   # enabled: False
    #   inputs:
    #     image: qmigtest.azurecr.io/$(imageRepository):$(tag)
    #     # severities: CRITICAL,HIGH,MEDIUM,LOW
    #     # loginDockerConfig: true
    #     ignoreUnfixed: true
    #     options: '--scanners vuln'
    #     exitCode: 0
    # - task: CmdLine@2
    #   inputs:
    #     script: |
    #       mkdir $(Build.ArtifactStagingDirectory)/reports
    #       cp /home/<USER>/work/_temp/trivy-results* $(Build.ArtifactStagingDirectory)/reports/
    #   condition: succeededOrFailed()

    # - task: PublishPipelineArtifact@1
    #   inputs:
    #     targetPath: '$(Build.ArtifactStagingDirectory)/reports'
    #     artifact: 'reports'
    #     publishLocation: 'pipeline'
    #   condition: succeededOrFailed()

    # Docker Push
    - task: Docker@2
      displayName: Push an image to container registry
      inputs:
        command: Push
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
