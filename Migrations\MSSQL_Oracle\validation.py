import os, sys, re, shutil
from datetime import datetime
import pandas as pd
from import_file import import_file
import xml.etree.ElementTree as ET
from common_modules.api import api_authentication, decrypt_database_details
from common_modules.common_functions import adjust_column_width
from common_modules.stored_procedures import request_insert, request_update, validation_insert


def validation_trigger(task_name, project_id, migration_name, iteration_id, object_category, schema_name, target_schema,
                       source_connection_id, target_connection_id, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    print(f'Performing {task_name} process')
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        if not os.path.exists(working_directory_path):
            os.makedirs(working_directory_path)

        code_objects_list, storage_objects_list = [], []
        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'validation_queries.xml'

        tree = ET.parse(xml_path)
        root = tree.getroot()
        storage_objects_tags = root.find('Validation_Queries/Source/Storage')
        for storage_tag in storage_objects_tags.iter():
            storage_objects_list.append(storage_tag.tag)

        storage_objects_list = [i for i in storage_objects_list if i not in ['Storage']]

        code_objects_tags = root.find('Validation_Queries/Source/Code')
        for code_tag in code_objects_tags.iter():
            code_objects_list.append(code_tag.tag)
        code_objects_list = [i for i in code_objects_list if
                             i not in ['Code']]

        objects_list = []
        if object_category == 'Code_Objects':
            objects_list = code_objects_list
        elif object_category == 'Storage_Objects':
            objects_list = storage_objects_list
        elif object_category == 'All':
            objects_list = storage_objects_list + code_objects_list

        if object_category.capitalize() == 'All':
            object_category_folder = 'All_Objects'
        else:
            object_category_folder = object_category

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            function_call = getattr(import_object, 'connect_database')

            source_DB_details = decrypt_database_details(token_data, project_id, 'Source', source_connection_id)
            source_function_call = getattr(import_object, 'DB_connection')

            target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)
            target_function_call = getattr(import_object, 'target_DB_connection')

            execute_function_call_list = getattr(import_object, 'execute_query')

            source_parallel_size = source_DB_details['parallelprocess']
            target_parallel_size = target_DB_details['parallelprocess']

            request_id = ''
            try:
                project_connection = function_call(project_DB_details)
                request_id = request_insert(project_connection, iteration_id, source_connection_id, 'Validation', object_category,
                                   schema_name, 'All')[0]

                project_folder = 'PRJ' + project_id + 'SRC'

                validation_reports_folder = root_path + '/' + project_folder + '/' + str(
                    iteration_id) + '/' + 'Reports' + '/' + 'Validation'
                if not os.path.exists(validation_reports_folder):
                    os.makedirs(validation_reports_folder)

                conversion_path = root_path + '/' + project_folder + '/' + str(
                    iteration_id) + '/' + 'Conversion' + '/' + schema_name.capitalize()
                if not os.path.exists(conversion_path):
                    os.makedirs(conversion_path)

                validation_report_path = validation_reports_folder + '/' + str(
                    iteration_id) + '_' + schema_name.capitalize() + '_' + object_category_folder + '_Validation_{}.xlsx'.format(
                    datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                excel_path = conversion_path + '/' + str(
                    iteration_id) + '_' + schema_name.capitalize() + '_' + object_category_folder + '_Validation.xlsx'

                temp_excel_path = working_directory_path + '/' + str(
                    iteration_id) + '_' + schema_name.capitalize() + '_' + object_category_folder + '_Validation.xlsx'

                source_name, target_name = 'MSSQL', 'Oracle'

                with pd.ExcelWriter(temp_excel_path, engine="openpyxl") as writer:
                    final_summary_objects_list = []
                    for object_type in objects_list:
                        try:
                            source_query_tag = 'Validation_Queries/Source/' + '/' + object_type
                            object_source_query = list(root.iterfind(source_query_tag))[0].text
                            object_source_query = object_source_query.replace('@schemaname', schema_name.upper()).replace(
                                '@order',
                                '').replace(
                                '@degree', str(source_parallel_size))

                            source_connection, error = source_function_call(source_DB_details)
                            source_object_output = execute_function_call_list(source_connection, object_source_query)

                            target_query_tag = 'Validation_Queries/Target/' + '/' + object_type
                            object_target_query = list(root.iterfind(target_query_tag))[0].text
                            object_target_query = object_target_query.replace('@schemaname', target_schema.upper()).replace(
                                '@order',
                                '').replace(
                                '@degree', str(target_parallel_size))

                            target_connection, error = target_function_call(target_DB_details)
                            target_object_output = execute_function_call_list(target_connection, object_target_query)

                            if object_type == 'Table':
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Schema_name', 'Table_Name', 'Table_Type'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Schema_name', 'Table_Name', 'Table_Type'])
                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                                source_data_df['Table_Type'] = source_data_df['Table_Type'].apply(
                                    lambda x: re.sub(r'\bt\b', 'table', str(x), flags=re.IGNORECASE))
                                target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})

                                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Table_Type'],
                                                        suffixes=('_Source', '_Target'), how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = source_data_df[
                                    ~source_data_df['Table_Name'].isin(matched_data['Table_Name'])]
                                target_data = target_data_df[
                                    ~target_data_df['Table_Name'].isin(matched_data['Table_Name'])]

                                source_only_data = pd.merge(source_data, target_data,
                                                            on=['Table_Name', 'Table_Type'],
                                                            suffixes=('_Source', '_Target'),
                                                            how='left', indicator=True)
                                source_only_data = source_only_data[source_only_data['_merge'] == 'left_only'].drop(
                                    columns=['_merge'])
                                source_only_data['Status'] = f"Available in {source_name} not in {target_name}"

                                target_only_data = pd.merge(source_data, target_data,
                                                            on=['Table_Name', 'Table_Type'],
                                                            suffixes=('_Source', '_Target'),
                                                            how='right', indicator=True)
                                target_only_data = target_only_data[target_only_data['_merge'] == 'right_only'].drop(
                                    columns=['_merge'])
                                target_only_data['Status'] = f"Available in {target_name} not in {source_name}"

                                result_df = pd.concat([matched_data, source_only_data, target_only_data], ignore_index=True)
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type == 'Partition':
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Schema_name', 'Table_Name', 'Partition_Name'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Schema_name', 'Table_Name', 'Partition_Name'])

                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                matched_data = pd.merge(source_data_df, target_data_df,
                                                        on=['Table_Name', 'Partition_Name'],
                                                        suffixes=('_Source', '_Target'), how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                                       suffixes=('_Source', '_Target'), how='left', indicator=True)
                                source_data = source_data[source_data['_merge'] == 'left_only']
                                source_data['Status'] = f"Available in {source_name} not in {target_name}"

                                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Partition_Name'],
                                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                                target_data = target_data[target_data['_merge'] == 'right_only']
                                target_data['Status'] = f"Available in {target_name} not in {source_name}"

                                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                                result_df = result_df.drop(columns=['_merge'], errors='ignore')
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type in ['Primary_Key', 'Foreign_Key', 'Unique_Constraint']:
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Schema_Name', 'Table_Name', object_type,
                                                                       'Source_Column_Name',
                                                                       'Source_' + object_type + '_Status'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Schema_Name', 'Table_Name', object_type,
                                                                       'Target_Column_Name',
                                                                       'Target_' + object_type + '_Status'])

                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                                        suffixes=('_Source', '_Target'),
                                                        how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                                       suffixes=('_Source', '_Target'),
                                                       how='left', indicator=True)
                                source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                                    columns=['Target_Column_Name', 'Target_' + object_type + '_Status', '_merge'])
                                source_data['Status'] = f"Available in {source_name} not in {target_name}"
                                source_data['Target_Column_Name'] = ' '
                                source_data['Target_' + object_type + '_Status'] = ' '

                                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                                       suffixes=('_Source', '_Target'),
                                                       how='right', indicator=True)
                                target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                                    columns=['Source_Column_Name', 'Source_' + object_type + '_Status', '_merge'])
                                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                                target_data['Source_Column_Name'] = ' '
                                target_data['Source_' + object_type + '_Status'] = ' '
                                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                                result_df.rename(columns={'_merge': 'Status'}, inplace=True, errors='ignore')
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type == 'Check_Constraint':
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Schema_Name', 'Table_Name', object_type,
                                                                       'Source_Constraint_Name',
                                                                       'Source_' + object_type + '_Status'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Schema_Name', 'Table_Name', object_type,
                                                                       'Target_Constraint_Name',
                                                                       'Target_' + object_type + '_Status'])

                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                                        suffixes=('_Source', '_Target'),
                                                        how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                                       suffixes=('_Source', '_Target'),
                                                       how='left', indicator=True)
                                source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                                    columns=['Target_Constraint_Name', 'Target_' + object_type + '_Status', '_merge'])
                                source_data['Status'] = f"Available in {source_name} not in {target_name}"
                                source_data['Target_Constraint_Name'] = ' '
                                source_data['Target_' + object_type + '_Status'] = ' '

                                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', object_type],
                                                       suffixes=('_Source', '_Target'),
                                                       how='right', indicator=True)
                                target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                                    columns=['Source_Constraint_Name', 'Source_' + object_type + '_Status', '_merge'])
                                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                                target_data['Source_Constraint_Name'] = ' '
                                target_data['Source_' + object_type + '_Status'] = ' '
                                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                                result_df.rename(columns={'_merge': 'Status'}, inplace=True, errors='ignore')
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type == "Default_Constraint":
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                                       'Source_Column_Name'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Schema_Name', 'Table_Name', 'Constraint_Name',
                                                                       'Target_Column_Name'])
                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                matched_data = pd.merge(source_data_df, target_data_df,
                                                        on=['Table_Name', 'Constraint_Name'],
                                                        suffixes=('_Source', '_Target'), how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                                       suffixes=('_Source', '_Target'), how='left', indicator=True)
                                source_data = source_data[source_data['_merge'] == 'left_only']
                                source_data['Status'] = f"Available in {source_name} not in {target_name}"
                                source_data['Target_Column_Name'] = ' '

                                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Constraint_Name'],
                                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                                target_data = target_data[target_data['_merge'] == 'right_only']
                                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                                target_data['Source_Column_Name'] = ' '

                                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                                result_df = result_df.drop(columns=['_merge'], errors='ignore')
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type == "Not_Null_Constraint":

                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Schema_name', 'Constraint_Name',
                                                                       'Constraint_Value'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Schema_name', 'Constraint_Name',
                                                                       'Constraint_Value'])
                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                matched_data = pd.merge(source_data_df, target_data_df,
                                                        on=['Constraint_Name', 'Constraint_Value'],
                                                        suffixes=('_Source', '_Target'), how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = pd.merge(source_data_df, target_data_df,
                                                       on=['Constraint_Name', 'Constraint_Value'],
                                                       suffixes=('_Source', '_Target'), how='left', indicator=True)
                                source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                                    columns=['Schema_name_Target',
                                             '_merge'])
                                source_data['Status'] = f"Available in {source_name} not in {target_name}"

                                target_data = pd.merge(source_data_df, target_data_df,
                                                       on=['Constraint_Name', 'Constraint_Value'],
                                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                                target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                                    columns=['Schema_name_Source',
                                             '_merge'])
                                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type == 'View':
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Schema_Name', 'Table_Name'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Schema_Name', 'Table_Name'])
                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                                        suffixes=('_Source', '_Target'),
                                                        how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                                       suffixes=('_Source', '_Target'),
                                                       how='left', indicator=True)
                                source_data = source_data[source_data['_merge'] == 'left_only'].drop(columns=['_merge'])
                                source_data['Status'] = f"Available in {source_name} not in {target_name}"
                                source_data['Schema_Name_Target'] = ' '

                                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name'],
                                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                                target_data = target_data[target_data['_merge'] == 'right_only'].drop(columns=['_merge'])
                                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                                target_data['Schema_Name_Source'] = ' '

                                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                                result_df = result_df.drop(columns=['_merge'], errors='ignore')
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type == "Sequence":
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Source_Schema_Name', 'Sequence_Name',
                                                                       'Source_Sequence_Value'])

                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Target_Schema_Name', 'Sequence_Name',
                                                                       'Target_Sequence_Value'])

                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                matched_data = pd.merge(source_data_df, target_data_df, on='Sequence_Name',
                                                        suffixes=('_Source', '_Target'), how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = pd.merge(source_data_df, target_data_df, on='Sequence_Name',
                                                       suffixes=('_Source', '_Target'), how='left', indicator=True)
                                source_data = source_data[source_data['_merge'] == 'left_only']
                                source_data['Status'] = f"Available in {source_name} not in {target_name}"

                                target_data = pd.merge(source_data_df, target_data_df, on='Sequence_Name',
                                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                                target_data = target_data[target_data['_merge'] == 'right_only']
                                target_data['Status'] = f"Available in {target_name} not in {source_name}"

                                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type == "Index":
                                source_data_df = pd.DataFrame(source_object_output)
                                if len(source_data_df):
                                    source_data_df = source_data_df.loc[:, [0, 1, 2, 3, 6]]
                                    source_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name',
                                                              'Source_Index_Column',
                                                              'Source_Index_DDL']
                                else:
                                    source_data_df = pd.DataFrame(
                                        columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Source_Index_Column',
                                                 'Source_Index_DDL'])

                                target_data_df = pd.DataFrame(target_object_output)
                                if len(target_data_df):
                                    target_data_df = target_data_df.loc[:, [0, 1, 2, 3, 6]]
                                    target_data_df.columns = ['Schema_Name', 'Table_Name', 'Index_Name',
                                                              'Target_Index_Column',
                                                              'Target_Index_DDL']
                                else:
                                    target_data_df = pd.DataFrame(
                                        columns=['Schema_Name', 'Table_Name', 'Index_Name', 'Target_Index_Column',
                                                 'Target_Index_DDL'])

                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df['Target_Index_Column'] = target_data_df['Target_Index_Column'].str.replace(
                                    "['", '', regex=False).replace("']", '', regex=False).replace('["', '',
                                                                                                  regex=False).replace('"]',
                                                                                                                       '',
                                                                                                                       regex=False)
                                target_data_df['Index_Name'] = target_data_df['Index_Name'].str.replace('___idx1$', '',
                                                                                                        case=False,
                                                                                                        regex=True)

                                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                                        suffixes=('_Source', '_Target'), how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                                       suffixes=('_Source', '_Target'), how='left', indicator=True)
                                source_data = source_data[source_data['_merge'] == 'left_only'].drop(
                                    columns=['Target_Index_Column', 'Target_Index_DDL', '_merge'])
                                source_data['Status'] = f"Available in {source_name} not in {target_name}"
                                source_data['Target_Index_Column'] = ' '
                                source_data['Target_Index_DDL'] = ' '

                                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Index_Name'],
                                                       suffixes=('_Source', '_Target'), how='right', indicator=True)
                                target_data = target_data[target_data['_merge'] == 'right_only'].drop(
                                    columns=['Source_Index_Column', 'Source_Index_DDL', '_merge'])
                                target_data['Status'] = f"Available in {target_name} not in {source_name}"
                                target_data['Source_Index_Column'] = ' '
                                target_data['Source_Index_DDL'] = ' '

                                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                                result_df = result_df.drop(columns=['_merge'], errors='ignore')
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type == 'Trigger':
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Schema_Name', 'Table_Name', 'Trigger_Name'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Schema_Name', 'Table_Name', 'Trigger_Name'])
                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                matched_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Trigger_Name'],
                                                        suffixes=('_Source', '_Target'),
                                                        how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Trigger_Name'],
                                                       suffixes=('_Source', '_Target'),
                                                       how='left')
                                source_data['Status'] = f"Available in {source_name} not in {target_name}"

                                target_data = pd.merge(source_data_df, target_data_df, on=['Table_Name', 'Trigger_Name'],
                                                       suffixes=('_Source', '_Target'),
                                                       how='right')
                                target_data['Status'] = f"Available in {target_name} not in {source_name}"

                                result_df = pd.concat([matched_data, source_data, target_data], ignore_index=True)
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type in ['Procedure', 'Function']:
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Object_Type', 'Object_Name'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Object_Type', 'Object_Name'])
                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                target_data_df = target_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                matched_data = pd.merge(source_data_df, target_data_df, on='Object_Name',
                                                        suffixes=('_Source', '_Target'),
                                                        how='inner')
                                matched_data['Status'] = f"Available in both {source_name} and {target_name}"

                                source_data = source_data_df[
                                    ~source_data_df['Object_Name'].isin(matched_data['Object_Name'])]
                                target_data = target_data_df[
                                    ~target_data_df['Object_Name'].isin(matched_data['Object_Name'])]

                                source_only_data = pd.merge(source_data, target_data,
                                                            on=['Object_Name'],
                                                            suffixes=('_Source', '_Target'),
                                                            how='left', indicator=True)
                                source_only_data = source_only_data[source_only_data['_merge'] == 'left_only'].drop(
                                    columns=['_merge'])
                                source_only_data['Status'] = f"Available in {source_name} not in {target_name}"

                                target_only_data = pd.merge(source_data, target_data,
                                                            on=['Object_Name'],
                                                            suffixes=('_Source', '_Target'),
                                                            how='right', indicator=True)
                                target_only_data = target_only_data[target_only_data['_merge'] == 'right_only'].drop(
                                    columns=['_merge'])
                                target_only_data['Status'] = f"Available in {target_name} not in {source_name}"

                                result_df = pd.concat([matched_data, source_only_data, target_only_data], ignore_index=True)
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)


                            elif object_type == 'Datatype':
                                datatypes_dict = {'IDENTITY': 'GENERATED ALWAYS AS IDENTITY',
                                                  'BIGINT': 'NUMBER',
                                                  'BIT': 'NUMBER',
                                                  'DECIMAL': 'NUMBER',
                                                  'DATETIME': 'TIMESTAMP(6)',
                                                  'IMAGE': 'LONG RAW',
                                                  'INT': 'NUMBER',
                                                  'MONEY': 'NUMBER',
                                                  'NVARCHAR': 'NVARCHAR2',
                                                  'VARCHAR': 'VARCHAR2',
                                                  'NUMERIC': 'NUMBER',
                                                  'FLOAT': 'FLOAT',
                                                  'REAL': 'FLOAT',
                                                  'SMALLDATETIME': 'DATE',
                                                  'SMALLMONEY': 'NUMBER',
                                                  'SMALLINT': 'NUMBER',
                                                  'TEXT': 'CLOB',
                                                  'BINARY': 'BLOB',
                                                  'TINYINT': 'NUMBER',
                                                  'XML': 'SYS.XMLTYPE',
                                                  'SYSNAME': 'VARCHAR2',
                                                  'getdate': 'sysdate',
                                                  'uniqueidentifier': 'varchar2',
                                                  'newid': 'sys_guid',
                                                  'flag': 'NUMBER',
                                                  'NVARCHAR(max)': 'VARCHAR2(4000)',
                                                  'VARCHAR(max)': 'VARCHAR2(4000)',
                                                  'varbinary': 'BLOB',
                                                  'decimal': 'NUMBER',
                                                  'ntext': 'NCLOB',
                                                  'datetimeoffset': 'TIMESTAMP',
                                                  'TIME': 'DATE',
                                                  'datetimeoffset': 'timestamp(6) with time zone'
                                                  }

                                datatypes_dict = {key.lower(): value.lower() for key, value in datatypes_dict.items()}
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Source_Schema', 'Table_Name', 'Column_Name',
                                                                       'Source_Datatype',
                                                                       'Source_Datatype_Length', 'Source_Index',
                                                                       'Table_Type'])
                                target_data_df = pd.DataFrame(target_object_output,
                                                              columns=['Target_Schema', 'Table_Name', 'Column_Name',
                                                                       'Target_Datatype',
                                                                       'Target_Datatype_Length', 'Target_Index',
                                                                       'Table_Type'])

                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)
                                target_data_df = target_data_df.applymap(
                                    lambda x: x.decode('utf-8').lower() if isinstance(x, bytes) else (
                                        x.lower() if isinstance(x, str) else x))
                                source_data_df['Table_Type'] = source_data_df['Table_Type'].replace({'base table': 'table'})
                                source_data_df['Table_Type'] = source_data_df['Table_Type'].apply(
                                    lambda x: re.sub(r'\bt\b', 'table', str(x), flags=re.IGNORECASE))
                                target_data_df['Table_Type'] = target_data_df['Table_Type'].replace({'base table': 'table'})

                                source_data_df['Source_Modified_Datatype'] = source_data_df['Source_Datatype'].replace(
                                    datatypes_dict)
                                source_data_df['Source_Datatype_Length'] = source_data_df[
                                    'Source_Datatype_Length'].str.replace('(', '', regex=False).str.replace(')', '',
                                                                                                            regex=False)
                                target_data_df['Target_Modified_Datatype'] = target_data_df['Target_Datatype']
                                target_data_df['Target_Datatype_Length'] = target_data_df[
                                    'Target_Datatype_Length'].str.replace('(', '', regex=False).str.replace(')', '',
                                                                                                            regex=False)

                                merged_df = pd.merge(source_data_df, target_data_df,
                                                     on=['Table_Name', 'Column_Name', 'Table_Type'], how='inner')
                                result_match = []
                                for index, row in merged_df.iterrows():
                                    if row['Source_Modified_Datatype'] == row['Target_Modified_Datatype'] and row[
                                        'Source_Index'] == \
                                            row[
                                                'Target_Index'] and row['Source_Datatype_Length'] == row[
                                        'Target_Datatype_Length']:
                                        status = 'Matched'
                                    elif (row['Target_Modified_Datatype'] == 'xmltype' and row[
                                        'Source_Modified_Datatype'] == 'sys.xmltype') and row[
                                        'Source_Index'] == row['Target_Index']:
                                        status = 'Matched'
                                    elif (row['Target_Modified_Datatype'] == 'varchar2' and row[
                                        'Target_Datatype_Length'] == '4000') and (
                                            row['Source_Modified_Datatype'] == 'nvarchar2' and row[
                                        'Source_Datatype_Length'] == '4000'):
                                        status = 'Matched'
                                    else:
                                        if row['Source_Datatype'] in ['bit', 'bigint', 'money']:
                                            status = 'Matched'
                                        elif row['Source_Datatype'] in ['numeric', 'int'] and str(
                                                row['Target_Datatype_Length']) in ['','none','None']:
                                            status = 'Matched'
                                        else:
                                            status = 'Unmatched'

                                    result_match.append(
                                        (row['Source_Schema'], row['Target_Schema'], row['Table_Name'], row['Column_Name'],
                                         row['Source_Datatype'],
                                         row['Source_Datatype_Length'], row['Target_Datatype'],
                                         row['Target_Datatype_Length'], row['Source_Index'], row['Target_Index'],
                                         row['Table_Type'], status))

                                result_df = pd.DataFrame(result_match,
                                                         columns=['Schema_Name', 'Target_Schema', 'Table_Name',
                                                                  'Column_Name',
                                                                  'Source_Datatype',
                                                                  'Source_Datatype_Length', 'Target_Datatype',
                                                                  'Target_Datatype_Length',
                                                                  'Source_Index', 'Target_Index', 'Table_Type', 'Status'])
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)

                            elif object_type == "Synonym":
                                source_data_df = pd.DataFrame(source_object_output,
                                                              columns=['Synonym_Name', 'Table_Name', 'Schema_Name'])
                                source_data_df = source_data_df.applymap(lambda x: x.lower() if isinstance(x, str) else x)

                                result_df = source_data_df
                                result_df.to_excel(writer, sheet_name=f"{object_type}", index=False)
                            else:
                                result_df = pd.DataFrame()

                            if object_type not in ['Datatype', 'Synonym']:
                                object_matching_count = \
                                    len(result_df[
                                            (result_df['Status'] == f"Available in both {source_name} and {target_name}")])

                                source_objects_count = \
                                    len(result_df[(result_df['Status'] == f"Available in {source_name} not in {target_name}")])

                                if source_objects_count == 0:
                                    status = 'Matched'
                                else:
                                    status = 'Not Matched'

                                final_summary_objects_list.append(
                                    (schema_name, target_schema, object_type, object_matching_count, source_objects_count,status))

                                project_connection = function_call(project_DB_details)
                                validation_insert(project_connection, iteration_id, task_name, object_category,
                                                  source_connection_id, schema_name,
                                                  target_connection_id, target_schema, object_type,
                                                  object_matching_count + source_objects_count,
                                                  object_matching_count, source_objects_count)
                        except Exception as object_error:
                            print(f"Error occurred at validation of {schema_name}.{object_type} is {str(object_error)}")

                    final_summary_objects_df = pd.DataFrame(final_summary_objects_list,
                                                            columns=['Source_Schema', 'Target_Schema',
                                                                     'Object_Name',
                                                                     'Matching_Count', 'Not_Matching_Count',
                                                                     'Matching_Status'])
                    final_summary_objects_df.to_excel(writer, sheet_name='Summary', index=False)

                    workbook = writer.book
                    sheets = workbook.sheetnames
                    sheets.remove('Summary')
                    sheets.insert(0, 'Summary')
                    workbook._sheets = [workbook.get_sheet_by_name(sheet) for sheet in sheets]

                shutil.copyfile(temp_excel_path, excel_path)
                shutil.copyfile(temp_excel_path, validation_report_path)

                adjust_column_width(excel_path)
                adjust_column_width(validation_report_path)

                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Completed', None)
            except Exception as error:
                print(f"Error occurred at validation of {schema_name} is {str(error)}")
                project_connection = function_call(project_DB_details)
                request_update(project_connection, request_id, 'Error', str(error))
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')

