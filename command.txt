Conversion::
    python main_conversion.py -task Conversion -project_id 1167 -mig_name Oracle_Postgres14 -iteration_id 1 object_category Code_Objects/Storage_Objects -object_type '' -schema GGDB2 -target_schema GGDB2 -source_connection_id 11 -dr_connection_id 12 -target_connection_id '' -restart_flag True/False -process_type E2E -cloud_category Cloud

Validation::
    QMigrator:
    python main_conversion.py -task Validation -project_id 1167 -mig_name Oracle_Postgres14 -iteration_id 1 -object_category Code_Objects/Storage_Objects/All  -schema GGDB2 -target_schema GGDB2 -source_connection_id 11 -target_connection_id 12 -cloud_category Cloud
python main_conversion.py -task Deploy_Pipeline -project_id 1252 -mig_name Synapse_Fabrics -iteration_id '' -schema '' -source_connection_id 8 -target_connection_id 9 -target_schema '' -object_type 'Pipeline' -object_name 'csvTopgDBN'  -cloud_category Local

Deploy_File::
    python main_conversion.py -task Deploy_File -project_id 1167 -mig_name Oracle_Postgres14 -target_connection_id 11
    -schema GGDB2 -target_schema GGDB2 -deploy_file_path '' -cloud_category Cloud

Deployment_File::
    python main_conversion.py -task Deployment_File -project_id 1167 -mig_name Oracle_Postgres14 -
    deploy_file_create_option Database/Iteration -iteration_id 1 -target_connection_id 11 -target_schema GGDB2 -object_type ''
    -deploy_file_check True/False

Deploy_Objects::
    python main_conversion.py -task Deploy_Objects -project_id 1167 -mig_name Oracle_Postgres14 -iteration_id 1 -schema GGDB2
    -target_connection_id '' -target_schema '' -object_type '' -object_name '' -cloud_category Cloud

Manual_Conversion::
    python main_conversion.py -task Manual_Conversion -project_id 1167 -mig_name Oracle_Postgres14 -iteration_id 1
    -source_connection_id 11 -schema GGDB2 -target_connection_id '' -target_schema '' -object_type '' -object_name ''
    -observations '' -fix_duration '' -cloud_category Cloud

Target Extract_Schemas::
    python main_conversion.py -task Extract_Schemas -project_id 1167 -mig_name Oracle_Postgres14
    -target_connection_id 11 -cloud_category Cloud