import psycopg2, cx_Oracle

def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
        data = [[str(value) if isinstance(value, cx_Oracle.LOB) else value for value in row] for row in data]
    except cx_Oracle.DatabaseError as e:
        print("Issue found near source database query", e)
        data = None
    except psycopg2.DatabaseError as e:
        print("Issue found near target database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
