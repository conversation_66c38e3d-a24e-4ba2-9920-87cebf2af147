import psycopg2, pyodbc


def connect_database(db_data):
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'] , database=db_data['db_name'],
                                  port=db_data['port'])
    return connection



def DB_connection(database_data):
    host_name = database_data['host']
    database_name = database_data['db_name']
    user_name = database_data['name']
    password = database_data['password']
    port = database_data.get('port', 1433)  # default for Synapse/SQL Server
    connection_str = (
        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
        f"SERVER={host_name},{port};"
        f"DATABASE={database_name};"
        f"UID={user_name};"
        f"PWD={password};"
        f"Encrypt=yes;"
        f"TrustServerCertificate=no;"
        f"Connection Timeout=30;"
    )
    try:
        connection = pyodbc.connect(connection_str)
        error = ''
    except pyodbc.Error as e:
        connection = None
        error = str(e)
        print("Issue found near Synapse database connection:", e)

    return connection, error


def execute_query(db_connection, query):
    cursor = db_connection.cursor()
    try:
        cursor.execute(query)
        data = cursor.fetchall()
    except pyodbc.DatabaseError as e:
        print("Issue found near source database query", e)
        data = None
    finally:
        cursor.close()
        db_connection.commit()
    return data
