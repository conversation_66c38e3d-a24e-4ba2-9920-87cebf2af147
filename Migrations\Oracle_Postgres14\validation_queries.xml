<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ DISTINCT sequence_owner,sequence_name, TO_NUMBER(LAST_NUMBER) -
                    TO_NUMBER(INCREMENT_BY) AS sequence_value
                    from all_sequences
                    where sequence_owner in (upper('@schemaname'))
                    AND sequence_name NOT LIKE '%$%'
                </Sequence>
                <Table>
                    WITH cte AS (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    SELECT /*+ PARALLEL(@degree) */ * FROM cte
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%' and OBJECT_NAME not like '%$%'
                    AND OBJECT_NAME NOT LIKE 'SYS_%'
                    ORDER BY 1,2
                </Table>
                <Partition>
                    SELECT /*+ PARALLEL(@degree) */ DISTINCT
                    A.TABLE_OWNER,A.TABLE_NAME,A.PARTITION_NAME
                    FROM ALL_TAB_PARTITIONS A
                    JOIN ALL_TABLES B
                    ON A.TABLE_NAME = B.TABLE_NAME
                    WHERE TABLE_OWNER = upper('@schemaname')
                    AND A.TABLE_NAME NOT LIKE 'MLOG$%' AND A.TABLE_NAME NOT LIKE 'RUPD$%' and A.TABLE_NAME not like '%$%'
                    AND A.TABLE_NAME NOT LIKE 'SYS_%'
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.constraint_type = 'P'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name and ac.table_name not like '%$%'
                    and ac.table_name not like 'SYS_%'
                    and ac.table_name  NOT LIKE 'MLOG$%' AND ac.table_name  NOT LIKE 'RUPD$%'
                    order by ac.owner
                </Primary_Key>
                <Unique_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.constraint_type = 'U'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name and ac.table_name not like '%$%'
                    and ac.table_name not like 'SYS_%'
                    and ac.table_name  NOT LIKE 'MLOG$%' AND ac.table_name  NOT LIKE 'RUPD$%'
                    order by ac.owner
                </Unique_Constraint>
                <Foreign_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner schema_name,ac.table_name,ac.constraint_name,'FOREIGN KEY
                    (' || acc.column_name || ')' || ' REFERENCES ' ||
                    (SELECT /*+ PARALLEL(@degree) */ LISTAGG(alc.owner || '.' || alc.table_name || '(' ||
                    alc.column_name || ')', ', ')
                    WITHIN GROUP (ORDER BY alc.position)
                    FROM all_cons_columns alc
                    WHERE alc.constraint_name = ac.r_constraint_name AND alc.owner = ac.owner) || ';',ac.status
                    FROM all_constraints ac
                    JOIN all_cons_columns acc ON ac.owner = acc.owner
                    AND ac.table_name = acc.table_name
                    AND ac.constraint_name = acc.constraint_name
                    WHERE ac.owner in (upper('@schemaname'))
                    AND ac.constraint_type = 'R' and ac.table_name not like '%$%'
                    and ac.table_name not like 'SYS_%'
                    and ac.table_name  NOT LIKE 'MLOG$%' AND ac.table_name  NOT LIKE 'RUPD$%'
                    ORDER BY ac.owner
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ C.OWNER,C.TABLE_NAME,C.COLUMN_NAME
                    FROM all_tab_columns C
                    JOIN all_tables t ON C.owner = t.owner AND C.table_name = t.table_name
                    WHERE c.nullable = 'N'
                        AND C.owner = UPPER('@schemaname')
                        AND C.table_name NOT LIKE '%$%' AND C.table_name NOT LIKE 'SYS_%'
                        AND C.table_name NOT LIKE 'MLOG$%' AND C.table_name NOT LIKE 'RUPD$%'
                    ORDER BY C.owner,C.table_name,C.column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name, data_default
                    FROM all_tab_columns
                    WHERE data_default IS NOT NULL AND TABLE_NAME not like '%$%'
                    and TABLE_NAME not like 'SYS_%'
                    and TABLE_NAME NOT LIKE 'MLOG$%' AND TABLE_NAME  NOT LIKE 'RUPD$%'
                    AND owner = upper('@schemaname')
                </Default_Constraint>
                <Check_Constraint>
                    SELECT /*+ PARALLEL(@degree) */
                    ac.owner,ac.table_name,ac.constraint_name,ac.search_condition,ac.status
                    from all_constraints ac, all_cons_columns acc
                    where ac.constraint_type = 'C'
                    and ac.owner = acc.owner
                    and ac.table_name = acc.table_name
                    and ac.constraint_name = acc.constraint_name
                    AND ac.owner = upper('@schemaname')
                    AND (AC.TABLE_NAME||'.'||acc.column_name) NOT IN (SELECT /*+ PARALLEL(@degree) */ DISTINCT
                    (TABLE_NAME||'.'||COLUMN_NAME) FROM all_tab_columns WHERE all_tab_columns.nullable = 'N')
                    AND AC.TABLE_NAME NOT LIKE '%$%' AND AC.TABLE_NAME not like 'SYS_%'
                    and AC.TABLE_NAME NOT LIKE 'MLOG$%' AND AC.TABLE_NAME  NOT LIKE 'RUPD$%'
                    order by ac.owner
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    table_name,
                    index_name,
                    index_cols,
                    uniqueness,
                    constraint_type,
                    lower('create' || case when UNIQUENESS = 'NONUNIQUE' then ' index ' else ' UNIQUE index ' end ||
                    INDEX_NAME || ' on ' || schema_name || '.' || table_name || '(' || index_cols || ');') IDX_DEF,
                    lower(schema_name||'.'||table_name||'-'||index_name) oraConcat
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */ * from
                    (
                    WITH cols AS (
                    SELECT /*+ PARALLEL(@degree) */
                    idx.table_owner AS schema_name,
                    idx.table_name,
                    idx.index_name,
                    cols.column_name,
                    cols.column_position,
                    idx.uniqueness,
                    decode(cols.descend,'ASC','',' ' || cols.descend) descend
                    FROM
                    DBA_INDEXES idx,
                    DBA_IND_COLUMNS cols
                    WHERE
                    idx.owner = cols.index_owner
                    AND idx.table_name = cols.table_name
                    AND idx.index_name = cols.index_name
                    AND idx.table_owner = upper('@schemaname')
                    AND idx.table_name not like '%$%'
                    AND idx.table_name not like 'SYS_%'),
                    expr AS (
                    SELECT /*+ PARALLEL(@degree) */
                    extractValue(xs.object_value,'/ROW/TABLE_NAME') AS table_name,
                    extractValue(xs.object_value,'/ROW/INDEX_NAME') AS index_name,
                    extractValue(xs.object_value,'/ROW/COLUMN_EXPRESSION') AS column_expression,
                    extractValue(xs.object_value,'/ROW/COLUMN_POSITION') AS column_position
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    XMLTYPE(DBMS_XMLGEN.GETXML( 'SELECT table_name, index_name, column_expression, column_position FROM
                    DBA_IND_EXPRESSIONS WHERE table_name not like ''SYS_%'' and table_name not like ''%$%'' '
                    || ' union all SELECT null, null, null, null FROM dual ')
                    ) AS xml FROM DUAL
                    ) x,
                    TABLE(XMLSEQUENCE(EXTRACT(x.xml,'/ROWSET/ROW'))) xs)
                    SELECT /*+ PARALLEL(@degree) */
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE AS table_type,
                    listagg(CASE
                    WHEN cols.column_name LIKE 'SYS_N%' THEN expr.column_expression || cols.descend
                    ELSE cols.column_name || cols.descend
                    END,
                    ', ') within group(
                    order by cols.column_position) as Index_Cols,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    FROM
                    cols
                    LEFT JOIN expr ON
                    cols.table_name = expr.table_name
                    AND cols.index_name = expr.index_name
                    AND cols.column_position = expr.column_position
                    LEFT JOIN all_constraints ac ON
                    cols.index_name = ac.constraint_name
                    AND cols.table_name = ac.table_name
                    AND ac.owner = cols.schema_name
                    LEFT JOIN DBA_OBJECTS DO ON
                    DO.OWNER = cols.schema_name
                    AND do.OBJECT_NAME = cols.table_name
                    AND do.object_type = 'MATERIALIZED VIEW'
                    LEFT JOIN dba_dependencies DD ON
                    do.OBJECT_NAME = dd.NAME
                    GROUP BY
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    --, DBMS_METADATA.get_ddl ('INDEX', cols.index_name, cols.schema_name)
                    ORDER BY
                    cols.table_name,
                    cols.index_name,
                    cols.uniqueness)) res
                    WHERE upper(res.index_name) not in ( select upper(INDEX_NAME) from all_constraints where upper(owner
                    )= upper('@schemaname') and constraint_type in ( 'P', 'U'))
                    GROUP BY
                    res.schema_name,
                    res.table_name,
                    res.table_type,
                    res.index_cols,
                    res.index_name,
                    res.uniqueness,
                    res.constraint_type
                    ORDER BY 1,2
                </Index>
                <Synonym>
                    select /*+ PARALLEL(@degree) */ SYNONYM_NAME,TABLE_NAME,TABLE_OWNER from dba_synonyms where
                    owner=upper('@schemaname')
                </Synonym>
                <View>
                     SELECT /*+ PARALLEL(@degree) */ 
                        v.owner,
                        v.view_name
                    FROM 
                        dba_views v
                    JOIN 
                        dba_objects o 
                        ON v.owner = o.owner AND v.view_name = o.object_name AND o.object_type = 'VIEW'
                    WHERE 
                        v.owner = UPPER('@schemaname')
                        AND v.view_name NOT LIKE '%$%'
                        AND v.view_name NOT LIKE 'SYS_%'
                        AND v.view_name NOT LIKE 'MLOG$%'
                        AND v.view_name NOT LIKE 'RUPD$%'
                        AND o.status = 'VALID'
                    ORDER BY 
                        v.view_name
                </View>
                <Datatype>
                    WITH cte AS (SELECT /*+ PARALLEL(@degree) */ * from(
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    ORDER BY 1,2),
                    datatype as(SELECT /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME,
                    DATA_TYPE,COLUMN_SIZE,COLUMN_ID FROM (
                    select /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME, DATA_TYPE,
                    case when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NULL AND DATA_SCALE IS NULL THEN
                    '('||to_char(DATA_LENGTH)||')'
                    WHEN DATA_TYPE IN ('VARCHAR2', 'NVARCHAR2', 'NCHAR' ) THEN '('||to_char(CHAR_LENGTH)||')'
                    when DATA_TYPE IN ('TIMESTAMP(8)', 'XMLTYPE','CHAR', 'CLOB', 'UROWID') THEN
                    '('||to_char(DATA_LENGTH)||')'
                    when DATA_TYPE IN ('DATE', 'LONG', 'TIMESTAMP(6) WITH TIME ZONE', 'TIMESTAMP(6)', 'ROWID', 'RAW',
                    'BLOB', 'OBJ_SNSDETAILS') THEN NULL
                    when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NOT NULL AND DATA_SCALE IS NOT NULL THEN
                    '('||to_char(DATA_PRECISION)||','||to_char(DATA_SCALE)||')'
                    -- WHEN DATA_TYPE IN ('FLOAT') THEN NVL(DATA_PRECISION,'9999999')
                    END AS "COLUMN_SIZE", CHARACTER_SET_NAME
                    ,DATA_PRECISION, DATA_SCALE, CHAR_COL_DECL_LENGTH, DATA_LENGTH, COLUMN_ID
                    from all_tab_columns
                    where owner in upper('@schemaname')
                    order by owner, table_name, COLUMN_ID
                    )
                    )
                    SELECT /*+ PARALLEL(@degree) */ kk.owner,
                    kk.table_name,kk.column_name,kk.DATA_TYPE,kk.column_size,
                    kk.column_id,
                    cc.OBJECT_TYPE
                    FROM datatype kk
                    LEFT JOIN cte cc ON kk.owner = cc.owner
                    AND cc.object_name = kk.table_name
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    SELECT /*+ PARALLEL(@degree) */
                    object_type,
                    CASE
                    WHEN object_type LIKE 'PACKAGE%' THEN object_name || '_' || Method_name
                    WHEN object_type = 'TYPE_PROCEDURE' THEN object_name || '_' || Method_name
                    WHEN object_type = 'TYPE_FUNCTION' THEN object_name || '_' || Method_name
                    WHEN object_type = 'PROCEDURE' THEN object_name
                    WHEN object_type = 'FUNCTION' THEN object_name
                    END AS Code_Object_Name
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    CASE
                    WHEN p.object_type = 'PACKAGE' AND p.procedure_name IS NOT NULL AND EXISTS (
                    SELECT 1 FROM all_arguments a
                    WHERE a.owner = p.owner
                    AND a.package_name = p.object_name
                    AND a.object_name = p.procedure_name
                    AND a.argument_name IS NOT NULL
                    ) THEN 'PACKAGE_PROCEDURE'
                    WHEN p.object_type = 'PACKAGE' AND p.procedure_name IS NOT NULL THEN 'PACKAGE_FUNCTION'
                    WHEN p.object_type = 'TYPE' AND p.procedure_name IS NOT NULL THEN 'TYPE_PROCEDURE'
                    WHEN p.object_type = 'TYPE' AND p.procedure_name IS NULL THEN 'TYPE_FUNCTION'
                    ELSE p.object_type
                    END AS object_type,
                    p.object_name,
                    CASE
                    WHEN p.object_type IN ('PACKAGE', 'TYPE') THEN p.procedure_name
                    WHEN p.object_type IN ('PROCEDURE', 'FUNCTION') THEN p.object_name
                    END AS Method_name
                    FROM dba_procedures p
                    JOIN dba_objects o ON p.object_name = o.object_name AND p.object_type = o.object_type AND p.owner =
                    o.owner
                    WHERE p.owner = UPPER('@schemaname')
                    AND p.OBJECT_TYPE IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'TYPE')
                    AND o.status = 'VALID'
                    ) a
                    WHERE Method_name IS NOT NULL
                    ORDER BY object_type, Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ table_owner,
                    table_name,
                    trigger_name
                    FROM DBA_TRIGGERS
                    WHERE owner = upper('@schemaname') and table_name not like '%$%'
                    ORDER BY table_name,
                    trigger_name
                </Trigger>
            </Code>
        </Source>
        <Target>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ distinct schemaname as schema,
                    sequencename as sequence,
                    last_value - 1 as sequence_value
                    from pg_sequences where schemaname =lower('@schemaname')
                </Sequence>
                <Table>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name ,table_type FROM information_schema.tables
                    WHERE table_schema = lower('@schemaname') AND table_type = 'BASE TABLE' AND
                    table_schema||'.'||table_name
                    NOT IN (
                    SELECT inhrelid::regclass::text
                    FROM pg_inherits
                    )
                </Table>
                <Partition>
                    select /*+ PARALLEL(@degree) */ DISTINCT nmsp_parent.nspname as schemaname,parent.relname tablename,child.relname AS partition_table FROM pg_inherits
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_namespace nmsp_parent ON nmsp_parent.oid = parent.relnamespace
                    JOIN pg_namespace nmsp_child ON nmsp_child.oid = child.relnamespace
                    WHERE nmsp_parent.nspname = lower('@schemaname')
                    ORDER BY child.relname
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'PRIMARY KEY'
                    AND tc.table_schema = lower('@schemaname')
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS primary_keys
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Primary_Key>
                <Unique_Constraint>
                    select /*+ PARALLEL(@degree) */ table_schema ,table_name ,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */ tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'UNIQUE'
                    AND tc.table_schema = lower('@schemaname')
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS unique_constraints
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Unique_Constraint>
                <Foreign_Key>
                    with cte as (
                    SELECT /*+ PARALLEL(@degree) */ conname AS constraint_name, conrelid::regclass AS table_name,
                    a.attname AS column_name,
                    confrelid::regclass AS referenced_table, fa.attname AS referenced_column
                    FROM pg_constraint AS c
                    JOIN pg_attribute AS a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid
                    JOIN pg_attribute AS fa ON fa.attnum = ANY(c.confkey) AND fa.attrelid = c.confrelid)

                    select /*+ PARALLEL(@degree) */ split_part(table_name::regclass::varchar,'.',1)
                    schema_name,split_part(table_name::regclass::varchar,'.',2) table_name, constraint_name,'FOREIGN
                    KEY'||' (' ||column_name ||') ' ||'REFERENCES'
                    ||' '||referenced_table||' (' || referenced_column||');','ENABLED'
                    from cte
                    where split_part(table_name::regclass::varchar,'.',1) = lower('@schemaname')
                    and table_name::regclass::varchar not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname )
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema ,table_name ,column_name
                    FROM information_schema.columns
                    WHERE upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,column_name,column_default
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    and column_default IS NOT NULL
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Default_Constraint>
                <Check_Constraint>
                    with cte as(select /*+ PARALLEL(@degree) */
                    n.nspname AS schema_name,
                    conname AS constraint_name,
                    conrelid::regclass AS table_name,
                    pg_get_constraintdef(c.oid) AS check_expression,'ENABLED'
                    FROM pg_constraint c
                    JOIN pg_namespace n ON n.oid = c.connamespace
                    WHERE confrelid = 0 AND contype = 'c'
                    and n.nspname = lower('@schemaname')
                    )
                    select /*+ PARALLEL(@degree) */ split_part(table_name::text,'.',1) as
                    schema_name,split_part(table_name::text,'.',2) as
                    table_name,constraint_name,check_expression,'ENABLED' as status from cte where
                    schema_name ||'.'|| table_name::text not in (SELECT (cn.nspname || '.' || child.relname) AS
                    Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    CASE WHEN table_name IS NULL OR table_name = '' THEN idx_schema_name ELSE table_name END AS
                    table_name,
                    index_name,
                    array_to_string(index_columns,',')AS index_columns,
                    pkey AS contraint_type,
                    table_name || '-' || index_name AS postgresconcat,
                    index_definition

                    FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    ns.nspname AS schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 1) AS idx_schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 2) AS table_name,
                    ARRAY(SELECT pg_get_indexdef(idx.indexrelid, k + 1, TRUE)
                    FROM generate_subscripts(idx.indkey, 1) AS k
                    ORDER BY k) AS index_columns,
                    CASE
                    WHEN idx.indisprimary = true THEN 'Primary Key'
                    ELSE 'Non Primary Key'
                    END AS pkey,
                    pg_get_indexdef(idx.indexrelid::regclass) AS index_definition,
                    i.relname AS index_name,
                    idx.indisunique AS is_unique,
                    idx.indisprimary AS is_primary,
                    am.amname AS index_type,
                    (idx.indexprs IS NOT NULL) OR (idx.indkey::int[] @> array[0]) AS is_functional
                    FROM pg_index AS idx
                    JOIN pg_class AS i ON i.oid = idx.indexrelid
                    JOIN pg_am AS am ON i.relam = am.oid
                    JOIN pg_namespace AS ns ON i.relnamespace = ns.oid
                    JOIN pg_user AS u ON i.relowner = u.usesysid
                    LEFT JOIN pg_constraint AS con ON con.conindid = idx.indexrelid
                    WHERE NOT nspname LIKE 'pg%'
                    AND nspname = LOWER('@schemaname')
                    AND idx.indisprimary = false
                    AND (con.contype IS NULL OR con.contype != 'u')
                    ) ind_details
                    WHERE (schema_name || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname
                    )
                    ORDER BY 1, 2, 3
                </Index>
                <Synonym>
                    SELECT /*+ PARALLEL(@degree) */ viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ schemaname,viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </View>
                <Datatype>
                    SELECT /*+ PARALLEL(@degree) */
                    isc.table_schema AS "OWNER",
                    isc.table_name AS "TABLE_NAME",
                    isc.column_name AS "COLUMN_NAME",
                    isc.data_type AS "DATA_TYPE",
                    CASE
                    WHEN isc.data_type = 'integer' THEN '(' || numeric_precision || ',' || isc.numeric_scale || ')'
                    WHEN isc.data_type IN ('double precision', 'bigint') THEN '(' || numeric_precision || ')'
                    WHEN isc.data_type IN ('character varying', 'numeric', 'character') THEN '(' ||
                    character_maximum_length || ')'
                    WHEN isc.data_type = 'numeric'
                    AND isc.numeric_precision IS NULL THEN '[Default]'
                    WHEN isc.data_type IN ('date', 'timestamp without time zone', 'text', 'timestamp with time zone',
                    'ARRAY', 'USER-DEFINED', 'bytea', 'xml', 'uuid', 'oid' ) THEN '(' || ')'
                    END AS "COLUMN_SIZE",
                    isc.ordinal_position,
                    ist.table_type
                    FROM
                    information_schema.tables ist,
                    information_schema.columns isc
                    WHERE
                    ist.table_schema = isc.table_schema
                    AND ist.table_name = isc.table_name
                    AND ist.table_schema IN (lower('@schemaname'))
                    ORDER BY
                    isc.table_schema,
                    isc.table_name,
                    isc.ordinal_position
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    Select /*+ PARALLEL(@degree) */ res.* from (select /*+ PARALLEL(@degree) */ routine_type,
                    routine_name as Code_Object_Name from information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    except
                    select /*+ PARALLEL(@degree) */ routine_type, routine_name as Code_Object_Name from
                    information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')
                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name
                </Trigger>
            </Code>
        </Target>
    </Validation_Queries>
    <Table_Validation_Queries>
        <Source>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ DISTINCT sequence_owner,sequence_name, TO_NUMBER(LAST_NUMBER) -
                    TO_NUMBER(INCREMENT_BY) AS sequence_value
                    from all_sequences
                    where sequence_owner in (lower('@schemaname'))
                </Sequence>
                <Table>
                    WITH cte AS (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    SELECT /*+ PARALLEL(@degree) */ * FROM cte
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%' and OBJECT_NAME not like '%$%'
                    AND OBJECT_NAME NOT LIKE 'SYS_%'
                    ORDER BY 1,2
                </Table>
                <Partition>
                    SELECT  /*+ PARALLEL(@degree) */ DISTINCT
                    A.TABLE_OWNER,A.TABLE_NAME,A.PARTITION_NAME
                    FROM ALL_TAB_PARTITIONS A
                    JOIN ALL_TABLES B
                    ON A.TABLE_NAME = B.TABLE_NAME
                    WHERE TABLE_OWNER = upper('@schemaname') and A.TABLE_NAME = ('@tablename')
                    AND A.TABLE_NAME NOT LIKE 'MLOG$%' AND A.TABLE_NAME NOT LIKE 'RUPD$%' and A.TABLE_NAME not like '%$%'
                    AND A.TABLE_NAME NOT LIKE 'SYS_%'
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.table_name in ('@tablename')
                    and ac.constraint_type = 'P'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name
                    and ac.table_name not like '%$%'
                    and ac.table_name not like 'SYS_%'
                    and ac.table_name  NOT LIKE 'MLOG$%' AND ac.table_name  NOT LIKE 'RUPD$%'
                    order by ac.owner
                </Primary_Key>
                <Unique_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner,ac.table_name,ac.constraint_name,ccl.col_list,ac.status
                    from all_constraints ac,
                    (select /*+ PARALLEL(@degree) */ acc.owner, acc.table_name, acc.constraint_name,
                    listagg(acc.column_name,',') within group (order by acc.position) as col_list from all_cons_columns
                    acc
                    group by acc.owner, acc.table_name, acc.constraint_name ) ccl
                    where ac.owner = upper('@schemaname')
                    and ac.table_name in ('@tablename')
                    and ac.constraint_type = 'U'
                    and ac.owner = ccl.owner
                    and ac.table_name = ccl.table_name
                    and ac.constraint_name = ccl.constraint_name
                    and ac.table_name not like '%$%'
                    and ac.table_name not like 'SYS_%'
                    and ac.table_name  NOT LIKE 'MLOG$%' AND ac.table_name  NOT LIKE 'RUPD$%'
                    order by ac.owner
                </Unique_Constraint>
                <Foreign_Key>
                    SELECT /*+ PARALLEL(@degree) */ ac.owner schema_name,ac.table_name,ac.constraint_name,'FOREIGN KEY
                    (' || acc.column_name || ')' || ' REFERENCES ' ||
                    (SELECT /*+ PARALLEL(@degree) */ LISTAGG(alc.owner || '.' || alc.table_name || '(' ||
                    alc.column_name || ')', ', ')
                    WITHIN GROUP (ORDER BY alc.position)
                    FROM all_cons_columns alc
                    WHERE alc.constraint_name = ac.r_constraint_name AND alc.owner = ac.owner) || ';',ac.status
                    FROM all_constraints ac
                    JOIN all_cons_columns acc ON ac.owner = acc.owner
                    AND ac.table_name = acc.table_name
                    AND ac.constraint_name = acc.constraint_name
                    WHERE ac.owner in (upper('@schemaname'))
                    and ac.table_name in ('@tablename')
                    AND ac.constraint_type = 'R'
                    and ac.table_name not like '%$%'
                    and ac.table_name not like 'SYS_%'
                    and ac.table_name  NOT LIKE 'MLOG$%' AND ac.table_name  NOT LIKE 'RUPD$%'
                    ORDER BY ac.owner
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ C.OWNER,C.TABLE_NAME,C.COLUMN_NAME
                    FROM all_tab_columns C
                    JOIN all_tables t ON C.owner = t.owner AND C.table_name = t.table_name
                    WHERE c.nullable = 'N'
                        AND C.owner = UPPER('@schemaname')
                        AND C.table_name IN ('@tablename')
                        AND C.table_name NOT LIKE '%$%' AND C.table_name NOT LIKE 'SYS_%'
                        AND C.table_name NOT LIKE 'MLOG$%' AND C.table_name NOT LIKE 'RUPD$%'
                    ORDER BY C.owner,C.table_name,C.column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ OWNER,TABLE_NAME,column_name, data_default
                    FROM all_tab_columns
                    WHERE data_default IS NOT NULL and table_name not like '%$%'
                    and table_name not like 'SYS_%'
                    and table_name  NOT LIKE 'MLOG$%' AND table_name  NOT LIKE 'RUPD$%'
                    AND owner = upper('@schemaname')
                    and table_name in ('@tablename')
                </Default_Constraint>
                <Check_Constraint>
                    SELECT /*+ PARALLEL(@degree) */
                    ac.owner,ac.table_name,ac.constraint_name,ac.search_condition,ac.status
                    from all_constraints ac, all_cons_columns acc
                    where ac.constraint_type = 'C'
                    and ac.owner = acc.owner
                    and ac.table_name = acc.table_name
                    and ac.constraint_name = acc.constraint_name
                    AND ac.owner = upper('@schemaname')
                    and ac.table_name in ('@tablename')
                    AND (AC.TABLE_NAME||'.'||acc.column_name) NOT IN (SELECT /*+ PARALLEL(@degree) */ DISTINCT
                    (TABLE_NAME||'.'||COLUMN_NAME) FROM all_tab_columns WHERE all_tab_columns.nullable = 'N')
                    AND AC.TABLE_NAME NOT LIKE '%$%'
                    and AC.TABLE_NAME not like 'SYS_%'
                    and AC.TABLE_NAME NOT LIKE 'MLOG$%' AND AC.TABLE_NAME NOT LIKE 'RUPD$%'
                    order by ac.owner
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    table_name,
                    index_name,
                    index_cols,
                    uniqueness,
                    constraint_type,
                    lower('create' || case when UNIQUENESS = 'NONUNIQUE' then ' index ' else ' UNIQUE index ' end ||
                    INDEX_NAME || ' on ' || schema_name || '.' || table_name || '(' || index_cols || ');') IDX_DEF,
                    lower(schema_name||'.'||table_name||'-'||index_name) oraConcat
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */ * from
                    (
                    WITH cols AS (
                    SELECT /*+ PARALLEL(@degree) */
                    idx.table_owner AS schema_name,
                    idx.table_name,
                    idx.index_name,
                    cols.column_name,
                    cols.column_position,
                    idx.uniqueness,
                    decode(cols.descend,'ASC','',' ' || cols.descend) descend
                    FROM
                    DBA_INDEXES idx,
                    DBA_IND_COLUMNS cols
                    WHERE
                    idx.owner = cols.index_owner
                    AND idx.table_name = cols.table_name
                    AND idx.index_name = cols.index_name
                    AND idx.table_owner = upper('@schemaname')
                    and idx.table_name in ('@tablename')
                    AND idx.table_name not like '%$%'
                    AND idx.table_name not like 'SYS_%'),
                    expr AS (
                    SELECT /*+ PARALLEL(@degree) */
                    extractValue(xs.object_value,'/ROW/TABLE_NAME') AS table_name,
                    extractValue(xs.object_value,'/ROW/INDEX_NAME') AS index_name,
                    extractValue(xs.object_value,'/ROW/COLUMN_EXPRESSION') AS column_expression,
                    extractValue(xs.object_value,'/ROW/COLUMN_POSITION') AS column_position
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    XMLTYPE(DBMS_XMLGEN.GETXML( 'SELECT table_name, index_name, column_expression, column_position FROM
                    DBA_IND_EXPRESSIONS WHERE table_name not like ''SYS_%'' and table_name not like ''%$%'' '
                    || ' union all SELECT null, null, null, null FROM dual ')
                    ) AS xml FROM DUAL
                    ) x,
                    TABLE(XMLSEQUENCE(EXTRACT(x.xml,'/ROWSET/ROW'))) xs)
                    SELECT /*+ PARALLEL(@degree) */
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE AS table_type,
                    listagg(CASE
                    WHEN cols.column_name LIKE 'SYS_N%' THEN expr.column_expression || cols.descend
                    ELSE cols.column_name || cols.descend
                    END,
                    ', ') within group(
                    order by cols.column_position) as Index_Cols,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    FROM
                    cols
                    LEFT JOIN expr ON
                    cols.table_name = expr.table_name
                    AND cols.index_name = expr.index_name
                    AND cols.column_position = expr.column_position
                    LEFT JOIN all_constraints ac ON
                    cols.index_name = ac.constraint_name
                    AND cols.table_name = ac.table_name
                    AND ac.owner = cols.schema_name
                    LEFT JOIN DBA_OBJECTS DO ON
                    DO.OWNER = cols.schema_name
                    AND do.OBJECT_NAME = cols.table_name
                    AND do.object_type = 'MATERIALIZED VIEW'
                    LEFT JOIN dba_dependencies DD ON
                    do.OBJECT_NAME = dd.NAME
                    GROUP BY
                    cols.schema_name,
                    cols.table_name,
                    DO.OBJECT_TYPE,
                    cols.index_name,
                    cols.uniqueness,
                    ac.constraint_type
                    --, DBMS_METADATA.get_ddl ('INDEX', cols.index_name, cols.schema_name)
                    ORDER BY
                    cols.table_name,
                    cols.index_name,
                    cols.uniqueness)) res
                    WHERE upper(res.index_name) not in ( select upper(INDEX_NAME) from all_constraints where upper(owner
                    )= upper('@schemaname') and constraint_type in ( 'P', 'U'))
                    GROUP BY
                    res.schema_name,
                    res.table_name,
                    res.table_type,
                    res.index_cols,
                    res.index_name,
                    res.uniqueness,
                    res.constraint_type
                    ORDER BY 1,2
                </Index>
                <Synonym>
                    select /*+ PARALLEL(@degree) */ SYNONYM_NAME,TABLE_NAME,TABLE_OWNER from dba_synonyms where
                    owner=upper('@schemaname')
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ owner,view_name
                    FROM dba_views
                    WHERE owner = upper('@schemaname') and view_name not like '%$%'
                    order by view_name
                </View>
                <Datatype>
                    WITH cte AS (SELECT /*+ PARALLEL(@degree) */ * from(
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name,
                    CASE WHEN OBJECT_TYPE = 'TABLE'
                    THEN 'TEMPORARY TABLE'
                    ELSE OBJECT_TYPE END AS OBJECT_TYPE
                    FROM dba_objects
                    WHERE TEMPORARY = 'Y'
                    AND OBJECT_TYPE = 'TABLE'
                    UNION
                    SELECT /*+ PARALLEL(@degree) */ owner,object_name,
                    CASE WHEN count_mv = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */ owner, object_name, count(*) AS count_mv
                    FROM dba_objects
                    WHERE OBJECT_TYPE in ('MATERIALIZED VIEW','TABLE')
                    AND TEMPORARY = 'N'
                    GROUP BY owner, object_name)
                    )
                    WHERE owner = upper('@schemaname')
                    AND
                    OBJECT_NAME NOT LIKE 'MLOG$%' AND OBJECT_NAME NOT LIKE 'RUPD$%'
                    ORDER BY 1,2),
                    datatype as(SELECT /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME,
                    DATA_TYPE,COLUMN_SIZE,COLUMN_ID FROM (
                    select /*+ PARALLEL(@degree) */ OWNER, TABLE_NAME, COLUMN_NAME, DATA_TYPE,
                    case when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NULL AND DATA_SCALE IS NULL THEN
                    '('||to_char(DATA_LENGTH)||')'
                    WHEN DATA_TYPE IN ('VARCHAR2', 'NVARCHAR2', 'NCHAR' ) THEN '('||to_char(CHAR_LENGTH)||')'
                    when DATA_TYPE IN ('TIMESTAMP(8)', 'XMLTYPE','CHAR', 'CLOB', 'UROWID') THEN
                    '('||to_char(DATA_LENGTH)||')'
                    when DATA_TYPE IN ('DATE', 'LONG', 'TIMESTAMP(6) WITH TIME ZONE', 'TIMESTAMP(6)', 'ROWID', 'RAW',
                    'BLOB', 'OBJ_SNSDETAILS') THEN NULL
                    when DATA_TYPE = 'NUMBER' AND DATA_PRECISION IS NOT NULL AND DATA_SCALE IS NOT NULL THEN
                    '('||to_char(DATA_PRECISION)||','||to_char(DATA_SCALE)||')'
                    -- WHEN DATA_TYPE IN ('FLOAT') THEN NVL(DATA_PRECISION,'9999999')
                    END AS "COLUMN_SIZE", CHARACTER_SET_NAME
                    ,DATA_PRECISION, DATA_SCALE, CHAR_COL_DECL_LENGTH, DATA_LENGTH, COLUMN_ID
                    from all_tab_columns
                    where owner in (upper('@schemaname'))
                    and table_name in ('@tablename')
                    order by owner, table_name, COLUMN_ID
                    )
                    )
                    SELECT /*+ PARALLEL(@degree) */ kk.owner,
                    kk.table_name,kk.column_name,kk.DATA_TYPE,kk.column_size,
                    kk.column_id,
                    cc.OBJECT_TYPE
                    FROM datatype kk
                    LEFT JOIN cte cc ON kk.owner = cc.owner
                    AND cc.object_name = kk.table_name
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    SELECT /*+ PARALLEL(@degree) */
                    object_type,
                    case
                    when object_type= 'PACKAGE' then object_name||'_'||Method_name
                    when object_type= 'TYPE' then object_name||'_'||Method_name
                    when object_type = 'PROCEDURE' then object_name
                    when object_type = 'FUNCTION' then object_name
                    END as Code_Object_Name
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    object_type,
                    object_name,
                    case
                    when object_type='PACKAGE' then procedure_name
                    when object_type='TYPE' then procedure_name
                    when object_type = 'PROCEDURE' then object_name
                    when object_type = 'FUNCTION' then object_name
                    END as Method_name
                    FROM dba_procedures
                    where owner = upper('@schemaname')
                    and
                    OBJECT_TYPE IN
                    (
                    'PROCEDURE',
                    'FUNCTION',
                    'PACKAGE',
                    'TYPE'
                    )
                    )a
                    where method_name is not null
                    order by object_type, Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ table_owner,
                    table_name,
                    trigger_name
                    FROM DBA_TRIGGERS
                    WHERE owner = upper('@schemaname') and table_name not like '%$%'
                    and table_name in ('@tablename')
                    ORDER BY table_name,
                    trigger_name
                </Trigger>

            </Code>
        </Source>
        <Target>
            <Storage>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ distinct schemaname as schema,
                    sequencename as sequence,
                    last_value - 1 as sequence_value
                    from pg_sequences where schemaname =lower('@schemaname')
                </Sequence>
                <Table>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name ,table_type FROM information_schema.tables
                    WHERE table_schema = lower('@schemaname') AND table_type = 'BASE TABLE'
                </Table>
                <Partition>
                    select /*+ PARALLEL(@degree) */ DISTINCT nmsp_parent.nspname as schemaname,parent.relname tablename,child.relname AS partition_table FROM pg_inherits
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_namespace nmsp_parent ON nmsp_parent.oid = parent.relnamespace
                    JOIN pg_namespace nmsp_child ON nmsp_child.oid = child.relnamespace
                    WHERE nmsp_parent.nspname = lower('@schemaname') and parent.relname = lower('@tablename')
                    ORDER BY child.relname
                </Partition>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'PRIMARY KEY'
                    AND tc.table_schema = lower('@schemaname')
                    and tc.table_name in (lower('@tablename'))
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS primary_keys
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Primary_Key>
                <Unique_Constraint>
                    select /*+ PARALLEL(@degree) */ table_schema ,table_name ,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */ tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'UNIQUE'
                    AND tc.table_schema = lower('@schemaname')
                    and tc.table_name in (lower('@tablename'))
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS unique_constraints
                    WHERE (table_schema || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Unique_Constraint>
                <Foreign_Key>
                    with cte as (
                    SELECT /*+ PARALLEL(@degree) */ conname AS constraint_name, conrelid::regclass AS table_name,
                    a.attname AS column_name,
                    confrelid::regclass AS referenced_table, fa.attname AS referenced_column
                    FROM pg_constraint AS c
                    JOIN pg_attribute AS a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid
                    JOIN pg_attribute AS fa ON fa.attnum = ANY(c.confkey) AND fa.attrelid = c.confrelid)

                    select /*+ PARALLEL(@degree) */ split_part(table_name::regclass::varchar,'.',1)
                    schema_name,split_part(table_name::regclass::varchar,'.',2) table_name, constraint_name,'FOREIGN
                    KEY'||' (' ||column_name ||') ' ||'REFERENCES'
                    ||' '||referenced_table||' (' || referenced_column||');','ENABLED'
                    from cte
                    where split_part(table_name::regclass::varchar,'.',1) = lower('@schemaname')
                    and split_part(table_name::regclass::varchar,'.',2) in (lower('@tablename'))
                    and table_name::regclass::varchar not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname )
                </Foreign_Key>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema ,table_name ,column_name
                    FROM information_schema.columns
                    WHERE upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    and table_name in (lower('@tablename'))
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,column_name,column_default
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    and table_name in (lower('@tablename'))
                    and column_default IS NOT NULL
                    and table_schema||'.'||table_name not in (SELECT (cn.nspname || '.' || child.relname) AS Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Default_Constraint>
                <Check_Constraint>
                    with cte as(select /*+ PARALLEL(@degree) */
                    n.nspname AS schema_name,
                    conname AS constraint_name,
                    conrelid::regclass AS table_name,
                    pg_get_constraintdef(c.oid) AS check_expression,'ENABLED'
                    FROM pg_constraint c
                    JOIN pg_namespace n ON n.oid = c.connamespace
                    WHERE confrelid = 0 AND contype = 'c'
                    and n.nspname = lower('@schemaname')
                    )
                    select /*+ PARALLEL(@degree) */ split_part(table_name::text,'.',1) as
                    schema_name,split_part(table_name::text,'.',2) as
                    table_name,constraint_name,check_expression,'ENABLED' as status from cte where
                    split_part(table_name::text,'.',2) in (lower('@tablename'))
                    and schema_name ||'.'|| table_name::text not in (SELECT (cn.nspname || '.' || child.relname) AS
                    Table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname)
                </Check_Constraint>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    CASE WHEN table_name IS NULL OR table_name = '' THEN idx_schema_name ELSE table_name END AS
                    table_name,
                    index_name,
                    index_columns,
                    pkey AS contraint_type,
                    index_definition,
                    table_name || '-' || index_name AS postgresconcat
                    FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    ns.nspname AS schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 1) AS idx_schema_name,
                    split_part(idx.indrelid::REGCLASS::text, '.', 2) AS table_name,
                    ARRAY(SELECT pg_get_indexdef(idx.indexrelid, k + 1, TRUE)
                    FROM generate_subscripts(idx.indkey, 1) AS k
                    ORDER BY k) AS index_columns,
                    CASE
                    WHEN idx.indisprimary = true THEN 'Primary Key'
                    ELSE 'Non Primary Key'
                    END AS pkey,
                    pg_get_indexdef(idx.indexrelid::regclass) AS index_definition,
                    i.relname AS index_name,
                    idx.indisunique AS is_unique,
                    idx.indisprimary AS is_primary,
                    am.amname AS index_type,
                    (idx.indexprs IS NOT NULL) OR (idx.indkey::int[] @> array[0]) AS is_functional
                    FROM pg_index AS idx
                    JOIN pg_class AS i ON i.oid = idx.indexrelid
                    JOIN pg_am AS am ON i.relam = am.oid
                    JOIN pg_namespace AS ns ON i.relnamespace = ns.oid
                    JOIN pg_user AS u ON i.relowner = u.usesysid
                    LEFT JOIN pg_constraint AS con ON con.conindid = idx.indexrelid
                    WHERE NOT nspname LIKE 'pg%'
                    AND nspname = LOWER('@schemaname')
                    and split_part(idx.indrelid :: REGCLASS :: text,'.',2) in (lower('@tablename'))
                    AND idx.indisprimary = false
                    AND (con.contype IS NULL OR con.contype != 'u')
                    ) ind_details
                    WHERE (schema_name || '.' || table_name) NOT IN (
                    SELECT (cn.nspname || '.' || child.relname) AS table_name
                    FROM pg_inherits
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_namespace pn ON parent.relnamespace = pn.oid
                    JOIN pg_namespace cn ON child.relnamespace = cn.oid
                    WHERE pn.nspname NOT IN ('pg_catalog', 'information_schema')
                    ORDER BY pn.nspname, parent.relname, cn.nspname, child.relname
                    )
                    ORDER BY 1, 2, 3
                </Index>
                <Synonym>
                    SELECT /*+ PARALLEL(@degree) */ viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')

                    order by viewname
                </Synonym>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ schemaname,viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </View>
                <Datatype>
                    SELECT /*+ PARALLEL(@degree) */
                    isc.table_schema AS "OWNER",
                    isc.table_name AS "TABLE_NAME",
                    isc.column_name AS "COLUMN_NAME",
                    isc.data_type AS "DATA_TYPE",
                    CASE
                    WHEN isc.data_type = 'integer' THEN '(' || numeric_precision || ',' || isc.numeric_scale || ')'
                    WHEN isc.data_type IN ('double precision', 'bigint') THEN '(' || numeric_precision || ')'
                    WHEN isc.data_type IN ('character varying', 'numeric', 'character') THEN '(' ||
                    character_maximum_length || ')'
                    WHEN isc.data_type = 'numeric'
                    AND isc.numeric_precision IS NULL THEN '[Default]'
                    WHEN isc.data_type IN ('date', 'timestamp without time zone', 'text', 'timestamp with time zone',
                    'ARRAY', 'USER-DEFINED', 'bytea', 'xml', 'uuid', 'oid' ) THEN '(' || ')'
                    END AS "COLUMN_SIZE",
                    isc.ordinal_position,
                    ist.table_type
                    FROM
                    information_schema.tables ist,
                    information_schema.columns isc
                    WHERE
                    ist.table_schema = isc.table_schema
                    AND ist.table_name = isc.table_name
                    AND ist.table_schema IN (lower('@schemaname'))
                    and ist.table_name in (lower('@tablename'))
                    ORDER BY
                    isc.table_schema,
                    isc.table_name,
                    isc.ordinal_position
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    Select /*+ PARALLEL(@degree) */ res.* from (select /*+ PARALLEL(@degree) */ routine_type,
                    routine_name as Code_Object_Name from information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    except
                    select /*+ PARALLEL(@degree) */ routine_type, routine_name as Code_Object_Name from
                    information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')

                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name
                </Trigger>
            </Code>
        </Target>
    </Table_Validation_Queries>
</Queries>