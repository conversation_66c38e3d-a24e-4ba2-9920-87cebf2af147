<Queries>
    <Validation_Queries>
        <Source>
            <Storage>
               <Table>
                   WITH cte AS (SELECT SCHEMA_NAME(schema_id) AS owner,name AS object_name,
                   CASE WHEN temporal_type = 2 THEN 'TEMPORARY TABLE' ELSE 'UNKNOWN' END AS OBJECT_TYPE
                   FROM sys.tables WHERE temporal_type = 2
                   UNION
                   SELECT SCHEMA_NAME(schema_id) AS owner,name AS object_name,
                   CASE WHEN COUNT(*) = 2 THEN 'MATERIALIZED VIEW' ELSE 'TABLE' END AS OBJECT_TYPE
                   FROM sys.tables
                   WHERE temporal_type = 0 GROUP BY SCHEMA_NAME(schema_id), name
                   ) SELECT * FROM cte WHERE owner = '@schemaname'
                   ORDER BY owner, object_name
               </Table>
               <Sequence>
                   SELECT schema_name(schema_id) AS sequence_owner,name AS sequence_name,CAST(current_value AS INT) -
                   CAST(increment AS INT) AS sequence_value
                   FROM sys.sequences WHERE schema_name(schema_id) IN ('@schemaname')
               </Sequence>
               <Foreign_Key>
                   SELECT schema_name(tab.schema_id) AS schema_name,tab.name AS table_name,
                   fk.name AS constraint_name,'FOREIGN KEY (' + STUFF((SELECT ', ' + col.name
                   FROM sys.columns col
                   JOIN sys.foreign_key_columns fkc ON col.column_id = fkc.parent_column_id AND col.object_id =
                   fkc.parent_object_id
                   WHERE fkc.constraint_object_id = fk.object_id
                   ORDER BY fkc.constraint_column_id
                   FOR XML PATH('')), 1, 2, '') + ') REFERENCES ' +
                   schema_name(refTab.schema_id) + '.' + refTab.name + '(' +
                   STUFF((SELECT ', ' + col.name
                   FROM sys.columns col
                   WHERE col.object_id = refTab.object_id
                   ORDER BY col.column_id
                   FOR XML PATH('')), 1, 2, '') + ');' AS foreign_key_definition,
                   fk.is_disabled AS status
                   FROM sys.foreign_keys fk
                   JOIN sys.tables tab ON fk.parent_object_id = tab.object_id
                   JOIN sys.tables refTab ON fk.referenced_object_id = refTab.object_id
                   WHERE schema_name(tab.schema_id) IN ('@schemaname')
                   ORDER BY schema_name(tab.schema_id)
                </Foreign_Key>
                <Synonym>
                    SELECT s.name AS SYNONYM_NAME,base_object_name AS TABLE_NAME, SCHEMA_NAME(o.schema_id) AS
                    TABLE_OWNER
                    FROM sys.synonyms s JOIN sys.objects o ON s.object_id = OBJECT_ID(SCHEMA_NAME(o.schema_id) + '.' +
                    o.name) WHERE SCHEMA_NAME(o.schema_id) = '@schemaname'
                </Synonym>
                <Check_Constraint>
                    SELECT col.TABLE_SCHEMA AS owner,col.TABLE_NAME,cc.name AS CONSTRAINT_NAME,cc.definition AS
                    search_condition,cc.is_disabled AS status
                    FROM INFORMATION_SCHEMA.COLUMNS col
                    JOIN sys.check_constraints cc ON col.TABLE_NAME = OBJECT_NAME(cc.parent_object_id)
                    WHERE cc.type_desc = 'CHECK_CONSTRAINT'
                    AND col.TABLE_SCHEMA = '@schemaname'
                    AND NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS c
                    WHERE c.TABLE_SCHEMA = col.TABLE_SCHEMA AND c.TABLE_NAME = col.TABLE_NAME
                    AND c.COLUMN_NAME = col.COLUMN_NAME AND c.IS_NULLABLE = 'NO'
                    ) AND col.TABLE_NAME NOT LIKE '%$%' ORDER BY col.TABLE_SCHEMA, col.TABLE_NAME, cc.name;
                </Check_Constraint>
                <Not_Null_Constraint>
                    SELECT
                    ic.TABLE_SCHEMA as owner,
                    ic.TABLE_NAME,
                    ic.COLUMN_NAME
                    FROM
                    INFORMATION_SCHEMA.COLUMNS ic
                    join INFORMATION_SCHEMA.tables it on
                    it.TABLE_NAME = ic.TABLE_NAME
                     WHERE
                    IS_NULLABLE = 'NO'
                    AND ic.TABLE_SCHEMA = '@schemaname'
                    and table_type != 'VIEW'
                   ORDER BY
                    ic.TABLE_SCHEMA,
                    ic.TABLE_NAME,
                    ic.COLUMN_NAME
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT TABLE_SCHEMA AS OWNER,TABLE_NAME,COLUMN_NAME,COLUMN_DEFAULT AS data_default FROM
                    INFORMATION_SCHEMA.COLUMNS
                    WHERE COLUMN_DEFAULT IS NOT NULL AND TABLE_SCHEMA = '@schemaname' ORDER BY TABLE_SCHEMA, TABLE_NAME,
                    COLUMN_NAME
                </Default_Constraint>
                <Primary_Key>
                    SELECT SCHEMA_NAME(t.schema_id) AS owner,t.name AS table_name,kc.name AS
                    constraint_name,STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) AS col_list,
                    CASE WHEN OBJECTPROPERTY(kc.object_id, 'IsPrimaryKey') = 1 THEN 'ENABLED' ELSE 'DISABLED' END AS
                    status
                    FROM sys.tables t
                    JOIN sys.key_constraints kc ON t.object_id = kc.parent_object_id
                    JOIN sys.index_columns ic ON kc.parent_object_id = ic.object_id AND kc.unique_index_id = ic.index_id
                    JOIN sys.columns c ON ic.column_id = c.column_id AND ic.object_id = c.object_id
                    WHERE t.schema_id = SCHEMA_ID('@schemaname') -- Replace with the actual schema name
                    AND kc.type_desc = 'PRIMARY_KEY_CONSTRAINT'
                    GROUP BY SCHEMA_NAME(t.schema_id), t.name, kc.name, kc.object_id
                    ORDER BY SCHEMA_NAME(t.schema_id), t.name, kc.name
                </Primary_Key>
                <Unique_Constraint>
                    SELECT SCHEMA_NAME(t.schema_id) AS owner,t.name AS table_name,kc.name AS
                    constraint_name,STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) AS col_list,
                    CASE WHEN OBJECTPROPERTY(kc.object_id, 'IsUniqueCnst') = 1 THEN 'ENABLED' ELSE 'DISABLED' END AS
                    status
                    FROM sys.tables t
                    JOIN sys.key_constraints kc ON t.object_id = kc.parent_object_id
                    JOIN sys.indexes i ON kc.parent_object_id = i.object_id AND kc.unique_index_id = i.index_id
                    JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    JOIN sys.columns c ON ic.column_id = c.column_id AND ic.object_id = c.object_id
                    WHERE t.schema_id = SCHEMA_ID('@schemaname') -- Replace with the actual schema name
                    AND kc.type_desc = 'UNIQUE_CONSTRAINT'
                    GROUP BY SCHEMA_NAME(t.schema_id), t.name, kc.name, kc.object_id
                    ORDER BY SCHEMA_NAME(t.schema_id), t.name, kc.name
                </Unique_Constraint>
               <View>
                   SELECT SCHEMA_NAME(schema_id) AS owner,name AS view_name FROM sys.views
                   WHERE SCHEMA_NAME(schema_id) = '@schemaname' AND name NOT LIKE '%$%' ORDER BY name
               </View>
                 <Index>
                    WITH IndexColumns AS (
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    i.name AS index_name,
                    c.name AS column_name,
                    ic.key_ordinal AS column_position,
                    i.is_unique AS uniqueness,
                    ic.is_descending_key AS descend
                    FROM
                    sys.indexes i
                    INNER JOIN
                    sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    INNER JOIN
                    sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    INNER JOIN
                    sys.tables t ON t.object_id = i.object_id
                    INNER JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    WHERE
                    s.name = lower('@schemaname')
                    AND
                    t.name NOT LIKE '%$%'
                    ),
                    ConstraintType AS (
                    SELECT
                    s.name AS schema_name,
                    t.name AS table_name,
                    i.name AS index_name,
                    CASE
                    WHEN i.is_primary_key = 1 THEN 'P'
                    ELSE 'NP'
                    END AS constraint_type
                    FROM
                    sys.indexes i
                    INNER JOIN
                    sys.tables t ON i.object_id = t.object_id
                    INNER JOIN
                    sys.schemas s ON t.schema_id = s.schema_id
                    WHERE
                    s.name = lower('@schemaname')
                    )
                    SELECT
                    ic.schema_name,
                    ic.table_name,
                    ic.index_name,
                    STUFF((
                    SELECT ', ' +
                    CASE
                    WHEN ic_inner.column_name LIKE 'SYS_N%' THEN ic_inner.column_name + CASE WHEN ic_inner.descend = 1
                    THEN ' DESC' ELSE ' ASC' END
                    ELSE ic_inner.column_name + CASE WHEN ic_inner.descend = 1 THEN ' DESC' ELSE ' ASC' END
                    END
                    FROM IndexColumns ic_inner
                    WHERE ic.schema_name = ic_inner.schema_name
                    AND ic.table_name = ic_inner.table_name
                    AND ic.index_name = ic_inner.index_name
                    ORDER BY ic_inner.column_position
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS index_cols,
                    CASE
                    WHEN ic.uniqueness = 0 THEN 'NONUNIQUE'
                    ELSE 'UNIQUE'
                    END AS uniqueness,
                    ct.constraint_type,
                    LOWER('CREATE' +
                    CASE
                    WHEN ic.uniqueness = 0 THEN ' INDEX '
                    ELSE ' UNIQUE INDEX '
                    END +
                    ic.index_name + ' ON ' + ic.schema_name + '.' + ic.table_name + '(' +
                    STUFF((
                    SELECT ', ' +
                    CASE
                    WHEN ic_inner.column_name LIKE 'SYS_N%' THEN ic_inner.column_name + CASE WHEN ic_inner.descend = 1
                    THEN ' DESC' ELSE ' ASC' END
                    ELSE ic_inner.column_name + CASE WHEN ic_inner.descend = 1 THEN ' DESC' ELSE ' ASC' END
                    END
                    FROM IndexColumns ic_inner
                    WHERE ic.schema_name = ic_inner.schema_name
                    AND ic.table_name = ic_inner.table_name
                    AND ic.index_name = ic_inner.index_name
                    ORDER BY ic_inner.column_position
                    FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') + ');') AS IDX_DEF,
                    LOWER(ic.schema_name + '.' + ic.table_name + '-' + ic.index_name) AS oraConcat
                    FROM
                    IndexColumns ic
                    LEFT JOIN
                    ConstraintType ct ON ic.schema_name = ct.schema_name AND ic.table_name = ct.table_name AND
                    ic.index_name = ct.index_name
                    GROUP BY
                    ic.schema_name,
                    ic.table_name,
                    ic.index_name,
                    ic.uniqueness,
                    ct.constraint_type
                    ORDER BY
                    ic.schema_name, ic.table_name, ic.index_name;
                </Index>
            </Storage>
            <Code>
                <Code_Objects>
                    SELECT
                    CASE
                    WHEN object_type = 'PROCEDURE' THEN 'PROCEDURE'
                    WHEN object_type = 'FUNCTION' THEN 'FUNCTION'
                    WHEN object_type = 'PACKAGE' THEN 'PACKAGE'
                    WHEN object_type = 'TYPE' THEN 'TYPE'
                    END AS object_type,
                    CASE
                    WHEN object_type IN ('PACKAGE', 'TYPE') THEN object_name + '_' + method_name
                    ELSE object_name
                    END AS Code_Object_Name
                    FROM
                    (
                    SELECT
                    CASE
                    WHEN ROUTINE_TYPE = 'PROCEDURE' THEN 'PROCEDURE'
                    WHEN ROUTINE_TYPE = 'FUNCTION' THEN 'FUNCTION'
                    WHEN ROUTINE_TYPE = 'FUNCTION' THEN 'PACKAGE'
                    ELSE 'TYPE'
                    END AS object_type,
                    SPECIFIC_NAME AS object_name,
                    ROUTINE_NAME AS method_name
                    FROM
                    INFORMATION_SCHEMA.ROUTINES
                    WHERE
                    SPECIFIC_SCHEMA = '@schemaname' AND
                    ROUTINE_TYPE IN ('PROCEDURE', 'FUNCTION')
                    ) a
                    WHERE
                    method_name IS NOT NULL
                    ORDER BY
                    object_type,
                    Code_Object_Name;
                </Code_Objects>
                <Trigger>
                    SELECT SCHEMA_NAME(t.schema_id) AS table_owner,t.name AS table_name,tr.name AS trigger_name
                    FROM sys.tables t JOIN sys.triggers tr ON t.object_id = tr.parent_id
                    WHERE SCHEMA_NAME(t.schema_id) = '@schemaname' AND t.name NOT LIKE '%$%' ORDER BY t.name, tr.name
                </Trigger>
            </Code>
        </Source>
         <Target>
            <Storage>
                <Table>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name ,table_type FROM information_schema.tables
                    WHERE table_schema = lower('@schemaname') AND table_type = 'BASE TABLE' AND table_schema||'.'||table_name
                    NOT IN (
                    SELECT inhrelid::regclass::text
                    FROM pg_inherits
                    )
                </Table>
                <Sequence>
                    select /*+ PARALLEL(@degree) */ distinct schemaname as schema,
                    sequencename as sequence,
                    last_value - 1 as sequence_value
                    from pg_sequences where schemaname =lower('@schemaname')
                </Sequence>
                <Foreign_Key>
                    with cte as (
                    SELECT /*+ PARALLEL(@degree) */ conname AS constraint_name, conrelid::regclass AS table_name,
                    a.attname AS column_name,
                    confrelid::regclass AS referenced_table, fa.attname AS referenced_column
                    FROM pg_constraint AS c
                    JOIN pg_attribute AS a ON a.attnum = ANY(c.conkey) AND a.attrelid = c.conrelid
                    JOIN pg_attribute AS fa ON fa.attnum = ANY(c.confkey) AND fa.attrelid = c.confrelid)

                    select /*+ PARALLEL(@degree) */ split_part(table_name::regclass::varchar,'.',1)
                    schema_name,split_part(table_name::regclass::varchar,'.',2) table_name, constraint_name,'FOREIGN
                    KEY'||' (' ||column_name ||') ' ||'REFERENCES'
                    ||' '||referenced_table||' (' || referenced_column||');','ENABLED'
                    from cte
                    where split_part(table_name::regclass::varchar,'.',1) = lower('@schemaname');
                </Foreign_Key>
                <Synonym>
                    SELECT /*+ PARALLEL(@degree) */ viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </Synonym>
                 <Check_Constraint>
                    WITH cte AS (
                    SELECT /*+ PARALLEL(8) */
                    n.nspname AS schema_name,
                    c.conname AS constraint_name,
                    c.conrelid,
                    pg_get_constraintdef(c.oid) AS check_expression,
                    'ENABLED' AS status
                    FROM pg_constraint c
                    JOIN pg_namespace n ON n.oid = c.connamespace
                    WHERE c.confrelid = 0
                    AND c.contype = 'c'
                    AND n.nspname = lower('@schemaname')
                    )
                    SELECT /*+ PARALLEL(8) */
                    cte.schema_name,
                    cl.relname AS table_name,
                    cte.constraint_name,
                    cte.check_expression,
                    cte.status
                    FROM cte
                    JOIN pg_class cl ON cl.oid = cte.conrelid
                    ORDER BY table_name, constraint_name;
                </Check_Constraint>
                <Not_Null_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema ,table_name ,column_name
                    FROM information_schema.columns
                    WHERE upper(is_nullable) = 'NO'
                    AND table_schema = lower('@schemaname')
                    ORDER BY table_schema, table_name, column_name
                </Not_Null_Constraint>
                <Default_Constraint>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,column_name,column_default
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    and column_default IS NOT NULL
                </Default_Constraint>
                <Primary_Key>
                    SELECT /*+ PARALLEL(@degree) */ table_schema,table_name,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */
                    tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'PRIMARY KEY'
                    AND tc.table_schema = lower('@schemaname')
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS primary_keys
                </Primary_Key>
                <Unique_Constraint>
                    select /*+ PARALLEL(@degree) */ table_schema ,table_name ,constraint_name,column_list,'ENABLED' as
                    status FROM (
                    SELECT /*+ PARALLEL(@degree) */ tc.table_schema,
                    tc.table_name,
                    tc.constraint_name,
                    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS column_list,'ENABLED'
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.table_name = kcu.table_name
                    WHERE upper(tc.constraint_type) = 'UNIQUE'
                    AND tc.table_schema = lower('@schemaname')
                    GROUP BY tc.table_schema, tc.table_name, tc.constraint_name
                    ) AS unique_constraints
                </Unique_Constraint>
                <View>
                    SELECT /*+ PARALLEL(@degree) */ schemaname,viewname
                    FROM pg_views
                    WHERE schemaname = lower('@schemaname')
                    order by viewname
                </View>
                <Index>
                    SELECT /*+ PARALLEL(@degree) */
                    schema_name,
                    case when table_name is null or table_name = '' then idx_schema_name else table_name end as
                    table_name,
                    index_name,
                    index_columns,
                    pkey,
                    index_definition,
                    table_name||'-'||index_name postgresconcat
                    FROM
                    (
                    SELECT /*+ PARALLEL(@degree) */
                    ns.nspname AS schema_name,
                    split_part(idx.indrelid :: REGCLASS :: text,'.',1) AS idx_schema_name,
                    split_part(idx.indrelid :: REGCLASS :: text,'.',2) AS table_name,
                    ARRAY(SELECT pg_get_indexdef(idx.indexrelid, k + 1, TRUE) from generate_subscripts(idx.indkey, 1) AS
                    k ORDER BY k) AS index_columns,
                    case when indisprimary=true then 'Primary Key'
                    when indisprimary=false then 'Non Primary Key'
                    END pkey,
                    pg_get_indexdef(idx.indexrelid::regclass) index_definition,
                    i.relname AS index_name,
                    idx.indisunique AS is_unique,
                    idx.indisprimary AS is_primary,
                    am.amname AS index_type,
                    (idx.indexprs IS NOT NULL) OR (idx.indkey::int[] @> array[0]) AS is_functional
                    FROM pg_index AS idx
                    JOIN pg_class AS i
                    ON i.oid = idx.indexrelid
                    JOIN pg_am AS am
                    ON i.relam = am.oid
                    JOIN pg_namespace AS NS ON i.relnamespace = NS.OID
                    JOIN pg_user AS U ON i.relowner = U.usesysid
                    WHERE NOT nspname LIKE 'pg%' -- Excluding system tables
                    and nspname = lower('@schemaname')
                    )ind_details
                    ORDER BY 1,2,3
                </Index>
                <Datatype>
                    SELECT /*+ PARALLEL(@degree) */
                    isc.table_schema AS "OWNER",
                    isc.table_name AS "TABLE_NAME",
                    isc.column_name AS "COLUMN_NAME",
                    isc.data_type AS "DATA_TYPE",
                    CASE
                    WHEN isc.data_type = 'integer' THEN '(' || numeric_precision || ',' || isc.numeric_scale || ')'
                    WHEN isc.data_type IN ('double precision', 'bigint') THEN '(' || numeric_precision || ')'
                    WHEN isc.data_type IN ('character varying', 'numeric', 'character') THEN '(' ||
                    character_maximum_length || ')'
                    WHEN isc.data_type = 'numeric'
                    AND isc.numeric_precision IS NULL THEN '[Default]'
                    WHEN isc.data_type IN ('date', 'timestamp without time zone', 'text', 'timestamp with time zone',
                    'ARRAY', 'USER-DEFINED', 'bytea', 'xml', 'uuid', 'oid' ) THEN '(' || ')'
                    END AS "COLUMN_SIZE",
                    isc.ordinal_position,
                    ist.table_type
                    FROM
                    information_schema.tables ist,
                    information_schema.columns isc
                    WHERE
                    ist.table_schema = isc.table_schema
                    AND ist.table_name = isc.table_name
                    AND ist.table_schema IN (lower('@schemaname'))
                    ORDER BY
                    isc.table_schema,
                    isc.table_name,
                    isc.ordinal_position
                </Datatype>
            </Storage>
            <Code>
                <Code_Objects>
                    Select /*+ PARALLEL(@degree) */ res.* from (select /*+ PARALLEL(@degree) */ routine_type,
                    routine_name as Code_Object_Name from information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    except
                    select /*+ PARALLEL(@degree) */ routine_type, routine_name as Code_Object_Name from
                    information_schema.routines
                    where routine_type in
                    (
                    'FUNCTION'
                    ,'PROCEDURE'
                    )
                    and lower(specific_schema) = lower('@schemaname')
                    and lower(data_type) =lower( 'trigger') ) res order by res.Code_Object_Name
                </Code_Objects>
                <Trigger>
                    SELECT /*+ PARALLEL(@degree) */ upper(trigger_schema) trigger_schema,
                    upper(event_object_table) as Table_Name,
                    upper(trigger_name) as Trigger_Name
                    FROM information_schema.triggers
                    WHERE trigger_schema = lower('@schemaname')
                    GROUP BY trigger_schema, trigger_name, event_object_table, action_timing, action_orientation
                    ORDER BY event_object_table, trigger_name;
                </Trigger>
            </Code>
        </Target>
   </Validation_Queries>
</Queries>