import re, os, sys, warnings, csv, multiprocessing
import pandas as pd
import numpy as np
from datetime import datetime
import xml.etree.ElementTree as ET
from import_file import import_file
from joblib import Parallel, delayed
from cryptography.fernet import Fernet
from common_modules.api import decrypt_database_details, api_authentication, get_decryption_key, \
    delete_files_in_directory
from common_modules.common_functions import DualStream, create_sub_objects, pg_formatter, deploy_object
from common_modules.comments_handling import retrieve_comments_with_rules, retrieve_singlequote_data, \
    replace_comment_markers, replace_single_quote_markers
from common_modules.stored_procedures import request_insert, request_update, target_objects_insert, \
    target_deployment_insert, schema_insert

warnings.filterwarnings('ignore')


def date_format(data):
    capital_rr = re.search('RRRR', data, flags=re.DOTALL)
    if capital_rr:
        data = re.sub(rf'\b{capital_rr.group()}\b', 'YYYY', flags=re.DOTALL | re.I)
    lower_rr = re.search('rrrr', data, flags=re.DOTALL)
    if lower_rr:
        data = re.sub(rf'\b{capital_rr.group()}\b', 'yyyy', flags=re.DOTALL | re.I)
    return data


def add_execute_keyword(query):
    if re.search(r'execute\s*immediate\s*', query, flags=re.IGNORECASE | re.DOTALL):
        query = re.sub(r'execute\s*immediate\s*', r"execute ", query, flags=re.IGNORECASE | re.DOTALL)
    if not re.search(r'\bexecute\s*\'', query, flags=re.IGNORECASE | re.DOTALL):
        check = re.findall(r"\'\s*(?:\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\balter\b).*?;", query,
                           flags=re.IGNORECASE | re.DOTALL)
        for i in list(set(check)):
            if re.search(r'\bselect\b', i, flags=re.DOTALL | re.I):
                if re.search(r'\bfrom\b', i, flags=re.DOTALL | re.I):
                    query = query.replace(i, 'Execute ' + i)
            else:
                query = query.replace(i, 'Execute ' + i)
    query_mod = re.findall(r'\bexecute\s*\'\s*(?:insert\b|update\b|delete\s*from\b|delete)\s*\'\s*\;', query,
                           flags=re.DOTALL | re.I)
    for q in query_mod:
        q1 = re.sub(r'\bexecute\b', '', q, flags=re.DOTALL | re.I)
        query = query.replace(q, q1)
    query = re.sub(r'\:\s*\=\s*execute\b', ' := ', query, flags=re.DOTALL | re.I)
    new_change = re.findall(r"\(\s*execute\s*'\s*(?:select|insert|update|delete|alter)\b", query, flags=re.IGNORECASE)
    for new in new_change:
        new1=re.sub(r'\bexecute\b','',new,flags=re.DOTALL|re.I)
        query=query.replace(new,new1)
    query=re.sub(r'\braise\s+notice\s*execute\b','RAISE NOTICE ',query,flags=re.DOTALL|re.I)



    return query

def call_statement_handling(source_data, conversion_file_path):
    pacakages_path = conversion_file_path.replace('Conversion_Files.xlsx', 'Package_Procedure')
    package_procedures = []
    if os.path.exists(pacakages_path):
        package_procedures = os.listdir(pacakages_path)
        package_procedures = [i.replace('.sql', '').strip().split('-')[1].strip().upper() for i in package_procedures]
        package_procedures = list(set(package_procedures))
    code_objects_df = pd.read_excel(conversion_file_path, sheet_name='Code_Objects')

    code_objects_df['Combined_Column'] = code_objects_df['Schema_Name'] + '.' + code_objects_df[
        'Object_Name'].str.replace('.', '_', regex=False)
    code_objects_df_all = code_objects_df
    code_objects_df = code_objects_df[['Combined_Column', 'Object_Type']].drop_duplicates()

    reserved_words = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'MERGE', 'GRANT',
                      'REVOKE', 'LISTAGG', 'COMMIT', 'ROLLBACK', 'SAVEPOINT', 'BEGIN', 'END', 'LOOP', 'EXCEPTION', 'IF',
                      'THEN', 'ELSIF', 'PROCEDURE', 'FUNCTION', 'PACKAGE', 'TRIGGER', 'FROM', 'WHERE', 'GROUP',
                      'GROUP BY', 'HAVING', 'ORDER BY', 'AND', 'OR', 'NOT', 'IN', 'BETWEEN', 'LIKE', 'IS NULL',
                      'EXISTS', 'DISTINCT', 'ASC', 'DESC', 'NULL', 'TRUE', 'FALSE', 'ROWNUM', 'COLUMN', 'TABLE',
                      'VIEW', 'INDEX', 'SEQUENCE', 'USER', 'SYSDATE', 'NVL', 'TO_CHAR', 'TO_DATE', 'TO_NUMBER',
                      'CAST', 'COALESCE', 'TRUNC', 'DATE_TRUNC', 'BY', 'VALUES', 'DUAL', 'NVL', 'COUNT', 'RETURN',
                      'RETURNS', 'DBMS_OUTPUT.PUT_LINE', 'VARCHAR2', 'WHILE', 'NVARCHAR2', 'CHAR', 'INSTR', 'SUBSTR',
                      'EXTRACT', 'XMLTYPE', 'SUM', 'UPPER', 'LOWER', 'TRIM', 'LTRIM', 'RTRIM', 'JOIN', 'MAX', 'AVG',
                      'ROUND', 'DECODE', 'MIN', 'ELSE', '.GETSTRINGVAL', '.GETNUMBERVAL', 'GETSTRINGVAL',
                      'GETNUMBERVAL', 'RAISE', 'EXTRACTVALUE', 'REPLACE', 'CASE', 'DBMS_OUTPUT.ENABLE',
                      'DBMS_OUTPUT.PUT']

    source_data = re.sub(r' +', ' ', source_data)
    begin_end_block = re.findall(r'BEGIN.*END', source_data, flags=re.IGNORECASE | re.DOTALL)
    if begin_end_block:
        modified_begin_block = begin_end_block[0]
        code_objects_list1 = re.findall(r'\s+(\S+)\s*\(', modified_begin_block, flags=re.IGNORECASE | re.DOTALL)
        code_objects_list_2 = re.findall(r'\bBEGIN\s*(\S+)\s*\(', modified_begin_block, flags=re.IGNORECASE | re.DOTALL)
        code_objects_list = code_objects_list1 + code_objects_list_2
        code_objects_list = [i.split('(')[0].strip() if '(' in i else i.strip() for i in code_objects_list]
        code_objects_list = [i for i in code_objects_list if i.strip() not in reserved_words]
        for code_object_data in set(code_objects_list + package_procedures):
            # print(code_object_data)
            if '.' not in code_object_data:
                code_object_type = \
                    code_objects_df_all[
                        code_objects_df_all['Object_Name'] == code_object_data.upper().replace('"', '').strip()][
                        'Object_Type']

            else:
                code_object_type = \
                    code_objects_df[
                        code_objects_df['Combined_Column'] == code_object_data.upper().replace('"', '').strip()][
                        'Object_Type']
            if not code_object_type.empty:
                if code_object_type.iloc[0] in ['PROCEDURE', 'PACKAGE_PROCEDURE']:
                    modified_begin_block = re.sub(rf'\b{code_object_data}', 'CALL ' + code_object_data,
                                                  modified_begin_block, flags=re.DOTALL | re.I)
            if code_object_data in package_procedures and re.search(code_object_data, modified_begin_block,
                                                                    flags=re.DOTALL | re.I):
                modified_begin_block = re.sub(rf'(?:\s{code_object_data}\b|\b{code_object_data}\b)',
                                              'CALL ' + code_object_data,
                                              modified_begin_block, flags=re.DOTALL | re.I)

        source_data = source_data.replace(begin_end_block[0], modified_begin_block)

    source_data = re.sub(r'\bcall\s*call\b', 'call', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bselect\s*select\b', 'select', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bend\s*call\b', ' end ', source_data, flags=re.IGNORECASE | re.DOTALL)
    end_loop_mod = re.findall(r'\bend\s*loop\s*\w+\s*;', source_data, flags=re.DOTALL | re.I)
    if end_loop_mod:
        for elm in end_loop_mod:
            if not re.search(r'comment_quad_markers', elm, flags=re.DOTALL | re.I):
                source_data = source_data.replace(elm, '\nend loop;\n')
    source_data = re.sub(r'\bdbms_output\s*\.\s*enable', '--dbms_output.enable', source_data, flags=re.DOTALL | re.I)
    return source_data


def schema_handling(source_data, schema, conversion_file_path):
    source_data = re.sub(r' +', ' ', source_data)
    source_data = re.sub(r'\bfrom\s*dual\b', '', source_data, flags=re.DOTALL | re.I)
    insert_tables = re.findall(r'\binsert\s*into\s*.*?\S*', source_data, flags=re.DOTALL | re.I)
    update_tables = re.findall(r'\bupdate\b\s*.*?\bset\b', source_data, flags=re.DOTALL | re.I)
    delete_tables = re.findall(r'(?:\bdelete\s*from\b|\bfrom\b)\s*\S*', source_data, flags=re.DOTALL | re.I)
    join_tables = re.findall(r'\bjoin\s*.*?\bon\b', source_data, flags=re.DOTALL | re.I)
    nextval_tables = re.findall(r'\S*\.nextval', source_data, flags=re.DOTALL | re.I)
    tables_data = insert_tables + update_tables + delete_tables + join_tables + nextval_tables
    tables_list = [i.strip() for i in tables_data if i.strip() and not re.search(r"[.\(\);'\"]", i.strip())]
    for table_data in list(set(tables_list)):

        if re.search(r'(?:\bdelete\s*from\b|\binsert\s*into\b)', table_data, flags=re.I | re.DOTALL):
            modified_table_data = table_data.replace(table_data.split()[2], schema + '.' + table_data.split()[2])
        elif re.search(r'nextval', table_data, flags=re.I | re.DOTALL):
            modified_table_data = table_data.replace(table_data.split()[0], schema + '.' + table_data.split()[0])
        else:
            rep_table = table_data.split()[1]
            modified_table_data = re.sub(rf'\b{rep_table}\b', schema + '.' + rep_table, table_data,
                                         flags=re.DOTALL | re.I)  # table_data.replace(re.escape(table_data.split()[1]+' '), schema + '.' + table_data.split()[1])
        source_data = source_data.replace(table_data, modified_table_data)

    type_tables = re.findall(r'\s+\w+\s*\.\s*\w+\s*\%\s*\w+', source_data, flags=re.DOTALL | re.I)
    for type_data in type_tables:
        modified_table_data = type_data.replace(type_data, ' ' + schema.strip() + '.' + type_data.strip())
        source_data = source_data.replace(type_data, modified_table_data)

    sheet_schema_list = list(
        set(pd.read_excel(conversion_file_path, sheet_name='Code_Objects')['Schema_Name'].values.tolist()))
    code_objects_df = pd.read_excel(conversion_file_path, sheet_name='Code_Objects')
    filtered_df = code_objects_df[
        (code_objects_df['Schema_Name'].str.upper() == schema.upper())
    ]

    code_objects_list_total = set(filtered_df['Object_Name'].tolist())
    code_objects_list_total = [i.replace('.', '_') for i in code_objects_list_total]

    reserved_words = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'MERGE', 'GRANT',
                      'REVOKE', 'LISTAGG', 'COMMIT', 'ROLLBACK', 'SAVEPOINT', 'BEGIN', 'END', 'LOOP', 'EXCEPTION', 'IF',
                      'THEN', 'ELSIF', 'PROCEDURE', 'FUNCTION', 'PACKAGE', 'TRIGGER', 'FROM', 'WHERE', 'GROUP',
                      'GROUP BY', 'HAVING', 'ORDER BY', 'AND', 'OR', 'NOT', 'IN', 'BETWEEN', 'LIKE', 'IS NULL',
                      'EXISTS', 'DISTINCT', 'ASC', 'DESC', 'NULL', 'TRUE', 'FALSE', 'ROWNUM', 'COLUMN', 'TABLE',
                      'VIEW', 'INDEX', 'SEQUENCE', 'USER', 'SYSDATE', 'NVL', 'TO_CHAR', 'TO_DATE', 'TO_NUMBER',
                      'CAST', 'COALESCE', 'TRUNC', 'DATE_TRUNC', 'BY', 'VALUES', 'DUAL', 'NVL', 'COUNT', 'RETURN',
                      'RETURNS', 'DBMS_OUTPUT.PUT_LINE', 'VARCHAR2', 'WHILE', 'NVARCHAR2', 'CHAR', 'INSTR', 'SUBSTR',
                      'EXTRACT', 'XMLTYPE', 'SUM', 'UPPER', 'LOWER', 'TRIM', 'LTRIM', 'RTRIM', 'JOIN', 'MAX', 'AVG',
                      'ROUND', 'DECODE', 'MIN', 'ELSE', '.GETSTRINGVAL', '.GETNUMBERVAL', 'GETSTRINGVAL',
                      'GETNUMBERVAL', 'RAISE', 'EXTRACTVALUE', 'REPLACE', 'CASE', 'ON', 'WHEN']

    begin_end_block = re.findall(r'BEGIN.*END', source_data, flags=re.IGNORECASE | re.DOTALL)
    if begin_end_block:
        modified_begin_block = begin_end_block[0]
        code_objects_list1 = re.findall(r'\s+(\S+)\s*\(', modified_begin_block, flags=re.IGNORECASE | re.DOTALL)
        code_objects_list_2 = re.findall(r'\bBEGIN\s*(\S+)\s*\(', modified_begin_block, flags=re.IGNORECASE | re.DOTALL)
        code_objects_list = code_objects_list1 + code_objects_list_2
        code_objects_list = [i.upper() for i in code_objects_list]
        insert_into_alias_list = re.findall(r'\binsert\s*into\s*\S+\s*(\w+)\s*\(', modified_begin_block,
                                            flags=re.IGNORECASE | re.DOTALL)
        variableswith_indexvar = re.findall(r'(\w+)\s*\(\s*\w+\s*\)\s*\.', source_data, flags=re.DOTALL | re.I)
        reserved_words = reserved_words + insert_into_alias_list + variableswith_indexvar
        code_objects_list = [j for j in code_objects_list_total if j in code_objects_list]
        for code_object_data in code_objects_list:
            # print(code_object_data)
            code_object_split = code_object_data.split('.')
            if len(code_object_split) == 1 and code_object_data.strip() != '=':
                modified_table_data = re.sub(rf'\s{re.escape(code_object_split[0])}\b',
                                             ' ' + schema.strip() + '.' + code_object_split[0].strip(),
                                             code_object_data,
                                             flags=re.DOTALL | re.I)
                modified_table_data = re.sub(rf'\b{re.escape(code_object_split[0])}\b',
                                             ' ' + schema.strip() + '.' + code_object_split[0].strip(),
                                             modified_table_data,
                                             flags=re.DOTALL | re.I)
                modified_begin_block = re.sub(rf'{code_object_data}', modified_table_data, modified_begin_block,
                                              flags=re.DOTALL | re.I)

            elif len(code_object_split) == 2 and code_object_data.strip() != '=':
                if not code_object_split[0].upper().replace('"', '').strip() in sheet_schema_list:
                    modified_table_data = re.sub(rf'\b{re.escape(code_object_data)}\b',
                                                 ' ' + schema.strip() + '.' + code_object_data.strip(),
                                                 code_object_data,
                                                 flags=re.DOTALL | re.I)
                    modified_begin_block = re.sub(rf'{code_object_data}', modified_table_data, modified_begin_block,
                                                  flags=re.DOTALL | re.I)
        source_data = source_data.replace(begin_end_block[0], modified_begin_block)

    for reserve in reserved_words:
        source_data = re.sub(rf'{schema}\s*\.\s*{reserve}\s*\(', reserve.strip() + '(', source_data,
                             flags=re.DOTALL | re.I)

    schema_unexpected_xml_sequence = re.findall(rf'\b{schema}\s*\.\w+\s*\.\w+\s*\(', source_data,
                                                flags=re.DOTALL | re.I)
    for xml_sequence in schema_unexpected_xml_sequence:
        xml_modified = re.sub(rf'\b{schema}\s*\.\b', '', xml_sequence, flags=re.DOTALL | re.I)
        source_data = source_data.replace(xml_sequence, xml_modified)
    source_data = re.sub(rf'\b{schema}\s*.\s*\(', ' (', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\)\s*{schema}\b\s*.', ') ', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\b{schema}\s*.\s*=', ' =', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'=\s*{schema}\b\s*.', ' =', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\b{schema}\s*.{schema}\b', schema + '.', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\b{schema}\s*\.\s*set\b', ' Set ', source_data, flags=re.DOTALL | re.I)
    schema_name_var_equals = re.findall(rf'\b{schema}\s*\.\w+\s*\=', source_data, flags=re.DOTALL | re.I)
    for schema_equals in schema_name_var_equals:
        schema_equals_mod = re.sub(rf'\b{schema}\s*\.\b', '', schema_equals, flags=re.DOTALL | re.I)
        source_data = source_data.replace(schema_equals, schema_equals_mod)
    double_schema_name = re.findall(rf'\b{schema}\s*\.\w+\s*{schema}\s*\.\w+', source_data, flags=re.DOTALL | re.I)
    for double in double_schema_name:
        double1 = re.sub(rf'\b{schema}\s*\.', '', double, flags=re.DOTALL | re.I)
        double1 = schema + '.' + double1.strip() + ' '
        source_data = source_data.replace(double, double1)

    limit_not = re.findall(r'\brownum\s*\<\s*\>\s*\d+', source_data, flags=re.DOTALL | re.I)
    for limit in limit_not:
        limit1 = re.sub(r'\<\s*\>', '<', limit, flags=re.DOTALL | re.I)
        source_data = source_data.replace(limit, limit1)
    source_data = re.sub(rf'\b{schema}\s*\.\s*\.\b', schema.strip() + '.', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\b{schema}\s*\.\s*{schema}\s*\.', schema.strip() + '.', source_data, flags=re.DOTALL | re.I)
    return source_data


def view_dates_handling(source_data, schema, conversion_file_path):
    trunc_date_column=re.findall(r'\btrunc\s*\(.*?\)',source_data,flags=re.DOTALL|re.I)
    from_semi=re.findall(r'\bfrom\b.*?;',source_data,flags=re.DOTALL|re.I)
    for from_block in list(set(from_semi)):
        pre_defined_words = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'MERGE', 'GRANT',
                             'REVOKE', 'LISTAGG', 'COMMIT', 'ROLLBACK', 'SAVEPOINT', 'BEGIN', 'END', 'LOOP',
                             'EXCEPTION', 'IF',
                             'THEN', 'ELSIF', 'PROCEDURE', 'FUNCTION', 'PACKAGE', 'TRIGGER', 'FROM', 'WHERE', 'GROUP',
                             'GROUP BY', 'HAVING', 'ORDER BY', 'AND', 'OR', 'NOT', 'IN', 'BETWEEN', 'LIKE', 'IS NULL',
                             'EXISTS', 'DISTINCT', 'ASC', 'DESC', 'NULL', 'TRUE', 'FALSE', 'ROWNUM', 'COLUMN', 'TABLE',
                             'VIEW', 'INDEX', 'SEQUENCE', 'USER', 'SYSDATE', 'NVL', 'TO_CHAR', 'TO_DATE', 'TO_NUMBER',
                             'CAST', 'COALESCE', 'TRUNC', 'DATE_TRUNC', 'BY', 'VALUES', 'DUAL', 'NVL', 'COUNT',
                             'RETURN',
                             'RETURNS', 'DBMS_OUTPUT.PUT_LINE', 'VARCHAR2', 'WHILE', 'NVARCHAR2', 'CHAR', 'INSTR',
                             'SUBSTR',
                             'EXTRACT', 'XMLTYPE', 'SUM', 'UPPER', 'LOWER', 'TRIM', 'LTRIM', 'RTRIM', 'JOIN', 'MAX',
                             'AVG',
                             'ROUND', 'DECODE', 'MIN', 'ELSE', '.GETSTRINGVAL', '.GETNUMBERVAL', 'GETSTRINGVAL',
                             'GETNUMBERVAL', 'RAISE', 'EXTRACTVALUE', 'REPLACE', 'CASE', 'UPPER', 'LOWER']

        table_df = pd.read_excel(conversion_file_path, sheet_name='Table')
        table_df['Combined_Column'] = table_df['Schema_Name'] + '.' + table_df['Object_Name']

        for trunc in list(set(trunc_date_column)):
            if not re.search(r'\,',trunc,flags=re.DOTALL|re.I):
                trunc_column = re.findall(r'\btrunc\s*\((.*?)\)', source_data, flags=re.DOTALL | re.I)
                for column in list(set(trunc_column)):
                    if '.' in column:
                        alias_name= column.split('.')[0]
                        column_name= column.split('.')[1]

                        # print(from_block)
                        table_collect_alias=re.findall(rf'(\S+)\s+{alias_name}\b',from_block,flags=re.DOTALL|re.I)
                        for table in list(set(table_collect_alias)):
                            if table.strip().upper() not in pre_defined_words and not re.search(
                                    r'(?:=|>|<|!|\bby\b|\band\b|\bor\b|\bin\b|\bon\b|comment_quad_marker_\d+_us)',
                                    table, flags=re.DOTALL | re.I):
                                column_datatype = table_df[table_df['Combined_Column'] == table.upper().strip()][
                                    ['Input_Variable', 'Data_Type']]

                                datatype_column = column_datatype[
                                    column_datatype['Input_Variable'] == column_name.strip().upper()
                                    ]['Data_Type'].values

                                if datatype_column.size > 0 and datatype_column[0].upper() == 'DATE':
                                    # print(trunc_column)
                                    source_data = re.sub(rf'\b{trunc}\b', column.strip() + '::date ', source_data,
                                                         flags=re.DOTALL | re.I)
                    elif '.' not in column:
                        column_name=column
                        table_collect_alias = re.findall(rf'\bfrom\s+(\S+)\b', from_block, flags=re.DOTALL | re.I)
                        for table in list(set(table_collect_alias)):
                            if len(table.split())!=2:
                                if table.strip().upper() not in pre_defined_words and not re.search(
                                        r'(?:=|>|<|!|\bby\b|\band\b|\bor\b|\bin\b|\bon\b|comment_quad_marker_\d+_us)',
                                        table, flags=re.DOTALL | re.I):
                                    column_datatype = table_df[table_df['Combined_Column'] == table.upper().strip()][
                                        ['Input_Variable', 'Data_Type']]

                                    datatype_column = column_datatype[
                                        column_datatype['Input_Variable'] == column_name.strip().upper()
                                        ]['Data_Type'].values

                                    if datatype_column.size > 0 and datatype_column[0].upper() == 'DATE':
                                        # print(trunc_column)
                                        source_data = re.sub(rf'\b{trunc}\b', column.strip() + '::date ', source_data,
                                                             flags=re.DOTALL | re.I)
    return source_data


def cast_handling(source_data, schema, object_name, conversion_file_path):
    source_data = re.sub(r' +', ' ', source_data)

    pre_defined_words = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'MERGE', 'GRANT',
                         'REVOKE', 'LISTAGG', 'COMMIT', 'ROLLBACK', 'SAVEPOINT', 'BEGIN', 'END', 'LOOP',
                         'EXCEPTION', 'IF',
                         'THEN', 'ELSIF', 'PROCEDURE', 'FUNCTION', 'PACKAGE', 'TRIGGER', 'FROM', 'WHERE', 'GROUP',
                         'GROUP BY', 'HAVING', 'ORDER BY', 'AND', 'OR', 'NOT', 'IN', 'BETWEEN', 'LIKE', 'IS NULL',
                         'EXISTS', 'DISTINCT', 'ASC', 'DESC', 'NULL', 'TRUE', 'FALSE', 'ROWNUM', 'COLUMN', 'TABLE',
                         'VIEW', 'INDEX', 'SEQUENCE', 'USER', 'SYSDATE', 'NVL', 'TO_CHAR', 'TO_DATE', 'TO_NUMBER',
                         'CAST', 'COALESCE', 'TRUNC', 'DATE_TRUNC', 'BY', 'VALUES', 'DUAL', 'NVL', 'COUNT',
                         'RETURN',
                         'RETURNS', 'DBMS_OUTPUT.PUT_LINE', 'VARCHAR2', 'WHILE', 'NVARCHAR2', 'CHAR', 'INSTR',
                         'SUBSTR',
                         'EXTRACT', 'XMLTYPE', 'SUM', 'UPPER', 'LOWER', 'TRIM', 'LTRIM', 'RTRIM', 'JOIN', 'MAX',
                         'AVG',
                         'ROUND', 'DECODE', 'MIN', 'ELSE', '.GETSTRINGVAL', '.GETNUMBERVAL', 'GETSTRINGVAL',
                         'GETNUMBERVAL', 'RAISE', 'EXTRACTVALUE', 'REPLACE', 'CASE', 'UPPER', 'LOWER']

    from_block_list = re.findall(r'\bfrom\b.*?;', source_data, flags=re.IGNORECASE | re.DOTALL)
    for from_block in from_block_list:
        modified_from_block = from_block
        compare_words = re.findall(
            r'(?:trunc\s*\(\s*\S+\s*\)|\w+\s*\.\s*\w+|\w+)?\s*=\s*(?:trunc\s*\(\s*\S+\s*\)|\w+\s*\.\s*\w+|\w+)?',
            from_block, flags=re.DOTALL | re.I)

        filtered_compare_words = [
            word for word in compare_words
            if not any(pre_word in set(re.findall(r'\b\w+\b', word, flags=re.IGNORECASE | re.DOTALL)) for pre_word in
                       pre_defined_words)
        ]

        alias_names = list({re.findall(r'(\w+)\s*\.', i, flags=re.DOTALL | re.I)[0] for i in compare_words if
                            re.findall(r'(\w+)\s*\.', i, flags=re.DOTALL | re.I)})

        alias_table_names_data = [re.findall(rf'\S+\s*{i}\s+', from_block, flags=re.IGNORECASE | re.DOTALL) for i in
                                  alias_names]
        filtered_alias_table_names = [
            word.replace('"', '').upper().strip() for sublist in alias_table_names_data
            for word in sublist
            if not re.search(r'\b(?:OR|=|AND|ON)\b|=\s*\w+\s+', word) and '(' not in word and len(word.split()) == 2
        ]
        tables = re.findall(rf'from\s*(\S+)\s*', from_block, flags=re.IGNORECASE | re.DOTALL)

        table_df = pd.read_excel(conversion_file_path, sheet_name='Table')
        table_df['Combined_Column'] = table_df['Schema_Name'] + '.' + table_df['Object_Name']
        filtered_table_df = table_df[table_df['Combined_Column'].isin(
            [i.split()[0].strip() for i in set(filtered_alias_table_names + tables)])] if not table_df[
            table_df['Combined_Column'].isin([i.split()[0].strip() for i in filtered_alias_table_names])].empty else \
            table_df[['Combined_Column', 'Input_Variable', 'Data_Type']].drop_duplicates()

        all_tables = [i.split()[0].strip() for i in set(filtered_alias_table_names + tables)]

        code_objects_df = pd.read_excel(conversion_file_path, sheet_name='Code_Objects')

        code_objects_df['Combined_Column'] = code_objects_df['Schema_Name'] + '.' + code_objects_df['Object_Name']
        filtered_object_df = \
            code_objects_df[code_objects_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper().strip()}'][
                ['Combined_Column', 'Input_Variable', 'Data_Type']].drop_duplicates()
        first_variable_datatype = pd.DataFrame()
        second_variable_datatype = pd.DataFrame()
        filtered_compare_words = [i for i in filtered_compare_words if
                                  not re.search(r'\=\s*\d+', i, flags=re.DOTALL | re.I)]

        for compare_sentence in list(set(filtered_compare_words)):
            modified_sentence = compare_sentence

            first_word = modified_sentence.split('=')[0].strip()
            second_word = modified_sentence.split('=')[1].strip()

            if re.search(r'\btrunc\b', first_word, flags=re.IGNORECASE | re.DOTALL):
                if '.' in first_word:
                    first_alias_name = first_word.split('.')[0].strip().upper()
                    first_variable_name = first_word.split('.')[1].strip().upper()
                    first_table_name = next(
                        (item.split()[0] for item in filtered_alias_table_names if
                         item.split()[1].strip().upper() == first_alias_name),
                        None
                    )
                    first_variable_datatype = filtered_table_df.loc[
                        (filtered_table_df['Combined_Column'] == first_table_name) &
                        (filtered_table_df['Input_Variable'] == first_variable_name), 'Data_Type']
                else:
                    first_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
            elif '.' in first_word:
                first_alias_name = first_word.split('.')[0].strip().upper()
                first_variable_name = first_word.split('.')[1].strip().upper()
                first_table_name = next(
                    (item.split()[0] for item in filtered_alias_table_names if
                     item.split()[1].strip().upper() == first_alias_name),
                    None
                )
                first_variable_datatype = filtered_table_df.loc[
                    (filtered_table_df['Combined_Column'] == first_table_name) &
                    (filtered_table_df['Input_Variable'] == first_variable_name), 'Data_Type']
            else:

                object_condition = filtered_object_df[filtered_object_df['Input_Variable'] == first_word.upper()]

                table_condition = filtered_table_df[
                    (filtered_table_df['Input_Variable'] == first_word.upper()) &
                    (filtered_table_df['Combined_Column'].isin(all_tables))
                    ]
                if not table_condition.empty:
                    first_variable_datatype = \
                        filtered_table_df[filtered_table_df['Input_Variable'] == first_word.upper()][
                            'Data_Type']

                if not object_condition.empty:
                    first_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df[
                             'Combined_Column'] == f'{schema.upper().strip()}.{object_name.upper().strip()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type'
                    ]
            if re.search(r'\btrunc\b', first_word, flags=re.IGNORECASE | re.DOTALL):
                if '.' in second_word:
                    second_alias_name = second_word.split('.')[0].strip().upper()
                    second_variable_name = first_word.split('.')[1].strip().upper()
                    second_table_name = next(
                        (item.split()[0] for item in all_tables if
                         item.split()[1].strip().upper() == second_alias_name),
                        None
                    )
                    second_variable_datatype = filtered_table_df.loc[
                        (filtered_table_df['Combined_Column'] == second_table_name) &
                        (filtered_table_df['Input_Variable'] == second_variable_name), 'Data_Type']
                else:
                    second_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
            elif '.' in second_word:
                second_alias_name = second_word.split('.')[0].strip().upper()
                second_variable_name = second_word.split('.')[1].strip().upper()
                second_table_name = next(
                    (item.split()[0] for item in filtered_alias_table_names if
                     item.split()[1].strip().upper() == second_alias_name),
                    None
                )
                second_variable_datatype = filtered_table_df.loc[
                    (filtered_table_df['Combined_Column'] == second_table_name) &
                    (filtered_table_df['Input_Variable'] == second_variable_name), 'Data_Type']
            else:

                object_condition = filtered_object_df[filtered_object_df['Input_Variable'] == second_word.upper()]

                table_condition = filtered_table_df[
                    (filtered_table_df['Input_Variable'] == second_word.upper()) &
                    (filtered_table_df['Combined_Column'].isin(all_tables))
                    ]
                if not table_condition.empty:
                    second_variable_datatype = \
                        filtered_table_df[filtered_table_df['Input_Variable'] == second_word.upper()][
                            'Data_Type']

                if not object_condition.empty:
                    second_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df[
                             'Combined_Column'] == f'{schema.upper().strip()}.{object_name.upper().strip()}') &
                        (filtered_object_df['Input_Variable'] == second_word.upper()), 'Data_Type'
                    ]

            first_variable_datatype = first_variable_datatype.reset_index(drop=True)
            second_variable_datatype = second_variable_datatype.reset_index(drop=True)

            if re.search(r'\btrunc\s*\(', first_word, flags=re.IGNORECASE | re.DOTALL) or re.search(r'\btrunc\s*\(',
                                                                                                    second_word,
                                                                                                    flags=re.IGNORECASE | re.DOTALL):
                if not first_variable_datatype.empty:
                    if str(first_variable_datatype.values[0]).lower() == 'date':
                        modified_sentence = first_word + ' = ' + second_word
                        modified_sentence = modified_sentence.replace(first_word,
                                                                      first_word.replace('trunc', '').replace('(',
                                                                                                              '').replace(
                                                                          ')', '') + '::date')
                if not second_variable_datatype.empty:
                    if str(second_variable_datatype.values[0]).lower() == 'date':
                        modified_sentence = first_word + ' = ' + second_word
                        modified_sentence = modified_sentence.replace(second_word,
                                                                      second_word.replace('trunc', '').replace('(',
                                                                                                               '').replace(
                                                                          ')', '') + '::date')
            elif not first_variable_datatype.empty and not second_variable_datatype.empty:
                if (first_variable_datatype.values[0] != second_variable_datatype.values[0]):
                    if str(first_variable_datatype.values[0]).lower() == 'number' and str(
                            second_variable_datatype.values[0]).lower() != 'number':
                        modified_sentence = first_word + ' = ' + second_word + '::numeric'
                    elif str(second_variable_datatype.values[0]).lower() == 'number' and str(
                            first_variable_datatype.values[0]).lower() != 'number':
                        modified_sentence = first_word + '::numeric = ' + second_word

            modified_from_block = modified_from_block.replace(compare_sentence, modified_sentence)

        difference_data = re.findall(r'(?:\w+\.\w+|\w+)\s*(?:\+|\-)\s*(?:\w+\.\w+|\w+)', from_block,
                                     flags=re.DOTALL | re.I)

        for compare_sentence in difference_data:
            if not re.search(r'\d+', compare_sentence, flags=re.DOTALL):
                modified_sentence = compare_sentence

                first_word = re.split(r'(?:\+|\-)', modified_sentence, flags=re.DOTALL | re.I)[
                    0].strip()  # .split('=')[0].strip()
                second_word = re.split(r'(?:\+|\-)', modified_sentence, flags=re.DOTALL | re.I)[1].strip()

                if re.search(r'\btrunc\b', first_word, flags=re.IGNORECASE | re.DOTALL):
                    if '.' in first_word:
                        first_alias_name = first_word.split('.')[0].strip().upper()
                        first_variable_name = first_word.split('.')[1].strip().upper()
                        first_table_name = next(
                            (item.split()[0] for item in filtered_alias_table_names if
                             item.split()[1].strip().upper() == first_alias_name),
                            None
                        )
                        first_variable_datatype = filtered_table_df.loc[
                            (filtered_table_df['Combined_Column'] == first_table_name) &
                            (filtered_table_df['Input_Variable'] == first_variable_name), 'Data_Type']
                    else:
                        first_variable_datatype = filtered_object_df.loc[
                            (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                            (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
                elif '.' in first_word:
                    first_alias_name = first_word.split('.')[0].strip().upper()
                    first_variable_name = first_word.split('.')[1].strip().upper()
                    first_table_name = next(
                        (item.split()[0] for item in filtered_alias_table_names if
                         item.split()[1].strip().upper() == first_alias_name),
                        None
                    )
                    first_variable_datatype = filtered_table_df.loc[
                        (filtered_table_df['Combined_Column'] == first_table_name) &
                        (filtered_table_df['Input_Variable'] == first_variable_name), 'Data_Type']
                else:
                    first_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']

                if re.search(r'\btrunc\b', second_word, flags=re.IGNORECASE | re.DOTALL):
                    if '.' in second_word:
                        second_alias_name = second_word.split('.')[0].strip().upper()
                        second_variable_name = first_word.split('.')[1].strip().upper()
                        second_table_name = next(
                            (item.split()[0] for item in filtered_alias_table_names if
                             item.split()[1].strip().upper() == second_alias_name),
                            None
                        )
                        second_variable_datatype = filtered_table_df.loc[
                            (filtered_table_df['Combined_Column'] == second_table_name) &
                            (filtered_table_df['Input_Variable'] == second_variable_name), 'Data_Type']
                    else:
                        second_variable_datatype = filtered_object_df.loc[
                            (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                            (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
                elif '.' in second_word:
                    if len(first_word.split('.')) == 2:
                        second_alias_name = second_word.split('.')[0].strip().upper()
                        second_variable_name = first_word.split('.')[1].strip().upper()
                        second_table_name = next(
                            (item.split()[0] for item in filtered_alias_table_names if
                             item.split()[1].strip().upper() == second_alias_name),
                            None
                        )
                        second_variable_datatype = filtered_table_df.loc[
                            (filtered_table_df['Combined_Column'] == second_table_name) &
                            (filtered_table_df['Input_Variable'] == second_variable_name), 'Data_Type']
                else:
                    second_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
                if not first_variable_datatype.empty and not second_variable_datatype.empty:
                    if str(first_variable_datatype.values[0]).lower() == 'date' and str(
                            second_variable_datatype.values[0]).lower() == 'date':
                        modified_sentence = 'EXTRACT(EPOCH FROM(' + compare_sentence.strip() + '))/(24*60*60)'

                modified_from_block = modified_from_block.replace(compare_sentence, modified_sentence)

        source_data = source_data.replace(from_block, modified_from_block)

    source_data = re.sub(r'to_date\s+\(', 'to_date(', source_data, flags=re.I)
    to_date_data = re.findall(r"to_date\s*\(.*?\)\s*", source_data, flags=re.IGNORECASE | re.DOTALL)
    for compare_sentence in to_date_data:
        remaining_statement_part = source_data.split(compare_sentence.strip())
        if len(remaining_statement_part) > 1:
            remaining_statement_part = source_data.split(compare_sentence.strip())[1]
            remaining_bracket_level = 0
            remaining_bracket_level_list = []
            remaining_statement = ''
            for c in compare_sentence + remaining_statement_part:
                if c == "(":
                    remaining_bracket_level += 1
                elif c == ")":
                    remaining_bracket_level -= 1
                remaining_statement = remaining_statement + c
                remaining_bracket_level_list.append(remaining_bracket_level)
                if 1 in remaining_bracket_level_list and remaining_bracket_level == 0:
                    break
                else:
                    continue
            modified_statement = remaining_statement
            parts = []
            bracket_level = 0
            current = []
            for c in (remaining_statement[8:-1] + ","):
                if c == "," and bracket_level == 0:
                    parts.append("".join(current))
                    current = []
                else:
                    if c == "(":
                        bracket_level += 1
                    elif c == ")":
                        bracket_level -= 1
                    current.append(c)
            parts = [i for i in parts if i != '']
            if len(parts) == 2:
                first_word = parts[0]
                second_word = parts[1]

                if (
                        'dd' in second_word.lower() or 'mm' in second_word.lower() or 'yy' in second_word.lower()) and 'to_' not in first_word.lower():
                    modified_statement = f'to_date(to_char({first_word},{second_word}))'

            source_data = source_data.replace(remaining_statement, modified_statement)
    operator_type1 = re.findall(r'(?:\>|\<|\=|\,|\!)\s*\:\:numeric', source_data, flags=re.DOTALL | re.I)
    operator_type2 = re.findall(r'(?:\>|\<|\=|\,|\!)\s*\:numeric', source_data, flags=re.DOTALL | re.I)
    operator_type = operator_type1 + operator_type2
    for operator in operator_type:
        operator_mod = re.sub(rf'::numeric', '', operator, flags=re.DOTALL | re.I)
        source_data = source_data.replace(operator, operator_mod)
    for i in pre_defined_words:
        source_data = re.sub(rf'\b{i}\s*\:\:numeric', i, source_data, flags=re.DOTALL | re.I)
    return source_data


def decrypt_conversion_file(file_path, file_encryption_key, working_directory_path):
    f = Fernet(file_encryption_key)
    with open(file_path, 'rb') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = f.decrypt(encrypted)

    if not os.path.exists(working_directory_path):
        os.makedirs(working_directory_path)
    file_name = file_path.split('/')[-1]
    with open(working_directory_path + '/' + file_name, 'wb') as decrypted_file:
        decrypted_file.write(decrypted)


def feature_execution_and(source_data, schema, folder_path, value, working_directory_path, token_data, decrypt_key):
    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    if '&' in str(value[2]):
        excel_list_all = str(value[2]).split('&')
        list_all = []
        for key in excel_list_all:
            if '~' in key:
                est = re.findall(r'~\S+', key)[0]
                key = key.replace(est, '').strip()
                list_all.append(key)
            else:
                key = key.strip()
                list_all.append(key)
        list_all = [i for i in list_all if i != '']
        status_list = []
        for key in list_all:
            if '*' in key or '%' in key or '.' in key:
                key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace(
                    '..',
                    '.').replace(
                    '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
                key = key.replace('(', '\(').replace('%', '.*?').replace(')', '\)').replace('.', '\.').replace('\.*?',
                                                                                                               '.*?')
                key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
            else:
                ignore_list = [':=', ':']
                check_ignore_item = ['True' for i in ignore_list if i in key]
                if check_ignore_item:
                    key_pattern = r'\s*' + re.escape(key) + r'\s*'
                else:
                    key_pattern = r'\b' + re.escape(key) + r'\b'
                key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
        if 0 not in status_list:
            try:
                encrypt_decrypt_path = folder_path + value[6] + '/' + str(value[1]).lower() + '.py'
                # decrypt_key = get_decryption_key(token_data)
                decrypt_conversion_file(encrypt_decrypt_path, decrypt_key, working_directory_path)
                import_object = import_file(working_directory_path + '/' + str(value[1]).lower() + '.py')
                delete_files_in_directory(working_directory_path)
                function_name = [i for i in dir(import_object) if i.lower() == str(value[1]).lower()][0]
                function_call = getattr(import_object, function_name.strip())
                output = function_call(source_data, schema)
                source_data = output
            except Exception as e:
                print('Error in conversion ' + str(e) + ' at module ' + str(value[1]).lower())
                source_data = source_data
    return source_data


def feature_execution_pipe(source_data, schema, folder_path, value, working_directory_path, token_data, decrypt_key):
    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    excel_list_any = str(value[2]).split('|')
    list_any = []
    for key in excel_list_any:
        if '~' in key:
            est = re.findall(r'~\S+', key)[0]
            key = key.replace(est, '').strip()
            list_any.append(key)
        else:
            key = key.strip()
            list_any.append(key)
    list_any = [i for i in list_any if i != '']
    status_list = []
    for key in list_any:
        key = key.strip()
        if '*' in key or '%' in key or '.' in key:
            key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace('..',
                                                                                                                  '.').replace(
                '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
            key = key.replace('(', '\(').replace('%', '.*?').replace(')', '\)').replace('.', '\.').replace('\.*?',
                                                                                                           '.*?')
            key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
        else:
            ignore_list = [':=', ':']
            check_ignore_item = ['True' for i in ignore_list if i in key]
            if check_ignore_item:
                key_pattern = r'\s*' + re.escape(key) + r'\s*'
            else:
                key_pattern = r'\b' + re.escape(key) + r'\b'
            key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
    if any(i > 0 for i in status_list):
        try:
            encrypt_decrypt_path = folder_path + value[6] + '/' + str(value[1]).lower() + '.py'
            # decrypt_key = get_decryption_key(token_data)
            decrypt_conversion_file(encrypt_decrypt_path, decrypt_key, working_directory_path)
            import_object = import_file(working_directory_path + '/' + str(value[1]).lower() + '.py')
            delete_files_in_directory(working_directory_path)
            function_name = [i for i in dir(import_object) if i.lower() == str(value[1]).lower()][0]
            function_call = getattr(import_object, function_name.strip())
            output = function_call(source_data, schema)

            source_data = output
        except Exception as e:
            print('Error in conversion ' + str(e) + ' at module ' + str(value[1]).lower())
    return source_data


def feature_execution(source_data, schema, folder_path, excel_data, complete_excel_data, working_directory_path,
                      token_data, decrypt_key):
    feature_execution_list = []
    for index, value in excel_data.iterrows():
        if value[3] == 'No Predecessor':
            if value[1] not in feature_execution_list:
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, working_directory_path,
                                                        token_data, decrypt_key)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value,
                                                         working_directory_path, token_data, decrypt_key)
                delete_files_in_directory(working_directory_path)
                feature_execution_list.append(value[1])
            else:
                source_data = source_data
                feature_execution_list = feature_execution_list
        else:
            if value[3] in feature_execution_list:
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, working_directory_path,
                                                        token_data, decrypt_key)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value,
                                                         working_directory_path, token_data, decrypt_key)
                delete_files_in_directory(working_directory_path)
                feature_execution_list.append(value[1])
            else:
                parent_feature_name = value[3]
                pred_excel_data = complete_excel_data[complete_excel_data['Feature_Name'] == parent_feature_name]
                source_data = feature_execution(source_data, schema, folder_path, pred_excel_data, complete_excel_data,
                                                working_directory_path, token_data, decrypt_key)
                if '&' in str(value[2]):
                    source_data = feature_execution_and(source_data, schema, folder_path, value, working_directory_path,
                                                        token_data, decrypt_key)
                else:
                    source_data = feature_execution_pipe(source_data, schema, folder_path, value,
                                                         working_directory_path, token_data, decrypt_key)
                delete_files_in_directory(working_directory_path)
                feature_execution_list.append(value[3])
                feature_execution_list.append(value[1])
    return source_data


def build_common_object_list(objects_excel_data, object_path):
    object_path_list = []
    object_excel_data = objects_excel_data[objects_excel_data['Object_Id'] == object_path]
    object_path_list.append(object_path)
    if not object_excel_data.empty:
        if type(object_excel_data.iloc[0]['Linked_Objects']) is not float and not isinstance(
                object_excel_data.iloc[0]['Linked_Objects'], np.float64):
            linked_objects_split = object_excel_data.iloc[0]['Linked_Objects'].split(',')
            if linked_objects_split:
                for path in linked_objects_split:
                    temp_list = build_common_object_list(objects_excel_data, path)
                    if temp_list:
                        object_path_list.extend(temp_list)
    return object_path_list


def tuple_execution(object_tuple, schema_name, object_path, modules_data, rules_data, objects_data, scripts_path,
                    working_directory_path, token_data, decrypt_key):
    object_path_list = build_common_object_list(objects_data, str(object_path + '/' + 'Pre'))
    pre_module_data = modules_data[modules_data['Object_Path'].isin(object_path_list)]

    pre_output = object_tuple[1]
    if not pre_module_data.empty:
        pre_output = feature_execution(object_tuple[1], schema_name, scripts_path, pre_module_data, pre_module_data,
                                       working_directory_path,
                                       token_data, decrypt_key)

    pre_output = '\n'.join([re.sub(r' +', ' ', i).strip() for i in pre_output.split('\n') if i.strip()])

    current_object_data = objects_data[(objects_data['Object_Id'] == object_path)]
    if current_object_data.iloc[0]['Object_Process_Style'] == 'Sequential':
        sub_object_path = object_path + '/'
        sub_objects_data = objects_data[
            objects_data['Object_Id'].str.contains(object_path) & ~objects_data['Object_Id'].str.contains(
                'Pre|Post')].sort_values("Object_Execution_Order")

        sub_object_names_list = []
        object_path_length = len([i for i in sub_object_path.split('/') if i])
        sub_object_names_list.extend(row['Object_Id'].rsplit('/', 1)[-1] for _, row in sub_objects_data.iterrows() if
                                     len(row['Object_Id'].split('/')) == object_path_length + 1)

        for sub_object in sub_object_names_list:
            current_object_path = object_path + '/' + sub_object
            sub_objects_list = create_sub_objects(pre_output, current_object_path, rules_data, objects_data)

            counter = 0
            for sub_tuple in sub_objects_list:
                sub_tuple_data = '\n'.join(
                    [re.sub(r' +', ' ', i).strip() for i in sub_tuple[1].split('\n') if i.strip()])

                if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                    pre_output = re.sub(rf'{re.escape(sub_tuple_data.strip())}',
                                        f' qbook_replace_{sub_tuple[0]}_{counter}_us ', pre_output, 1,
                                        flags=re.DOTALL | re.I)
                    # pre_output = pre_output.replace(sub_tuple_data,
                    #                                 ' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ',
                    #                                 1)
                    sub_object_output = tuple_execution(sub_tuple, schema_name, current_object_path, modules_data,
                                                        rules_data, objects_data, scripts_path, working_directory_path,
                                                        token_data, decrypt_key)
                    pre_output = str(pre_output).replace(
                        ' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ',
                        '\n' + str(sub_object_output))
                else:
                    pre_output = pre_output
                counter = counter + 1

    elif current_object_data.iloc[0]['Object_Process_Style'] == 'Mutually Exclusive':
        sub_object_path = object_path + '/'
        sub_objects_list = create_sub_objects(pre_output, sub_object_path, rules_data, objects_data)

        counter = 0
        for sub_tuple in sub_objects_list:
            sub_tuple_data = '\n'.join([re.sub(r' +', ' ', i).strip() for i in sub_tuple[1].split('\n') if i.strip()])

            if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                pre_output = re.sub(rf'{re.escape(sub_tuple_data.strip())}',
                                    f' qbook_replace_{sub_tuple[0]}_{counter}_us ', pre_output, 1,
                                    flags=re.DOTALL | re.I)
                # pre_output = pre_output.replace(sub_tuple_data,
                #                                 ' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ', 1)
                current_object_path = object_path + '/' + str(sub_tuple[0])
                sub_object_output = tuple_execution(sub_tuple, schema_name, current_object_path, modules_data,
                                                    rules_data, objects_data, scripts_path, working_directory_path,
                                                    token_data, decrypt_key)
                pre_output = pre_output.replace(' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ',
                                                '\n' + str(sub_object_output))
            else:
                pre_output = pre_output
            counter = counter + 1
    else:
        pre_output = pre_output

    object_path_list = build_common_object_list(objects_data, str(object_path + '/' + 'Post'))
    post_module_data = modules_data[modules_data['Object_Path'].isin(object_path_list)]

    post_output = feature_execution(pre_output, schema_name, scripts_path, post_module_data, post_module_data,
                                    working_directory_path,
                                    token_data, decrypt_key)
    post_output = '\n'.join([re.sub(r' +', ' ', i).strip() for i in post_output.split('\n') if i.strip()])

    return post_output


def object_conversion_trigger(schema_name, object_type, object_name, modules_data, rules_data, objects_data,
                              scripts_path, working_directory_path, conversion_file_path, token_data, decrypt_key,
                              column_data_pacakegs_list=None):
    comment_identifiers = rules_data[rules_data['Object_Path'] == object_type]['Comment_Identifiers'].values.tolist()[0]

    f = open(object_name, 'r', encoding='utf-8', errors='replace')
    source_data = f.read()
    original_source_code = source_data

    source_data = "".join([s for s in source_data.strip().splitlines(True) if s.strip()])
    source_data = re.sub(' +', ' ', source_data)

    comments_data_list = re.findall(r'\/\*[\s\S]*?\*\/', source_data)
    for comment_data in comments_data_list:
        modified_comment_data = comment_data.replace('/*', '/*\n', 1).replace('*/', '\n*/', 1)
        source_data = source_data.replace(comment_data, modified_comment_data)
    dbms_new_scenario = re.findall(r'\bDBMS_OUTPUT\s*\.\s*PUT_LINE\s*\(.*?\)\s*\;', source_data, flags=re.DOTALL | re.I)
    for dbms in dbms_new_scenario:
        dbms_new = re.sub(r'\s+', '', dbms, flags=re.DOTALL | re.I)
        if re.search('-\s*-', dbms_new, flags=re.DOTALL | re.I):
            dbms_new = dbms_new.replace('-', '')
            source_data = re.sub(rf'{re.escape(dbms.strip())}', f'{dbms_new}', source_data, flags=re.DOTALL | re.I)

    # source_data, single_quote_comment_dict = retrieve_singlequote_data(source_data)
    source_data, comment_dict = retrieve_comments_with_rules(source_data, comment_identifiers)
    source_data = re.sub(r' +', ' ', source_data)

    source_data = re.sub(r'CREATE\sProc\b', 'CREATE PROCEDURE', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = source_data.replace('&lt ;', '&lt;').replace('&lt;', '<')
    source_data = source_data.replace('&gt ;', '&gt;').replace('&gt;', '>')
    source_data = re.sub(r':\s*=\s*', ':= ', source_data, flags=re.DOTALL | re.IGNORECASE)
    source_data = re.sub(r' +', ' ', source_data)
    if column_data_pacakegs_list:
        for packages in column_data_pacakegs_list:
            packaeges_co = re.findall(rf'\b{packages}\s*\.\s*\w+\(', source_data, flags=re.DOTALL | re.I)
            if packaeges_co:
                for code in list(set(packaeges_co)):
                    code1 = code.replace('.', '_')
                    split_code = code.split('.')[1]
                    if split_code.strip().lower().startswith('p_'):
                        source_data = source_data.replace(code, 'CALL ' + code1)
                    else:
                        source_data = source_data.replace(code, code1)

    if os.path.exists(conversion_file_path):
        if object_type in ['Procedure', 'Function', 'Package_Procedure', 'Package_Function']:
            source_data = schema_handling(source_data, schema_name, conversion_file_path)
            source_data = call_statement_handling(source_data, conversion_file_path)
            source_data = cast_handling(source_data, schema_name, object_name.split('/')[-1].replace('.sql', ''),
                                        conversion_file_path)
        elif object_type in ['View']:
            source_data = schema_handling(source_data, schema_name, conversion_file_path)
            source_data=view_dates_handling(source_data, schema_name, conversion_file_path)

    object_tuple = (object_type, source_data)
    object_converted_output = tuple_execution(object_tuple, schema_name, object_type, modules_data, rules_data,
                                              objects_data, scripts_path, working_directory_path, token_data,
                                              decrypt_key)

    object_converted_output = replace_comment_markers(object_converted_output, comment_dict)
    # object_converted_output = replace_single_quote_markers(object_converted_output, single_quote_comment_dict)
    
    object_converted_output = add_execute_keyword(object_converted_output)
    if re.search(r'RRRR', object_converted_output, flags=re.DOTALL | re.I):
        object_converted_output = date_format(object_converted_output)

    return original_source_code, object_converted_output


def schema_execution(task_name, migration_name, root_path, project_folder, working_directory_path, iteration_id,
                     source_connection_id, dr_connection_id, target_connection_id, schema, target_schema, object_type,
                     objects_list, object_category, object_category_folder,
                     restart_flag, project_DB_details, dr_DB_details, target_DB_details, db_module_path, token_data,
                     modules_data, scripts_path, rules_data, objects_data, decrypt_key):
    import_object = import_file(db_module_path)
    function_call = getattr(import_object, 'connect_database')

    request_id = ''
    try:
        print("schema name ", schema)
        target_function_call = getattr(import_object, 'target_DB_connection')

        project_connection = function_call(project_DB_details)
        request_id = request_insert(project_connection, iteration_id, source_connection_id, task_name, object_category,
                                    schema, object_type)[0]

        source_path = root_path + '/' + project_folder + '/' + str(
            iteration_id) + '/' + 'Source' + '/' + schema.capitalize()

        conversion_path = root_path + '/' + project_folder + '/' + str(
            iteration_id) + '/' + 'Conversion' + '/' + schema.capitalize()
        if not os.path.exists(conversion_path):
            os.makedirs(conversion_path)

        single_file_output_path = root_path + '/' + project_folder + '/' + 'Single_File_Output'
        if not os.path.exists(single_file_output_path):
            os.makedirs(single_file_output_path)

        output_deployment_path = root_path + '/' + project_folder + '/' + str(
            iteration_id) + '/' + 'Deployment_Logs'
        if not os.path.exists(output_deployment_path):
            os.makedirs(output_deployment_path)

        failed_deployment_path = root_path + '/' + project_folder + '/' + str(
            iteration_id) + '/' + 'Failed_Objects'
        if not os.path.exists(failed_deployment_path):
            os.makedirs(failed_deployment_path)

        execution_log_path = root_path + '/' + project_folder + '/' + str(
            iteration_id) + '/' + 'Execution_Logs' + '/' + 'Conversion'
        if not os.path.exists(execution_log_path):
            os.makedirs(execution_log_path)

        execution_file_path = execution_log_path + '/' + str(
            iteration_id) + '_' + object_category_folder + '_' + schema.capitalize() + '_execution_{}.log'.format(
            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

        conversion_file_path = source_path + '/' + 'Conversion_Files.xlsx'
        packages_file_path = source_path + '/' + str(
            iteration_id) + '_' + schema.capitalize() + '_Package_Extraction.csv'
        column_data_pacakegs_list = []
        if os.path.exists(packages_file_path):
            df = pd.read_csv(packages_file_path)  # Replace with your CSV file path
            if not df.empty and len(df.columns) > 0:
                column_data_pacakegs_list = df['Object_Name'].tolist()

        original_stdout = sys.stdout
        original_stderr = sys.stderr
        log_file = open(execution_file_path, "w")
        sys.stdout = DualStream(log_file, original_stdout)
        sys.stderr = DualStream(log_file, original_stderr)

        for object_type in objects_list:
            try:
                if object_type == 'Package_Procedure':
                    rules_object_name = 'Procedure'
                elif object_type == 'Package_Function':
                    rules_object_name = 'Function'
                elif object_type == 'Trigger_Function':
                    rules_object_name = 'Function'
                elif object_type == 'Trigger_Specification':
                    rules_object_name = 'Trigger'
                else:
                    rules_object_name = object_type

                object_rules_data = rules_data[(rules_data['Migration_Name'] == migration_name) & (
                        rules_data['Object_Path'] == rules_object_name)]

                if not object_rules_data.empty:
                    object_conversion_path = conversion_path + '/' + object_type
                    if not os.path.exists(object_conversion_path):
                        os.makedirs(object_conversion_path)

                    object_source_path = source_path + '/' + object_type
                    if os.path.exists(source_path):
                        object_files_list = os.listdir(object_source_path)

                        partition_names_list = []
                        partition_source_path = source_path + '/' + 'Partition'
                        if os.path.exists(partition_source_path):
                            partition_names_list = os.listdir(partition_source_path)

                        partition_objects_list = [i.split('-')[0] + '.sql' for i in
                                                  [j.split('/')[-1] for j in object_files_list] if
                                                  i.split('-')[0] + '.sql' in partition_names_list]

                        if object_type == 'Table':
                            object_files_list = [i for i in object_files_list if i not in partition_names_list]

                        object_files_list = [object_source_path + '/' + file_i for file_i in object_files_list]

                        object_status_file = source_path + '/' + str(
                            iteration_id) + '_' + schema.capitalize() + '_' + object_type + '_Conversion.csv'

                        converted_objects_list = []
                        if not os.path.isfile(object_status_file) or restart_flag == 'True':
                            csv_file = open(object_status_file, mode='w', newline='')
                            writer = csv.writer(csv_file)
                            writer.writerow(['Object_Name', 'Conversion_Status'])
                            csv_file.flush()
                        else:
                            if os.path.isfile(object_status_file):
                                try:
                                    converted_df = pd.read_csv(object_status_file)
                                    # converted_objects_list = converted_df['Object_Name'].values.tolist()
                                    converted_objects_list = \
                                        converted_df[converted_df['Conversion_Status'] == 'Converted and Deployed'][
                                            'Object_Name'].values.tolist()
                                    os.remove(object_status_file)
                                    csv_file = open(object_status_file, mode='w', newline='')
                                    writer = csv.writer(csv_file)
                                    writer.writerow(['Object_Name', 'Conversion_Status'])
                                    csv_file.flush()
                                except Exception as restart_error:
                                    print(
                                        f'Error occurred at reading {object_type} sheet from excel path {object_status_file} : {str(restart_error)}')

                        if not os.path.exists(single_file_output_path + '/' + object_type):
                            os.makedirs(single_file_output_path + '/' + object_type)
                        single_file_output_file_path = single_file_output_path + '/' + object_type + '/' + str(
                            iteration_id) + '_' + str(
                            dr_DB_details[
                                'db_name']).capitalize() + '_~_' + target_schema.capitalize() + '_~_' + object_type + '_Single_File_Output_{}.sql'.format(
                            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                        if not os.path.exists(output_deployment_path + '/' + object_type):
                            os.makedirs(output_deployment_path + '/' + object_type)
                        output_deployment_file_path = output_deployment_path + '/' + object_type + '/' + str(
                            iteration_id) + '_' + target_schema.capitalize() + '_' + object_type + '_Deploy_Log_{}.log'.format(
                            datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                        if not os.path.exists(failed_deployment_path + '/' + object_category):
                            os.makedirs(failed_deployment_path + '/' + object_category)

                        failed_objects_file_path = failed_deployment_path + '/' + object_category + '/'+ str(
                            iteration_id) + '_' + target_schema.capitalize() + '_' + object_type+'_{}.sql'.format(datetime.now().strftime('%Y_%m_%d_%Hh_%Mm_%Ss'))

                        for index, object_name in enumerate(object_files_list):
                            object_converted_output = ''
                            try:
                                if restart_flag == 'False':
                                    if object_name.split('/')[-1].strip() not in converted_objects_list:
                                        pass
                                    else:
                                        object_status_df = pd.DataFrame(
                                            [(object_name.split('/')[-1], 'Already Converted and Deployed')],
                                            columns=['Object_Name', 'Conversion_Status'])
                                        object_status_df.to_csv(csv_file, header=False, index=False)
                                        csv_file.flush()

                                        print(f"Skipping {object_name.split('/')[-1]} as it is already Converted ")
                                        continue

                                original_source_code, object_converted_output = object_conversion_trigger(schema, rules_object_name,
                                                                                    object_name,
                                                                                    modules_data, rules_data,
                                                                                    objects_data, scripts_path,
                                                                                    working_directory_path,
                                                                                    conversion_file_path, token_data,
                                                                                    decrypt_key,
                                                                                    column_data_pacakegs_list)

                                if object_type not in ['Table', 'Procedure', 'Function', 'Package', 'Package_Procedure',
                                                       'Package_Function', 'Partition']:
                                    if object_name.split('/')[-1].split('-')[0] + '.sql' in partition_objects_list:
                                        with open(partition_source_path + '/' + object_name.split('/')[-1].split('-')[
                                            0] + '.sql', 'r') as fd:
                                            partion_script = fd.read()
                                            partiton_column = re.findall(r'\bPARTITION\s*BY\s*\w+\s*\((.*?)\)',
                                                                         partion_script, flags=re.DOTALL | re.I)
                                            if partiton_column:
                                                partiton_column = partiton_column[0].replace('"', '').strip()
                                                constraint_keys = re.findall(r'\((.*?)\)', object_converted_output,
                                                                             flags=re.DOTALL | re.I)
                                                for ck in constraint_keys:
                                                    if partiton_column not in ck:
                                                        ck_mod = ck + ',' + partiton_column
                                                        object_converted_output = re.sub(rf'\b{re.escape(ck)}\b',
                                                                                         ck_mod,
                                                                                         object_converted_output,
                                                                                         flags=re.DOTALL | re.I).replace(
                                                            '"', '')

                                object_converted_output = re.sub(rf'{schema}\.', target_schema + '.',
                                                                 object_converted_output,
                                                                 flags=re.IGNORECASE | re.DOTALL)
                                object_converted_output = pg_formatter(object_converted_output)

                                with open(object_conversion_path + '/' + object_name.split('/')[-1], 'w') as f:
                                    f.write("{}\n\n\n".format(object_converted_output))

                                print(f"Conversion completed for {object_type}.{object_name.split('/')[-1]}")

                                dr_connection, error = target_function_call(dr_DB_details)
                                deploy_error = deploy_object(dr_connection, object_converted_output)

                                status_msgs = ['table can have only one primary key', 'existing object',
                                               'already NOT NULL',
                                               'already indexed', 'already exists', 'column default value expression',
                                               'Duplicate key name ', 'Multiple primary key defined']

                                file_name = object_name.split('/')[-1]
                                filename_without_ext = file_name.split('.')[0]
                                if deploy_error != '' and all(
                                        str(i).lower() not in str(deploy_error).lower() for i in status_msgs):
                                    deploy_message = f"Error occurred at deployment of object {file_name} : {str(deploy_error)}"

                                    with open(failed_objects_file_path, 'a') as f:
                                        f.write("\n\n\n{}".format(original_source_code))

                                    target_objects_insert(project_connection, iteration_id, source_connection_id,
                                                          schema,
                                                          dr_connection_id, target_schema, object_type,
                                                          filename_without_ext,
                                                          object_converted_output, False)

                                    target_deployment_insert(project_connection, iteration_id, source_connection_id,
                                                             schema,
                                                             object_type, filename_without_ext, None, None,
                                                             False, deploy_error)

                                    object_status_df = pd.DataFrame(
                                        [(object_name.split('/')[-1], 'Converted and Undeployed')],
                                        columns=['Object_Name', 'Conversion_Status'])
                                    object_status_df.to_csv(csv_file, header=False, index=False)
                                    csv_file.flush()

                                else:
                                    deploy_message = f"Deployment completed successfully for object {file_name} : {str(deploy_error)}"

                                    with open(single_file_output_file_path, 'a') as f:
                                        f.write("\n\n\n{}".format(object_converted_output))

                                    target_objects_insert(project_connection, iteration_id, source_connection_id,
                                                          schema,
                                                          dr_connection_id, target_schema, object_type,
                                                          filename_without_ext,
                                                          object_converted_output, True)

                                    object_status_df = pd.DataFrame(
                                        [(object_name.split('/')[-1], 'Converted and Deployed')],
                                        columns=['Object_Name', 'Conversion_Status'])
                                    object_status_df.to_csv(csv_file, header=False, index=False)
                                    csv_file.flush()

                                    if target_connection_id != '' and dr_connection_id != target_connection_id and object_type in [
                                        'Table', 'Partition']:
                                        target_connection, error = target_function_call(target_DB_details)
                                        target_deploy_error = deploy_object(target_connection, object_converted_output)

                                        if deploy_error != '' and all(
                                                str(i).lower() not in str(target_deploy_error).lower() for i in
                                                status_msgs):
                                            print(
                                                f"Error occurred at deployment of object {file_name} in target DB: {str(target_deploy_error)}")
                                        else:
                                            print(
                                                f"Deployment completed successfully for object {file_name} in target DB: {str(target_deploy_error)}")

                                with open(output_deployment_file_path, 'a') as f:
                                    f.write("\n\n{}".format(deploy_message))
                            except Exception as object_name_error:
                                print(
                                    f"Error occurred at conversion of {object_type}.{object_name.split('/')[-1]} :{str(object_name_error)}")
            except Exception as object_error:
                print(f"Error occurred at object {object_type} is {str(object_error)}")

        project_connection = function_call(project_DB_details)
        request_update(project_connection, request_id, 'Completed', None)
    except Exception as schema_error:
        print(f"Error occurred at schema {schema} is {str(schema_error)}")

        project_connection = function_call(project_DB_details)
        request_update(project_connection, request_id, 'Error', str(schema_error))


def conversion_trigger(task_name, project_id, migration_name, iteration_id, object_category, object_type,
                       schema_name, target_schema, source_connection_id, dr_connection_id, target_connection_id,
                       restart_flag, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    print(f'Performing {task_name} process')
    config_path = local_root_path + '/' + 'config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')
        if not os.path.exists(working_directory_path):
            os.makedirs(working_directory_path)

        code_objects_list, storage_objects_list = [], []

        xml_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'extraction_queries.xml'
        tree = ET.parse(xml_path)
        root = tree.getroot()
        code_objects_tags = root.find('Extraction_Queries/Code_Objects')
        for code_tag in code_objects_tags.iter():
            code_objects_list.append(code_tag.tag)
        code_objects_list = [i for i in code_objects_list if
                             i not in ['Code_Objects', 'ListQuery', 'DefinitionQuery', 'Query']]
        if 'Package' in code_objects_list:
            code_objects_list = [item for item in code_objects_list if item != 'Package'] + ['Package_Procedure',
                                                                                             'Package_Function']
        if 'Trigger' in code_objects_list:
            code_objects_list = [item for item in code_objects_list if item != 'Trigger'] + ['Trigger_Specification',
                                                                                             'Trigger_Function']

        storage_objects_tags = root.find('Extraction_Queries/Storage_Objects')
        for storage_tag in storage_objects_tags.iter():
            storage_objects_list.append(storage_tag.tag)
        storage_objects_list = [i for i in storage_objects_list if
                                i not in ['Storage_Objects', 'ListQuery', 'DefinitionQuery', 'Query',
                                          'QueryUpperVersion',
                                          'QueryLowerVersion']]

        objects_list = []
        object_category_folder = ''
        if object_category == 'Code_Objects':
            objects_list = code_objects_list
            object_category_folder = object_category
        elif object_category == 'Storage_Objects':
            objects_list = storage_objects_list
            object_category_folder = object_category
        elif object_category == 'All':
            objects_list = storage_objects_list + code_objects_list
            object_category_folder = 'All_Objects'

        if object_type.capitalize().strip() == 'All':
            objects_list = objects_list
        else:
            objects_list = [i for i in objects_list if i in object_type.strip().split(',')]

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)

            project_folder = 'PRJ' + project_id + 'SRC'

            conversion_modules_path = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '.csv'
            modules_data = pd.read_csv(conversion_modules_path)

            scripts_path = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/'
            sys.path.append(scripts_path)

            dynamic_rules_path = local_root_path + '/' + 'Dynamic_Rules' + '/' + migration_name + '/' + migration_name + '.csv'
            rules_data = pd.read_csv(dynamic_rules_path)

            objects_excel = local_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '_objects_data.csv'
            objects_data = pd.read_csv(objects_excel)

            if schema_name.capitalize() == 'All':
                schema_list = os.listdir(root_path + '/' + project_folder + '/' + 'Source')
            else:
                schema_list = schema_name.strip().split(',')
            print(schema_list)

            token_data = api_authentication()
            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')

            dr_DB_details = decrypt_database_details(token_data, project_id, 'Target', dr_connection_id)

            target_DB_details = {}
            if target_connection_id != '':
                target_DB_details = decrypt_database_details(token_data, project_id, 'Target', target_connection_id)

            decrypt_key = get_decryption_key(token_data)

            import_object = import_file(db_module_path)
            function_call = getattr(import_object, 'connect_database')
            target_function_call = getattr(import_object, 'target_DB_connection')
            execute_function_call = getattr(import_object, 'execute_query')

            for schema in schema_list:
                try:
                    if object_type.capitalize().strip() == 'All' and object_category == 'Storage_Objects' :
                        dr_connection, error = target_function_call(dr_DB_details)
                        drop_schema_query = f'drop schema if exists {target_schema} cascade'
                        execute_function_call(dr_connection, drop_schema_query)

                        create_schema_query = f'create schema if not exists {target_schema}'
                        execute_function_call(dr_connection, create_schema_query)

                        project_connection = function_call(project_DB_details)
                        schema_insert(project_connection, schema, dr_connection_id)

                        if target_connection_id != '':
                            target_connection, error = target_function_call(target_DB_details)
                            drop_schema_query = f'drop schema if exists {target_schema} cascade'
                            execute_function_call(target_connection, drop_schema_query)

                            create_schema_query = f'create schema if not exists {target_schema}'
                            execute_function_call(target_connection, create_schema_query)

                            schema_insert(project_connection, schema, target_connection_id)

                    schema_execution(task_name, migration_name, root_path, project_folder, working_directory_path,
                                     iteration_id, source_connection_id, dr_connection_id, target_connection_id,
                                     schema, target_schema, object_type, objects_list, object_category,
                                     object_category_folder, restart_flag,
                                     project_DB_details, dr_DB_details, target_DB_details, db_module_path,
                                     token_data, modules_data, scripts_path,
                                     rules_data, objects_data, decrypt_key)
                except Exception as error:
                    print(f"Error occurred at schema creation in target {target_schema}: {error}")
        else:
            print(f'Module path {db_module_path} not found')
    else:
        print(f'Config path {config_path} not found')
