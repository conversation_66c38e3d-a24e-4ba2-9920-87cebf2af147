import os, sys, subprocess
from import_file import import_file
from Common_modules.api import api_authentication, decrypt_database_details
from Common_modules.stored_procedures import deploy_status_insertion, insert_data_compare_status,get_python_code


def manual_conversion_trigger(project_id, migration_name, iteration_id, object_type, object_name, table_name,
                              time_taken, observation_text, cloud_category):
    local_root_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    config_path = local_root_path + '/' + 'Path_Config' + '/' + 'Config.py'
    if os.path.isfile(config_path):
        sys.path.append(config_path)
        import_object = import_file(config_path)
        root_path = getattr(import_object, cloud_category.capitalize() + '_Path')

        working_directory_path = getattr(import_object, cloud_category.capitalize() + '_Directory_Path')

        db_module_path = local_root_path + '/' + 'Migrations' + '/' + migration_name + '/' + 'db_connection.py'
        if os.path.isfile(db_module_path):
            sys.path.append(db_module_path)
            import_object = import_file(db_module_path)

            token_data = api_authentication()

            project_DB_details = decrypt_database_details(token_data, project_id, 'Project', '')
            project_function_call = getattr(import_object, 'connect_database')
            project_connection = project_function_call(project_DB_details)

            result_data = get_python_code(project_connection,iteration_id,object_name)

            if result_data:
                source_DB_details = decrypt_database_details(token_data, project_id, 'Source', str(result_data[0][3]))

                source_code = result_data[0][1].replace('@endpoint_url',source_DB_details['host'])
                source_code = source_code.replace('@db_name', source_DB_details['db_name'])
                source_code = source_code.replace('@table_name',table_name)

                source_file = working_directory_path +'/' + object_name + '_source_run.py'

                with open(source_file,'w') as f:
                    f.write(source_code)
                source_result = subprocess.run(["python", source_file], capture_output=True, text=True)
                source_output = source_result.stdout
                source_error = source_result.stderr


                target_DB_details = decrypt_database_details(token_data, project_id, 'Target', str(result_data[0][4]))

                target_code = result_data[0][2].replace('@endpoint_url', target_DB_details['host'])
                target_code = target_code.replace('@key', target_DB_details['password'])
                target_code = target_code.replace('@db_name', target_DB_details['db_name'])
                target_code = target_code.replace('@table_name', table_name)

                target_file = working_directory_path + '/' + object_name + '_target_run.py'

                with open(target_file, 'w') as f:
                    f.write(target_code)
                target_result = subprocess.run(["python", target_file], capture_output=True, text=True)
                target_output = target_result.stdout
                target_error = target_result.stderr

                if source_error != '' or target_error != '':
                    error = source_error + '\n\n' + target_error
                    deploy_status_insertion(project_connection, iteration_id, 'public', object_type,
                                            object_name, 'Undeployed', str(error),
                                            str(result_data[0][4]), str(result_data[0][5]), time_taken, observation_text)
                else:
                    deploy_status_insertion(project_connection, iteration_id, 'public', object_type,
                                            object_name, 'Deployed', '',
                                            str(result_data[0][4]), str(result_data[0][5]), time_taken, observation_text)

                    source_output =  source_output.replace("'",'"')
                    target_output = target_output.replace("'", '"')
                    insert_data_compare_status(project_connection, iteration_id,migration_name, str(result_data[0][3]), str(result_data[0][4]),
                                               table_name.lower(),
                                               object_name.lower(), str(source_output),str(target_output),None)
    else:
        print('Config file Not Found')