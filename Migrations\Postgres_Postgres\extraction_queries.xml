<Queries>
    <Extraction_Queries>
        <Code_Objects>

            <Procedure>
                <ListQuery>
                    SELECT p.proname AS object_name,
                    'VALID' AS status
                    FROM pg_proc p
                    JOIN pg_namespace n ON p.pronamespace = n.oid
                    WHERE lower(n.nspname) = lower('@schemaname')
                    AND p.prokind IN ('p')
                    ORDER BY object_name

                </ListQuery>
                <DefinitionQuery>
                    WITH x AS (
                    SELECT
                    n.nspname AS schema_name,
                    p.proname AS object_name,
                    p.prokind AS object_type, -- 'p' for procedures, 'f' for functions
                    p.oid AS object_id
                    FROM pg_proc p
                    JOIN pg_namespace n ON p.pronamespace = n.oid
                    WHERE lower(n.nspname) = lower('@schemaname')
                    AND p.prokind = 'p'
                    AND lower(p.proname) = lower('@name')
                    )
                    SELECT pg_get_functiondef(x.object_id) AS ddlcode FROM x

                </DefinitionQuery>
            </Procedure>
            <Function>
                <ListQuery>

                    SELECT p.proname AS object_name,
                    'VALID' AS status
                    FROM pg_proc p
                    JOIN pg_namespace n ON p.pronamespace = n.oid
                    WHERE lower(n.nspname) = lower('@schemaname')
                    AND p.prokind IN ('f')
                    ORDER BY object_name

                </ListQuery>
                <DefinitionQuery>
                    WITH x AS (
                    SELECT
                    n.nspname AS schema_name,
                    p.proname AS object_name,
                    p.prokind AS object_type, -- 'p' for procedures, 'f' for functions
                    p.oid AS object_id
                    FROM pg_proc p
                    JOIN pg_namespace n ON p.pronamespace = n.oid
                    WHERE lower(n.nspname) = lower('@schemaname')
                    AND lower(p.proname) = lower('@name')
                    )
                    SELECT pg_get_functiondef(x.object_id) AS ddlcode FROM x

                </DefinitionQuery>
            </Function>
            <Trigger>
                <ListQuery>
                    SELECT
                    trg.tgname AS object_name,
                    CASE trg.tgenabled
                    WHEN 'O' THEN 'ENABLED'
                    WHEN 'D' THEN 'DISABLED'
                    ELSE 'UNKNOWN'
                    END AS status
                    FROM pg_trigger trg
                    JOIN pg_class cls ON trg.tgrelid = cls.oid
                    JOIN pg_namespace nsp ON cls.relnamespace = nsp.oid
                    WHERE lower(nsp.nspname) = lower('@schemaname') and lower(trg.tgname) not like '%constraint%'
                </ListQuery>
                <DefinitionQuery>

                    WITH x AS (
                    SELECT
                    n.nspname AS schema_name,
                    t.tgname AS object_name,
                    'TRIGGER' AS object_type,
                    c.relname AS table_name
                    FROM pg_trigger t
                    JOIN pg_class c ON t.tgrelid = c.oid
                    JOIN pg_namespace n ON c.relnamespace = n.oid
                    WHERE lower(n.nspname) = lower('@schemaname') -- Replace 'adt' with your schema name
                    AND lower(t.tgname) = lower('@name') -- Replace 'trigger_name' with the actual
                    trigger name
                    )
                    SELECT pg_get_triggerdef(t.oid, true) AS ddlcode
                    FROM pg_trigger t
                    JOIN pg_class c ON t.tgrelid = c.oid
                    JOIN pg_namespace n ON c.relnamespace = n.oid
                    WHERE lower(n.nspname) = lower('@schemaname')
                    AND lower(t.tgname) = lower('@name')
                </DefinitionQuery>
            </Trigger>
            <Job>
                <Query>
                    SELECT COUNT(1)
                    FROM pg_catalog.pg_class c
                    JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                    WHERE c.relkind = 'j'
                    AND upper(n.nspname) = upper('@schemaname')
                </Query>
            </Job>
            <Schedule>
                <Query>
                    SELECT COUNT(1)
                    FROM pg_catalog.pg_job j
                    JOIN pg_catalog.pg_namespace n ON j.jobnamespace = n.oid
                    WHERE upper(n.nspname) = upper('@schemaname')

                </Query>
            </Schedule>
            <Program>
                <Query>
                    SELECT COUNT(1)
                    FROM pg_catalog.pg_proc p
                    JOIN pg_catalog.pg_namespace n ON p.pronamespace = n.oid
                    WHERE upper(n.nspname) = upper('@schemaname')

                </Query>
            </Program>
        </Code_Objects>
        <Storage_Objects>
            <Type>
                <ListQuery>
                    SELECT t.typname AS object_name,
                    'VALID' AS status
                    FROM pg_catalog.pg_type t
                    JOIN pg_catalog.pg_namespace n ON t.typnamespace = n.oid
                    WHERE upper(n.nspname) = upper('@schemaname')
                    AND t.typtype = 'c'
                    AND t.typname NOT IN (
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE upper(table_schema) = upper('@schemaname')
                    )
                    AND t.typname NOT IN (
                    SELECT relname
                    FROM pg_catalog.pg_class c
                    JOIN pg_catalog.pg_namespace ns ON c.relnamespace = ns.oid
                    WHERE upper(ns.nspname) = upper('@schemaname') AND c.relkind = 'S'
                    )
                </ListQuery>
                <DefinitionQuery>
                    with type_info as (
                    select
                    n.nspname as schema_name,
                    t.typname as type_name,
                    t.typtype as type_kind,
                    t.oid
                    from
                    pg_catalog.pg_type t
                    join pg_catalog.pg_namespace n on
                    t.typnamespace = n.oid
                    where
                    upper(t.typname) = upper('@name')
                    and upper(n.nspname) = upper('@schemaname')
                    )
                    select
                    case
                    when type_kind = 'e' then
                    'CREATE TYPE ' || schema_name || '.' || type_name || ' AS ENUM (' ||
                    (
                    select
                    string_agg(quote_literal(e.enumlabel),
                    ', ')
                    from
                    pg_enum e
                    where
                    e.enumtypid = oid) || ');'
                    when type_kind = 'c' then
                    'CREATE TYPE ' || schema_name || '.' || type_name || ' AS (' ||
                    (
                    select
                    string_agg(
                    quote_ident(a.attname) || ' ' || pg_catalog.format_type(a.atttypid,
                    a.atttypmod),
                    ', ')
                    from
                    pg_attribute a
                    where
                    a.attrelid = (
                    select
                    oid
                    from
                    pg_class
                    where
                    relname = type_name)
                    and a.attnum > 0
                    and not a.attisdropped) || ');'
                    when type_kind = 'd' then
                    'CREATE DOMAIN ' || schema_name || '.' || type_name || ' AS ' ||
                    (
                    select
                    pg_catalog.format_type(t.typbasetype,
                    null)
                    from
                    pg_type t
                    where
                    t.oid = type_info.oid) ||
                    (
                    select
                    case
                    when t.typnotnull then ' NOT NULL'
                    else ''
                    end
                    from
                    pg_type t
                    where
                    t.oid = type_info.oid) ||
                    (
                    select
                    case
                    when t.typdefault is not null then ' DEFAULT ' || t.typdefault
                    else ''
                    end
                    from
                    pg_type t
                    where
                    t.oid = type_info.oid) || ';'
                    else '/* Unsupported type: ' || schema_name || '.' || type_name || ' */'
                    end as ddl_statement
                    from
                    type_info
                </DefinitionQuery>
            </Type>
            <Sequence>
                <ListQuery>

                    SELECT c.relname AS object_name,
                    'VALID' AS status
                    FROM pg_catalog.pg_class c
                    JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                    WHERE upper(n.nspname) = upper('@schemaname')
                    AND c.relkind = 'S' -- 'S' stands for sequence
                    AND lower(c.relname) NOT LIKE lower('ISEQ%')
                    AND lower(c.relname) NOT LIKE lower('%$%')


                </ListQuery>
                <DefinitionQuery>

                    SELECT lower(sequence_name) as Sequence_ddl
                    FROM (
                    SELECT 'CREATE SEQUENCE ' || n.nspname || '.' || c.relname ||
                    ' MINVALUE ' || s.min_value ||
                    ' MAXVALUE ' || CASE WHEN s.max_value = 9223372036854775807 THEN 99999999999999999 ELSE s.max_value
                    END ||
                    ' INCREMENT BY ' || s.increment_by ||
                    ' START WITH ' || s.start_value ||
                    ' CACHE ' || s.cache_size ||
                    ' NO CYCLE;' AS sequence_name
                    FROM pg_catalog.pg_class c
                    JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                    JOIN pg_catalog.pg_sequences s ON c.relname = s.sequencename -- Fixed join condition
                    WHERE upper(n.nspname) = upper('@schemaname')
                    AND upper(c.relname) = upper('@name')
                    AND lower(c.relname) NOT LIKE lower('ISEQ%')
                    ) AS seq_query




                </DefinitionQuery>
            </Sequence>
            <Table>
                <ListQuery>

                    SELECT distinct c.relname AS table_name,'VALID'as STATUS
                    FROM pg_catalog.pg_class c
                    JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                    WHERE upper(n.nspname) = upper('@schemaname')
                    AND c.relkind = 'r'
                    ORDER BY c.relname
                </ListQuery>
                <DefinitionQuery>

                    SELECT 'CREATE TABLE ' || n.nspname || '.' || c.relname || ' (' ||
                    string_agg(
                    a.attname || ' ' || pg_catalog.format_type(a.atttypid, a.atttypmod) ||
                    CASE WHEN a.attnotnull THEN ' NOT NULL' ELSE '' END, ', ')
                    || ');' AS ddlcode
                    FROM pg_catalog.pg_class c
                    JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                    JOIN pg_catalog.pg_attribute a ON a.attrelid = c.oid
                    WHERE lower(n.nspname) = lower('@schemaname')
                    AND upper(c.relname) = upper('@name')
                    AND a.attnum > 0 -- Only consider user-defined columns (not system columns)
                    AND NOT a.attisdropped -- Exclude dropped columns
                    GROUP BY n.nspname, c.relname
                </DefinitionQuery>
            </Table>
<!--            <Partition>-->
<!--                <ListQuery>-->
<!--                    SELECT table_name,status-->
<!--                    FROM all_tables-->
<!--                    WHERE partitioned = 'YES'-->
<!--                    AND upper(owner) = upper('@schemaname')-->
<!--                    and table_name not like '%$%'-->
<!--                </ListQuery>-->
<!--                <DefinitionQuery>-->
<!--                    SELECT CONCAT(DBMS_METADATA.GET_DDL('TABLE', table_name, owner),';') AS partition_table_def-->
<!--                    FROM all_tables-->
<!--                    WHERE partitioned = 'YES'-->
<!--                    AND upper(owner) = upper('@schemaname')-->
<!--                    AND upper(table_name) = upper('@name')-->
<!--                </DefinitionQuery>-->
<!--            </Partition>-->
            <Not_Null_Constraint>
                <ListQuery>
                    SELECT c.relname || '-' || a.attname AS column_name_status,
                    'ENABLED' AS status
                    FROM pg_catalog.pg_attribute a
                    JOIN pg_catalog.pg_class c ON a.attrelid = c.oid
                    JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                    WHERE a.attnotnull = true
                    AND lower(n.nspname) = lower('@schemaname')
                    AND c.relkind = 'r'
                    AND c.relname NOT LIKE '%$%'
                    AND a.attnum > 0
                    ORDER BY n.nspname, c.relname, a.attname



                </ListQuery>
                <DefinitionQuery>
                    with x as(
                    SELECT 'ALTER TABLE ' || n.nspname || '.' || c.relname || ' ALTER COLUMN ' || a.attname || ' SET NOT
                    NULL;' as ddl ,
                    c.relname as realname,a.attname as attributename
                    FROM pg_catalog.pg_attribute a
                    JOIN pg_catalog.pg_class c ON a.attrelid = c.oid
                    JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                    WHERE a.attnotnull = true
                    AND lower(n.nspname) = lower('@schemaname')
                    AND c.relkind = 'r'
                    AND a.attnum > 0 )
                    select ddl from x where upper(x.realname || '-' ||x.attributename) =
                    upper('@name')
                    ORDER BY x.realname, x.attributename

                </DefinitionQuery>
            </Not_Null_Constraint>
            <Primary_Key>
                <ListQuery>
                    SELECT
                    ac.table_name || '-' || ac.constraint_name AS constraint_info,
                    CASE
                    WHEN ac.constraint_name IS NOT NULL THEN 'VALID'
                    ELSE 'INVALID'
                    END AS status
                    FROM
                    information_schema.table_constraints ac
                    JOIN (
                    SELECT
                    acc.table_schema,
                    acc.table_name,
                    acc.constraint_name,
                    string_agg(acc.column_name, ',' ORDER BY acc.ordinal_position) AS col_list
                    FROM
                    information_schema.key_column_usage acc
                    GROUP BY
                    acc.table_schema,
                    acc.table_name,
                    acc.constraint_name
                    ) ccl ON ac.table_schema = ccl.table_schema
                    AND ac.table_name = ccl.table_name
                    AND ac.constraint_name = ccl.constraint_name
                    WHERE
                    upper(ac.table_schema) = UPPER('@schemaname')
                    AND ac.constraint_type = 'PRIMARY KEY'
                    AND ac.constraint_name NOT LIKE '%$%'
                    AND ac.table_name NOT LIKE '%$%'
                    ORDER BY
                    ac.table_schema

                </ListQuery>
                <DefinitionQuery>


                    SELECT
                    'ALTER TABLE ' || ac.table_schema || '.' || ac.table_name ||
                    ' ADD CONSTRAINT ' || ac.constraint_name ||
                    ' PRIMARY KEY(' || ccl.col_list || ');' as DDl
                    FROM
                    information_schema.table_constraints ac
                    JOIN (
                    SELECT
                    acc.table_schema,
                    acc.table_name,
                    acc.constraint_name,
                    STRING_AGG(acc.column_name, ',' ORDER BY acc.ordinal_position) AS col_list
                    FROM
                    information_schema.key_column_usage acc
                    GROUP BY
                    acc.table_schema, acc.table_name, acc.constraint_name
                    ) ccl
                    ON ac.table_schema = ccl.table_schema
                    AND ac.table_name = ccl.table_name
                    AND ac.constraint_name = ccl.constraint_name
                    WHERE
                    upper(ac.table_schema) = UPPER('@schemaname')
                    AND ac.constraint_type = 'PRIMARY KEY'
                    AND ac.constraint_name NOT LIKE '%$%'
                    AND upper(ac.table_name || '-' || ac.constraint_name) = upper('@name')
                    ORDER BY
                    ac.table_schema

                </DefinitionQuery>
            </Primary_Key>
            <Unique_Constraint>
                <ListQuery>
                    SELECT
                    ac.table_name || '-' || ac.constraint_name AS "ConstraintName",
                    'VALID' AS status
                    FROM
                    information_schema.table_constraints ac
                    JOIN (
                    SELECT
                    acc.table_schema,
                    acc.table_name,
                    acc.constraint_name,
                    STRING_AGG(acc.column_name, ',' ORDER BY acc.ordinal_position) AS col_list
                    FROM
                    information_schema.key_column_usage acc
                    GROUP BY
                    acc.table_schema, acc.table_name, acc.constraint_name
                    ) ccl
                    ON ac.table_schema = ccl.table_schema
                    AND ac.table_name = ccl.table_name
                    AND ac.constraint_name = ccl.constraint_name
                    WHERE
                    upper(ac.table_schema) = UPPER('@schemaname')
                    AND ac.constraint_type = 'UNIQUE'
                    AND ac.constraint_name NOT LIKE '%$%'
                    AND ac.table_name NOT LIKE '%$%'
                    ORDER BY
                    ac.table_schema
                </ListQuery>
                <DefinitionQuery>


                    SELECT
                    'ALTER TABLE ' || ac.table_schema || '.' || ac.table_name || ' ADD CONSTRAINT ' ||
                    ac.constraint_name ||
                    ' UNIQUE  (' || ccl.col_list || ');' as DDl
                    FROM
                    information_schema.table_constraints ac
                    JOIN (
                    SELECT
                    acc.table_schema,
                    acc.table_name,
                    acc.constraint_name,
                    STRING_AGG(acc.column_name, ',' ORDER BY acc.ordinal_position) AS col_list
                    FROM
                    information_schema.key_column_usage acc
                    GROUP BY
                    acc.table_schema, acc.table_name, acc.constraint_name
                    ) ccl
                    ON ac.table_schema = ccl.table_schema
                    AND ac.table_name = ccl.table_name
                    AND ac.constraint_name = ccl.constraint_name
                    WHERE
                    upper(ac.table_schema) = UPPER('@schemaname')
                    AND upper(ac.table_name || '-' || ac.constraint_name) = UPPER('@name')
                    AND ac.constraint_type = 'UNIQUE'
                    AND ac.constraint_name NOT LIKE '%$%'
                    ORDER BY
                    ac.table_schema






                </DefinitionQuery>
            </Unique_Constraint>
            <Foreign_Key>
                <ListQuery>
                    WITH CTE AS (
                    SELECT
                    ac.table_name,
                    ac.constraint_name,
                    'ALTER TABLE ' || ac.table_schema || '.' || ac.table_name || ' ADD CONSTRAINT ' ||
                    ac.constraint_name ||
                    ' FOREIGN KEY (' ||
                    (
                    SELECT
                    STRING_AGG(alc.column_name, ',' ORDER BY alc.ordinal_position)
                    FROM
                    information_schema.key_column_usage alc
                    WHERE
                    alc.constraint_name = ac.constraint_name
                    AND alc.table_schema = ac.table_schema
                    ) || ')' || ' REFERENCES ' ||
                    (
                    SELECT
                    STRING_AGG(alc.table_schema || '.' || alc.table_name, ',' ORDER BY alc.ordinal_position)
                    FROM
                    information_schema.key_column_usage alc
                    JOIN
                    information_schema.table_constraints tc
                    ON alc.constraint_name = tc.constraint_name
                    WHERE
                    tc.constraint_type = 'UNIQUE'
                    AND alc.constraint_name = ac.constraint_name
                    AND alc.table_schema = ac.table_schema
                    ) || '(' ||
                    (
                    SELECT
                    STRING_AGG(alc.column_name, ',' ORDER BY alc.ordinal_position)
                    FROM
                    information_schema.key_column_usage alc
                    WHERE
                    alc.constraint_name = ac.constraint_name
                    AND alc.table_schema = ac.table_schema
                    ) || ')' || ';' AS status
                    FROM
                    information_schema.table_constraints ac
                    WHERE
                    upper(ac.table_schema) = UPPER('@schemaname')
                    AND ac.constraint_type = 'FOREIGN KEY'
                    AND ac.constraint_name NOT LIKE '%$%'
                    AND ac.table_name NOT LIKE '%$%'
                    ORDER BY
                    ac.table_schema
                    )
                    SELECT DISTINCT
                    CTE.table_name || '-' || CTE.constraint_name AS table_constraint,
                    'VALID'as status
                    FROM
                    CTE


                </ListQuery>
                <DefinitionQuery>
                    WITH CTE AS (
                    SELECT
                    'ALTER TABLE ' || ac.table_schema || '.' || ac.table_name || ' ADD CONSTRAINT ' ||
                    ac.constraint_name || ' FOREIGN KEY (' ||
                    (
                    SELECT
                    STRING_AGG(alc.column_name, ',' ORDER BY alc.ordinal_position)
                    FROM
                    information_schema.key_column_usage alc
                    WHERE
                    alc.constraint_name = ac.constraint_name
                    AND alc.table_schema = ac.table_schema
                    ) || ') REFERENCES ' ||
                    rc.unique_constraint_schema || '.' || rc.unique_constraint_name || ' (' ||
                    (
                    SELECT
                    STRING_AGG(alc.column_name, ',' ORDER BY alc.ordinal_position)
                    FROM
                    information_schema.key_column_usage alc
                    WHERE
                    alc.constraint_name = rc.unique_constraint_name
                    AND alc.table_schema = rc.unique_constraint_schema
                    ) || ');' AS constraint_ddl
                    FROM
                    information_schema.table_constraints ac
                    JOIN
                    information_schema.referential_constraints rc
                    ON
                    ac.constraint_name = rc.constraint_name
                    WHERE
                    upper(ac.table_schema) = upper('@schemaname')
                    and upper(ac.table_name||'-'|| ac.constraint_name)=upper('@name')
                    AND ac.constraint_type = 'FOREIGN KEY'
                    ORDER BY
                    ac.table_schema
                    )
                    SELECT DISTINCT constraint_ddl
                    FROM CTE
                </DefinitionQuery>
            </Foreign_Key>
            <Default_Constraint>
                <ListQuery>
                  SELECT
                    table_name || '-' || column_name as Constraints,
                    'ENABLED'
                    FROM
                    information_schema.columns
                    WHERE
                    column_default IS NOT NULL
                    AND upper(table_schema) = UPPER('@schemaname')
                    AND table_name NOT LIKE '%$%'
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' || table_schema || '.' || table_name ||
                    ' ALTER COLUMN ' || column_name || ' SET DEFAULT ' || column_default || ';'
                    FROM
                    information_schema.columns
                    WHERE
                    column_default IS NOT NULL
                    AND upper(table_schema) = UPPER('@schemaname')
                    AND upper(table_name || '-' || column_name) = UPPER('@name')

                </DefinitionQuery>
            </Default_Constraint>
            <Check_Constraint>
                <ListQuery>
                    SELECT
                    ac.table_name || '-' || ac.constraint_name AS constraint_info,
                    ac.constraint_name AS status
                    FROM
                    information_schema.table_constraints ac
                    JOIN
                    information_schema.constraint_column_usage acc
                    ON
                    ac.constraint_name = acc.constraint_name
                    AND ac.table_schema = acc.table_schema
                    WHERE
                    ac.constraint_type = 'CHECK'
                    AND upper(ac.table_schema) = UPPER('@schemaname')
                    AND (ac.table_name || '.' || acc.column_name) NOT IN (
                    SELECT DISTINCT (table_name || '.' || column_name)
                    FROM information_schema.columns
                    WHERE is_nullable = 'NO'
                    )
                    AND ac.constraint_name NOT LIKE '%$%'
                    AND ac.table_name NOT LIKE '%$%'
                    ORDER BY
                    ac.table_schema

                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    'ALTER TABLE ' || ac.table_schema || '.' || ac.table_name ||
                    ' ADD CONSTRAINT ' || ac.constraint_name || ' CHECK (' ||
                    cc.check_clause || ');' AS constraint_ddl
                    FROM
                    information_schema.table_constraints ac
                    JOIN
                    information_schema.check_constraints cc
                    ON
                    ac.constraint_name = cc.constraint_name
                    AND ac.table_schema = cc.constraint_schema
                    WHERE
                    ac.constraint_type = 'CHECK'
                    AND UPPER(ac.table_schema) = UPPER('@schemaname')
                    AND UPPER(ac.table_name || '-' || ac.constraint_name) = UPPER('@name')
                    AND ac.constraint_name NOT LIKE '%$%'
                    ORDER BY
                    ac.table_schema

                </DefinitionQuery>
            </Check_Constraint>
            <Index>
                <ListQuery>
                    SELECT
                    tablename || '-' || indexname AS index_names
                    FROM
                    pg_catalog.pg_indexes
                    WHERE
                    schemaname NOT IN ('pg_catalog')
                    AND schemaname = '@schemaname'
                    AND indexname NOT IN (
                    SELECT
                    conname
                    FROM
                    pg_catalog.pg_constraint
                    WHERE
                    contype IN ('p', 'c', 'u', 'f')
                    )

                </ListQuery>
                <DefinitionQuery>
                    select indexdef from pg_catalog.pg_indexes where upper(schemaname)=upper('@schemaname') and
                    upper(tablename||'-'||indexname)=upper('@name')

                </DefinitionQuery>
            </Index>
            <View>
                <ListQuery>
                    SELECT table_name AS object_name, 'ENABLED' AS status
                    FROM information_schema.views
                    WHERE
                    upper(table_schema) = UPPER('@schemaname')
                    AND
                    table_name NOT LIKE '%$%'
                </ListQuery>
                <DefinitionQuery>
                    SELECT 'CREATE OR REPLACE VIEW ' || table_schema || '.' || table_name || ' AS ' ||
                    pg_get_viewdef(table_schema || '.' || table_name, true) || ';' AS view_ddl
                    FROM information_schema.views
                    WHERE upper(table_schema) = upper('@schemaname')
                    AND upper(table_name) = upper('@name')
                </DefinitionQuery>
            </View>
            <Datatype>
                <Query>
                    SELECT COUNT(1)
                    FROM (
                    SELECT lower(table_schema) AS owner,
                    lower(table_name) AS table_name,
                    lower(column_name) AS column_name,
                    numeric_precision AS data_precision,
                    numeric_scale AS data_scale,
                    character_maximum_length AS data_length
                    FROM information_schema.columns
                    WHERE table_schema = lower('@schemaname')
                    AND data_type = 'numeric'
                    AND table_name NOT LIKE '%$%'
                    AND NOT EXISTS (
                    SELECT 1 FROM information_schema.views v
                    WHERE v.table_schema = lower('@schemaname')
                    AND v.table_name = columns.table_name
                    )
                    AND NOT EXISTS (
                    SELECT 1 FROM pg_matviews mv
                    WHERE mv.schemaname = lower('@schemaname')
                    AND mv.matviewname = columns.table_name
                    )
                    AND NOT EXISTS (
                    SELECT 1 FROM pg_class c
                    JOIN pg_namespace n ON c.relnamespace = n.oid
                    WHERE n.nspname = lower('@schemaname')
                    AND c.relname = columns.table_name
                    AND c.relkind = 's'
                    )
                    ORDER BY table_name
                    ) subquery

                </Query>
            </Datatype>
            <Column>
                <Query>
                    SELECT COUNT(1)
                    FROM information_schema.columns c
                    INNER JOIN information_schema.tables t
                    ON c.table_name = t.table_name
                    AND c.table_schema = t.table_schema
                    WHERE c.table_schema = lower('@schemaname');
                </Query>
            </Column>
        </Storage_Objects>
    </Extraction_Queries>
        <Database_Exclusion_Query>
            SELECT DISTINCT lower(column_name)
            FROM information_schema.columns
            WHERE table_schema NOT IN (
            'pg_catalog', 'information_schema'
            )
            AND column_name IS NOT NULL

            UNION

            SELECT DISTINCT lower(parameter_name)
            FROM information_schema.parameters
            WHERE specific_schema NOT IN (
            'pg_catalog', 'information_schema'
            )
            AND parameter_name IS NOT NULL

            UNION

            SELECT DISTINCT lower(relname)
            FROM pg_catalog.pg_class
            WHERE relnamespace NOT IN (
            SELECT oid FROM pg_catalog.pg_namespace
            WHERE nspname IN ('pg_catalog', 'information_schema')
            )
            AND relkind NOT IN ('i', 'S', 't') -- Excluding indexes, sequences, and TOAST tables
            AND relname NOT LIKE 'pg_%'
            AND relname NOT LIKE '%$%'
            AND relname IS NOT NULL

            UNION

            SELECT DISTINCT lower(proname)
            FROM pg_catalog.pg_proc
            WHERE pronamespace NOT IN (
            SELECT oid FROM pg_catalog.pg_namespace
            WHERE nspname IN ('pg_catalog', 'information_schema')
            )
            AND proname NOT LIKE 'pg_%'
            AND proname IS NOT NULL

    </Database_Exclusion_Query>

    <Source_Schemas>
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('information_schema', 'pg_catalog')
        AND schema_name NOT LIKE 'pg_toast%'
        AND schema_name NOT LIKE 'pg_temp%'
    </Source_Schemas>
    <Target_Schemas>
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('information_schema', 'pg_catalog')
        AND schema_name NOT LIKE 'pg_toast%'
        AND schema_name NOT LIKE 'pg_temp%'
    </Target_Schemas>
</Queries>