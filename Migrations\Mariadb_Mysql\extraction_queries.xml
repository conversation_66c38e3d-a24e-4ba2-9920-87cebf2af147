<Queries>
    <Extraction_Queries>
        <Code_Objects>
            <Package>
                <ListQuery>
                    SELECT ROUTINE_NAME as PACKAGE_NAME,'VALID' as STATUS
                    FROM information_schema.ROUTINES
                    WHERE ROUTINE_TYPE = 'PACKAGE'
                    AND UPPER(ROUTINE_SCHEMA) = UPPER('@schemaname');
                </ListQuery>
                <DefinitionQuery>

                    SELECT
                    group_concat(CASE
                    WHEN ROUTINE_TYPE = 'PACKAGE' THEN
                    CONCAT('CREATE OR REPLACE PACKAGE ', ROUTINE_SCHEMA, '.', ROUTINE_NAME, ' AS\n', ROUTINE_DEFINITION, ';\n')
                    WHEN ROUTINE_TYPE = 'PACKAGE BODY' THEN
                    CONCAT('CREATE OR REPLACE PACKAGE BODY ', ROUTINE_SCHEMA, '.', ROUTINE_NAME, ' AS\n', ROUTINE_DEFINITION, ';\n')
                    end) AS package_ddl
                    FROM
                    information_schema.ROUTINES
                    WHERE
                    UPPER(ROUTINE_SCHEMA) = UPPER('@schemaname')
                    AND ROUTINE_TYPE IN ('PACKAGE', 'PACKAGE BODY')
                    and UPPER(SPECIFIC_NAME) = UPPER('@name')
                    ORDER by
                    ROUTINE_NAME, ROUTINE_TYPE;

                </DefinitionQuery>
            </Package>
            <Procedure>
                <ListQuery>
                    SELECT ROUTINE_NAME as PROCEDURE_NAME,'VALID' as STATUS
                    FROM information_schema.ROUTINES
                    WHERE ROUTINE_TYPE = 'PROCEDURE'
                    AND UPPER(ROUTINE_SCHEMA) = UPPER('@schemaname');
                </ListQuery>
                <DefinitionQuery>
                    select
                    case
                    when param_list is not null
                    and param_list != '' then
                    CONCAT(
                    'CREATE OR REPLACE PROCEDURE ', r.ROUTINE_SCHEMA, '.', r.ROUTINE_NAME,
                    ' (',
                    param_list,
                    ') ',
                    r.ROUTINE_DEFINITION, ';\n'
                    )
                    else
                    CONCAT(
                    'CREATE OR REPLACE PROCEDURE ', r.ROUTINE_SCHEMA, '.', r.ROUTINE_NAME,
                    ' ( ) \n',
                    r.ROUTINE_DEFINITION, ';\n'
                    )
                    end as procedure_ddl
                    from
                    information_schema.ROUTINES r
                    left join
                    (
                    select
                    p.SPECIFIC_SCHEMA,
                    p.SPECIFIC_NAME,
                    GROUP_CONCAT(
                    CONCAT(
                    p.PARAMETER_NAME, ' ',
                    p.DATA_TYPE,
                    case
                    when p.DATA_TYPE in ('VARCHAR', 'CHAR', 'TEXT') and p.CHARACTER_MAXIMUM_LENGTH is not null then
                    CONCAT('(', p.CHARACTER_MAXIMUM_LENGTH, ')')
                    else
                    ''
                    end
                    )
                    order by p.ORDINAL_POSITION
                    separator ', '
                    ) as param_list
                    from
                    information_schema.PARAMETERS p
                    where
                    UPPER(p.SPECIFIC_SCHEMA) = UPPER('@schemaname')
                    and UPPER(p.SPECIFIC_NAME) = UPPER('@name')
                    group by
                    p.SPECIFIC_SCHEMA,
                    p.SPECIFIC_NAME) as params
                    on
                    r.ROUTINE_SCHEMA = params.SPECIFIC_SCHEMA
                    and r.ROUTINE_NAME = params.SPECIFIC_NAME
                    where
                    UPPER(r.ROUTINE_SCHEMA) = UPPER('@schemaname')
                    -- Replace with your schema name
                    and UPPER(r.ROUTINE_NAME) = UPPER('@name')
                    -- Replace with your procedure name
                    and r.ROUTINE_TYPE = 'PROCEDURE'
                    order by
                    r.ROUTINE_NAME;
                </DefinitionQuery>

            </Procedure>
            <Function>
                <ListQuery>
                    SELECT ROUTINE_NAME as FUNCTION_NAME,'VALID' as STATUS
                    FROM information_schema.ROUTINES
                    WHERE ROUTINE_TYPE = 'FUNCTION'
                    AND UPPER(ROUTINE_SCHEMA) = UPPER('@schemaname')
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CASE
                        WHEN param_list IS NOT NULL AND param_list != '' THEN
                            CONCAT(
                                'CREATE FUNCTION ', r.ROUTINE_SCHEMA, '.', r.ROUTINE_NAME,
                                ' (', param_list, ') RETURNS ',
                                CONCAT(
                                    r.DATA_TYPE,
                                    CASE
                                        WHEN r.DATA_TYPE IN ('VARCHAR', 'CHAR', 'TEXT') AND r.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN
                                            CONCAT('(', r.CHARACTER_MAXIMUM_LENGTH, ')')
                                        WHEN r.DATA_TYPE IN ('DECIMAL', 'NUMERIC', 'FLOAT') AND r.NUMERIC_PRECISION IS NOT NULL THEN
                                            CONCAT('(', r.NUMERIC_PRECISION,
                                                CASE
                                                    WHEN r.NUMERIC_SCALE IS NOT NULL THEN CONCAT(',', r.NUMERIC_SCALE)
                                                    ELSE ''
                                                END,
                                            ')')
                                        WHEN r.DATA_TYPE = 'DOUBLE' THEN
                                            '' -- No length required for DOUBLE
                                        ELSE
                                            ''
                                    END
                                ),
                                ' DETERMINISTIC\n',
                                r.ROUTINE_DEFINITION, ';\n'
                            )
                        ELSE
                            CONCAT(
                                'CREATE FUNCTION ', r.ROUTINE_SCHEMA, '.', r.ROUTINE_NAME,
                                ' RETURNS ',
                                CONCAT(
                                    r.DATA_TYPE,
                                    CASE
                                        WHEN r.DATA_TYPE IN ('VARCHAR', 'CHAR', 'TEXT') AND r.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN
                                            CONCAT('(', r.CHARACTER_MAXIMUM_LENGTH, ')')
                                        WHEN r.DATA_TYPE IN ('DECIMAL', 'NUMERIC', 'FLOAT') AND r.NUMERIC_PRECISION IS NOT NULL THEN
                                            CONCAT('(', r.NUMERIC_PRECISION,
                                                CASE
                                                    WHEN r.NUMERIC_SCALE IS NOT NULL THEN CONCAT(',', r.NUMERIC_SCALE)
                                                    ELSE ''
                                                END,
                                            ')')
                                        WHEN r.DATA_TYPE = 'DOUBLE' THEN
                                            '' -- No length required for DOUBLE
                                        ELSE
                                            ''
                                    END
                                ),
                                ' DETERMINISTIC\n',
                                r.ROUTINE_DEFINITION, ';\n'
                            )
                    END AS function_ddl
                FROM
                    information_schema.ROUTINES r
                LEFT JOIN
                    (SELECT
                        p.SPECIFIC_SCHEMA, p.SPECIFIC_NAME,
                        GROUP_CONCAT(
                            CONCAT(
                                p.PARAMETER_NAME, ' ',
                                p.DATA_TYPE,
                                CASE
                                    WHEN p.DATA_TYPE IN ('VARCHAR', 'CHAR', 'TEXT') AND p.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN
                                        CONCAT('(', p.CHARACTER_MAXIMUM_LENGTH, ')')
                                    WHEN p.DATA_TYPE IN ('DECIMAL', 'NUMERIC', 'FLOAT') AND p.NUMERIC_PRECISION IS NOT NULL THEN
                                        CONCAT('(', p.NUMERIC_PRECISION,
                                            CASE
                                                WHEN p.NUMERIC_SCALE IS NOT NULL THEN CONCAT(',', p.NUMERIC_SCALE)
                                                ELSE ''
                                            END,
                                        ')')
                                    WHEN p.DATA_TYPE = 'DOUBLE' THEN
                                        '' -- No length required for DOUBLE
                                    ELSE
                                        ''
                                END
                            )
                            ORDER BY p.ORDINAL_POSITION
                            SEPARATOR ', '
                        ) AS param_list
                    FROM
                        information_schema.PARAMETERS p
                    WHERE
                        UPPER(p.SPECIFIC_SCHEMA) = UPPER('@schemaname')
                        AND UPPER(p.SPECIFIC_NAME) = UPPER('@name')
                    GROUP BY
                        p.SPECIFIC_SCHEMA, p.SPECIFIC_NAME) AS params
                ON
                    r.ROUTINE_SCHEMA = params.SPECIFIC_SCHEMA
                    AND r.ROUTINE_NAME = params.SPECIFIC_NAME
                WHERE
                    UPPER(r.ROUTINE_SCHEMA) = UPPER('@schemaname')
                    AND UPPER(r.ROUTINE_NAME) = UPPER('@name')
                    AND r.ROUTINE_TYPE = 'FUNCTION'
                ORDER BY
                    r.ROUTINE_NAME;
                    
                </DefinitionQuery>
            </Function>
            <Trigger>
                <ListQuery>
                    select
                    TRIGGER_NAME as Trigger_name,
                    'VALID' as status
                    from
                    information_schema.TRIGGERS
                    where
                    UPPER(TRIGGER_SCHEMA) = UPPER('@schemaname');
                </ListQuery>
                <DefinitionQuery>
                    select
                    CONCAT(
                    'CREATE TRIGGER ', TRIGGER_SCHEMA, '.', TRIGGER_NAME, ' \n', ACTION_TIMING, ' ', EVENT_MANIPULATION,
                    ' ON ', EVENT_OBJECT_SCHEMA, '.', EVENT_OBJECT_TABLE,
                    '\nFOR EACH ', ACTION_ORIENTATION, ' ',
                    '\n', ACTION_STATEMENT, ';'
                    ) as TRIGGER_DDL
                    from
                    information_schema.TRIGGERS
                    where
                    UPPER(TRIGGER_SCHEMA) = UPPER('@schemaname')
                    and UPPER(TRIGGER_NAME) = UPPER('@name');

                </DefinitionQuery>

            </Trigger>
            <Event>
                <ListQuery>
                    select
                    event_name,
                    'VALID' as Status
                    from
                    information_schema.EVENTS
                    where
                    UPPER(EVENT_SCHEMA) = UPPER('@schemaname');
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'CREATE EVENT ', EVENT_SCHEMA, '.', EVENT_NAME, '\n',
                    'ON SCHEDULE ',
                    CASE
                    WHEN EVENT_TYPE = 'ONE TIME' THEN CONCAT('AT ', EXECUTE_AT)
                    WHEN EVENT_TYPE = 'RECURRING' THEN CONCAT('EVERY ', INTERVAL_VALUE, ' ', INTERVAL_FIELD)
                    END, '\n',
                    'ON COMPLETION ', ON_COMPLETION, '\n',
                    'DO ', EVENT_DEFINITION, ';'
                    ) AS EVENT_DDL
                    FROM
                    information_schema.EVENTS
                    WHERE
                    UPPER(EVENT_SCHEMA) = UPPER('@schemaname')
                    and UPPER(EVENT_NAME) = UPPER('@name');
                </DefinitionQuery>
            </Event>
        </Code_Objects>
        <Storage_Objects>
            <!--            <Type></Type>-->
            <!--            <sequence></sequence>-->
            <Table>
                <ListQuery>
                    select TABLE_NAME,'VALID' as STATUS FROM information_schema.tables where table_type = 'BASE TABLE'
                    and CREATE_OPTIONS != 'partitioned' and UPPER(table_schema)= UPPER('@schemaname');
                </ListQuery>
                <DefinitionQuery>
                    select
                    CONCAT('CREATE TABLE ',
                    c.TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(CONCAT(COLUMN_NAME, ' ', COLUMN_TYPE, if(EXTRA != '', CONCAT(' ', EXTRA), '')) order by ORDINAL_POSITION separator ', '),
                    ');') as create_table_ddl
                    from
                    information_schema.columns c
                    join information_schema.tables t on c.TABLE_SCHEMA = t.TABLE_SCHEMA and c.TABLE_NAME = t.TABLE_NAME
                    where
                    c.TABLE_SCHEMA = '@schemaname'
                    and c.TABLE_NAME = lower('@name')
                    and t.TABLE_TYPE = 'BASE TABLE'
                    and not exists (
                    select
                    1
                    from
                    information_schema.partitions p
                    where
                    TABLE_SCHEMA = '@schemaname'
                    and PARTITION_NAME is not null
                    and p.table_name = t.TABLE_NAME)
                    group by c.TABLE_NAME;
                </DefinitionQuery>
            </Table>
            <Partition>
                <ListQuery>
                    select
                    distinct TABLE_NAME,'VALID' as STATUS
                    from
                    information_schema.partitions p
                    where
                    TABLE_SCHEMA = '@schemaname'
                    and PARTITION_NAME is not null;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'CREATE TABLE ', TABLE_SCHEMA, '.', TABLE_NAME, ' (',
                    GROUP_CONCAT(
                    CONCAT(
                    COLUMN_NAME, ' ', COLUMN_TYPE,
                    IF(IS_NULLABLE = 'NO', ' NOT NULL', ''),
                    IF(COLUMN_DEFAULT IS NOT NULL, CONCAT(' DEFAULT ', COLUMN_DEFAULT), ''),
                    IF(EXTRA != '', CONCAT(' ', EXTRA), '')
                    ) ORDER BY ORDINAL_POSITION SEPARATOR ', '
                    ),
                    ') ',
                    IFNULL((SELECT CONCAT(
                    'PARTITION BY ', PARTITION_METHOD, '(', PARTITION_EXPRESSION, ') (',
                    GROUP_CONCAT(
                    CONCAT(
                    'PARTITION ', PARTITION_NAME,
                    ' VALUES ',
                    CASE
                    WHEN PARTITION_METHOD = 'RANGE' THEN CONCAT('LESS THAN (', PARTITION_DESCRIPTION, ')')
                    WHEN PARTITION_METHOD = 'LIST' THEN CONCAT('IN (', PARTITION_DESCRIPTION, ')')
                    ELSE ''
                    END
                    ) SEPARATOR ', '
                    ), ')'
                    ) FROM information_schema.PARTITIONS
                    WHERE TABLE_SCHEMA = COLUMNS.TABLE_SCHEMA
                    AND TABLE_NAME = COLUMNS.TABLE_NAME
                    AND PARTITION_NAME IS NOT NULL
                    GROUP BY TABLE_SCHEMA, TABLE_NAME, PARTITION_METHOD, PARTITION_EXPRESSION), '') , ';'
                    ) AS Create_table_Partition_DDL
                    FROM
                    information_schema.COLUMNS COLUMNS
                    WHERE
                    TABLE_SCHEMA = '@schemaname' AND TABLE_NAME = '@name'
                    GROUP BY
                    TABLE_SCHEMA, TABLE_NAME;
                </DefinitionQuery>
            </Partition>
            <Not_Null_Constraint>
                <ListQuery>
                     select
                    concat(c.TABLE_NAME, '-', c.COLUMN_NAME),
                    'VALID' as STATUS
                    from
                    information_schema.columns c
                    inner join information_schema.tables t on
                    c.TABLE_NAME = t.TABLE_NAME
                    and c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    where
                    UPPER(c.TABLE_SCHEMA) = UPPER('@schemaname')
                    and t.TABLE_TYPE = 'BASE TABLE'
                    and c.IS_NULLABLE = 'NO'
                    order by
                    c.TABLE_NAME,
                    c.COLUMN_NAME;
                </ListQuery>
                <DefinitionQuery>
                    select
                    concat('ALTER TABLE ', TABLE_SCHEMA, '.', TABLE_NAME, ' modify COLUMN ', COLUMN_NAME, ' ',
                    COLUMN_TYPE, ' NOT NULL;') as NotNull
                    from
                    information_schema.COLUMNS
                    where
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    and upper(concat(TABLE_NAME,'-',COLUMN_NAME)) = upper('@name')
                    order by
                    TABLE_NAME,
                    COLUMN_NAME;
                </DefinitionQuery>

            </Not_Null_Constraint>
            <Primary_Key>
                <ListQuery>
                    select
                    concat(TABLE_NAME,'-',CONSTRAINT_NAME),'VALID' as STATUS
                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    AND CONSTRAINT_NAME = 'PRIMARY'
                    GROUP BY
                    TABLE_NAME
                </ListQuery>
                <DefinitionQuery>
                    select
                    CONCAT(
                    'ALTER TABLE ',
                    TABLE_SCHEMA ,
                    '.',
                    TABLE_NAME,
                    ' ADD CONSTRAINT PRIMARY KEY (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION SEPARATOR ', '),
                    ');'
                    ) AS PrimaryKey
                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    upper(concat(TABLE_NAME,'-',CONSTRAINT_NAME)) = upper('@name')
                    AND CONSTRAINT_NAME = 'PRIMARY'
                    and UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    GROUP BY
                    TABLE_NAME
                </DefinitionQuery>

            </Primary_Key>
            <Unique_Constraint>
                <ListQuery>
                    select
                    concat(TC.TABLE_NAME,'-',TC.CONSTRAINT_NAME),'VALID' as STATUS
                    FROM
                    information_schema.TABLE_CONSTRAINTS TC
                    JOIN
                    information_schema.KEY_COLUMN_USAGE KCU
                    ON TC.CONSTRAINT_NAME = KCU.CONSTRAINT_NAME
                    AND TC.TABLE_NAME = KCU.TABLE_NAME
                    AND TC.TABLE_SCHEMA = KCU.TABLE_SCHEMA
                    WHERE
                    TC.CONSTRAINT_TYPE = 'UNIQUE'
                    AND UPPER(TC.TABLE_SCHEMA) = UPPER('@schemaname')
                    GROUP BY
                    TC.TABLE_SCHEMA, TC.TABLE_NAME, TC.CONSTRAINT_NAME
                    ORDER BY
                    TC.TABLE_NAME, TC.CONSTRAINT_NAME;

                </ListQuery>
                <DefinitionQuery>
                    select
                    CONCAT(
                    'ALTER TABLE ',
                    TC.TABLE_SCHEMA,'.',
                    TC.TABLE_NAME,
                    ' ADD CONSTRAINT ',
                    TC.CONSTRAINT_NAME,
                    ' UNIQUE (',
                    GROUP_CONCAT(KCU.COLUMN_NAME ORDER BY KCU.ORDINAL_POSITION),
                    ');'
                    ) AS ddl_statement
                    FROM
                    information_schema.TABLE_CONSTRAINTS TC
                    JOIN
                    information_schema.KEY_COLUMN_USAGE KCU
                    ON TC.CONSTRAINT_NAME = KCU.CONSTRAINT_NAME
                    AND TC.TABLE_NAME = KCU.TABLE_NAME
                    AND TC.TABLE_SCHEMA = KCU.TABLE_SCHEMA
                    WHERE
                    upper(concat(TC.TABLE_NAME,'-',TC.CONSTRAINT_NAME)) = upper('@name')
                    AND UPPER(TC.TABLE_SCHEMA) = UPPER('@schemaname')
                    GROUP BY
                    TC.TABLE_SCHEMA, TC.TABLE_NAME, TC.CONSTRAINT_NAME
                    ORDER BY
                    TC.TABLE_NAME, TC.CONSTRAINT_NAME;
                </DefinitionQuery>
            </Unique_Constraint>
            <Foreign_Key>
                <ListQuery>
                    SELECT
                    concat(TABLE_NAME,'-',CONSTRAINT_NAME),'VALID' as STATUS
                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    and REFERENCED_TABLE_NAME IS NOT NULL
                    GROUP BY
                    TABLE_NAME, CONSTRAINT_NAME;
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'ALTER TABLE ',
                    TABLE_SCHEMA ,
                    '.',
                    TABLE_NAME,
                    ' ADD CONSTRAINT ',
                    CONSTRAINT_NAME,
                    ' FOREIGN KEY (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION SEPARATOR ', '),
                    ') REFERENCES ',
                    REFERENCED_TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(REFERENCED_COLUMN_NAME ORDER BY POSITION_IN_UNIQUE_CONSTRAINT SEPARATOR ', '),
                    ');'
                    ) AS ForeignKey
                    FROM
                    information_schema.KEY_COLUMN_USAGE
                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    and upper(concat(TABLE_NAME,'-',CONSTRAINT_NAME)) = upper('@name')
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                    GROUP BY
                    TABLE_NAME, CONSTRAINT_NAME
                </DefinitionQuery>

            </Foreign_Key>
            <Default_Constraint>
                <ListQuery>
                    select
                    concat(c.TABLE_NAME, '-', c.COLUMN_NAME),
                    'VALID' as STATUS
                    from
                    information_schema.columns c
                    join information_schema.tables t on
                    c.TABLE_NAME = t.TABLE_NAME
                    and c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    where
                    UPPER(c.TABLE_SCHEMA) = UPPER('@schemaname')
                    and t.TABLE_TYPE = 'BASE TABLE'
                    AND c.IS_NULLABLE = 'YES'
                    order by
                    c.TABLE_NAME,
                    c.COLUMN_NAME;
                </ListQuery>
                <DefinitionQuery>
                    select
                    concat('ALTER TABLE ', TABLE_SCHEMA, '.', TABLE_NAME, ' modify COLUMN ', COLUMN_NAME, ' ',
                    COLUMN_TYPE, ' DEFAULT ', COLUMN_DEFAULT, ';') as DefaultConstraints
                    from
                    information_schema.COLUMNS
                    where
                    upper(concat(TABLE_NAME,'-',COLUMN_NAME)) = upper('@name')
                    and upper(TABLE_SCHEMA) = upper('@schemaname')
                    order by
                    TABLE_NAME,
                    COLUMN_NAME;
                </DefinitionQuery>
            </Default_Constraint>
            <Check_Constraint>
                <ListQuery>
                    select
                    concat(TABLE_NAME,'-',CONSTRAINT_NAME),'VALID' as STATUS
                    from
                    information_schema.CHECK_CONSTRAINTS
                    where
                    UPPER(CONSTRAINT_SCHEMA) = UPPER('@schemaname')
                    order by
                    CONSTRAINT_SCHEMA,
                    TABLE_NAME
                </ListQuery>
                <DefinitionQuery>
                    select
                    concat('ALTER TABLE ', CONSTRAINT_SCHEMA, '.', TABLE_NAME, ' add constraint ', CONSTRAINT_NAME, '
                    check (', CHECK_CLAUSE, ');') as CheckConstraints
                    from
                    information_schema.CHECK_CONSTRAINTS
                    where
                    upper(concat(TABLE_NAME,'-',CONSTRAINT_NAME))=upper('@name')
                    and upper(CONSTRAINT_SCHEMA) = upper('@schemaname')

                    order by
                    CONSTRAINT_SCHEMA,
                    TABLE_NAME;
                </DefinitionQuery>
            </Check_Constraint>
            <Index>
                <ListQuery>
                    select
                    concat(TABLE_NAME,'-',INDEX_NAME),'VALID' as STATUS
                    FROM
                    information_schema.STATISTICS
                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname')
                    and
                    INDEX_NAME != 'PRIMARY' 
                    GROUP BY
                    INDEX_NAME, TABLE_NAME, NON_UNIQUE, INDEX_TYPE;

                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'CREATE ',
                    IF(NON_UNIQUE = 0, 'UNIQUE ', ''),
                    'INDEX ',
                    INDEX_NAME,
                    ' ON ',
                    TABLE_SCHEMA,
                    '.',
                    TABLE_NAME,
                    ' (',
                    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX SEPARATOR ', '),
                    ');'
                    ) AS IndexDef
                    FROM
                    information_schema.STATISTICS
                    WHERE
                    upper(concat(TABLE_NAME,'-',INDEX_NAME)) = upper('@name') and
                    upper(TABLE_SCHEMA) = upper('@schemaname')
                    GROUP BY
                    INDEX_NAME, TABLE_NAME, NON_UNIQUE, INDEX_TYPE;

                </DefinitionQuery>
            </Index>
            <View>
                <ListQuery>
                    select TABLE_NAME AS VIEW_NAME,'VALID' as STATUS FROM information_schema.tables where table_type =
                    'VIEW' and UPPER(table_schema)= UPPER('@schemaname');
                </ListQuery>
                <DefinitionQuery>
                    SELECT
                    CONCAT(
                    'CREATE VIEW ',
                    REPLACE(REPLACE(TABLE_SCHEMA, '`', ''), '`', ''),
                    '.',
                    REPLACE(REPLACE(TABLE_NAME, '`', ''), '`', ''),
                    ' AS ',
                    REPLACE(VIEW_DEFINITION, '`', ''),
                    ';'
                    ) AS view_DDL
                    FROM
                    INFORMATION_SCHEMA.VIEWS

                    WHERE
                    UPPER(TABLE_SCHEMA) = UPPER('@schemaname') and UPPER(table_name )= UPPER('@name')

                </DefinitionQuery>
            </View>
        </Storage_Objects>
    </Extraction_Queries>
    <Database_Exclusion_Query>
        SELECT table_name AS object_name
        FROM information_schema.tables
        WHERE table_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')
        AND table_type = 'BASE TABLE'

        UNION ALL

        SELECT column_name AS object_name
        FROM information_schema.columns
        WHERE table_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT routine_name AS object_name
        FROM information_schema.routines
        WHERE routine_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT index_name AS object_name
        FROM information_schema.statistics
        WHERE table_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')
        AND index_name NOT LIKE 'PRIMARY'
        AND index_name NOT LIKE 'idx%'
        AND index_name NOT LIKE 'sys%'

        UNION ALL

        SELECT parameter_name AS object_name
        FROM information_schema.parameters
        WHERE specific_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT trigger_name AS object_name
        FROM information_schema.triggers
        WHERE trigger_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT partition_name AS object_name
        FROM information_schema.partitions
        WHERE partition_name IS NOT NULL
        AND table_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')

        UNION ALL

        SELECT event_name AS object_name
        FROM information_schema.events
        WHERE event_schema NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys');

    </Database_Exclusion_Query>
    <Source_Schemas>
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys');
    </Source_Schemas>
    <Target_Schemas>
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys');
    </Target_Schemas>
    <DB_Size>
        select
        table_schema as `Database`,
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as `Size (MB)`
        from
            information_schema.tables
        where
            table_schema = '@db_name'
        group by
            table_schema;
    </DB_Size>
</Queries>